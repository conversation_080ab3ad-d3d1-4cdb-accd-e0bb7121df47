import SwiftUI

/// 手机号输入组件
/// 
/// 职责：
/// - 提供手机号输入界面
/// - 实时验证手机号格式
/// - 显示验证状态和错误提示
struct PhoneInputView: View {
    
    // MARK: - Properties
    
    @Binding var phoneNumber: String
    let isValid: Bool
    
    @FocusState private var isFocused: Bool
    
    // MARK: - Body
    
    var body: some View {
        fieldsetView
    }
    
    // MARK: - Private Views
    
    /// 根据状态返回对应的 Fieldset 视图
    private var fieldsetView: some View {
        // 错误状态优先：红色边框，无渐变
        let isErrorState = !phoneNumber.isEmpty && !isValid

        return FieldsetInputView(
            borderColor: isErrorState ? .red : currentBorderColor,
            borderWidth: currentBorderWidth,
            labelText: currentLabelText,
            labelColor: currentLabelColor,
            borderGradient: isErrorState ? nil : currentBorderGradient
        ) {
            inputContent
        }
    }
    
    /// 输入框内容
    private var inputContent: some View {
        HStack(spacing: 16) {
            // 手机图标
            Image(systemName: AuthenticationConstants.Icons.phone)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(iconColor)
                .frame(width: 24, height: 24)

            // 输入框
            TextField(
                AuthenticationConstants.Text.phoneNumberPlaceholder,
                text: $phoneNumber
            )
            .font(.system(size: 16, weight: .medium))
            .keyboardType(.phonePad)
            .focused($isFocused)
            .onChange(of: phoneNumber) { _, newValue in
                let filtered = newValue.filter { $0.isNumber }
                let limitedValue = filtered.count <= 11 ? filtered : String(filtered.prefix(11))

                if limitedValue != phoneNumber {
                    phoneNumber = limitedValue
                }
            }

            // 验证状态图标/清空按钮
            if !phoneNumber.isEmpty {
                if isValid {
                    // 有效状态显示成功图标
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.green)
                } else {
                    // 错误状态显示清空按钮
                    Button(action: {
                        phoneNumber = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(.gray.opacity(0.6))
                    }
                    .buttonStyle(PlainButtonStyle())
                    .scaleEffect(isFocused ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: isFocused)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .contentShape(Rectangle())
        .onTapGesture {
            isFocused = true
        }
    }
    
    // MARK: - Computed Properties
    
    /// 当前边框颜色
    private var currentBorderColor: Color {
        if !phoneNumber.isEmpty && !isValid {
            // 错误状态 - 红色边框
            return .red
        } else if isFocused || (!phoneNumber.isEmpty && isValid) {
            // 聚焦状态或验证通过状态 - 使用渐变，返回透明色
            return .clear
        } else {
            // 普通状态 - 灰色边框
            return Color(.systemGray4)
        }
    }
    
    /// 当前边框宽度
    private var currentBorderWidth: CGFloat {
        if !phoneNumber.isEmpty && !isValid {
            return 2
        } else if isFocused {
            return 2
        } else if !phoneNumber.isEmpty && isValid {
            return 2
        } else {
            return 1
        }
    }
    
    /// 当前标签文本
    private var currentLabelText: String? {
        if !phoneNumber.isEmpty && !isValid {
            // 错误状态显示错误提示
            return AuthenticationConstants.Text.phoneNumberError
        } else {
            // 其他状态不显示标签
            return nil
        }
    }
    
    /// 当前标签颜色
    private var currentLabelColor: Color {
        if !phoneNumber.isEmpty && !isValid {
            // 错误状态
            return .red
        } else if isFocused {
            // 聚焦状态
            return .blue
        } else if !phoneNumber.isEmpty && isValid {
            // 成功状态
            return .green
        } else {
            // 普通状态
            return .secondary
        }
    }

    /// 当前边框渐变
    private var currentBorderGradient: LinearGradient? {
        // 聚焦状态或验证通过状态 - 都使用蓝紫渐变
        if isFocused || (!phoneNumber.isEmpty && isValid) {
            return LinearGradient(
                gradient: Gradient(colors: [.blue, .purple]),
                startPoint: .leading,
                endPoint: .trailing
            )
        } else {
            // 其他状态不使用渐变
            return nil
        }
    }

    private var iconColor: Color {
        if !phoneNumber.isEmpty && !isValid {
            // 错误状态 - 红色
            return .red
        } else if isFocused || (!phoneNumber.isEmpty && isValid) {
            // 聚焦状态或验证通过状态 - 蓝色（与渐变保持一致）
            return .blue
        } else {
            // 默认状态 - 灰色
            return .gray
        }
    }
}

#Preview("手机号输入组件") {
    VStack(spacing: 20) {
        PhoneInputView(phoneNumber: .constant(""), isValid: false)
        PhoneInputView(phoneNumber: .constant("138"), isValid: false)
        PhoneInputView(phoneNumber: .constant("13812345678"), isValid: true)
        PhoneInputView(phoneNumber: .constant("123"), isValid: false)
    }
    .padding()
    .appBackground()
}
