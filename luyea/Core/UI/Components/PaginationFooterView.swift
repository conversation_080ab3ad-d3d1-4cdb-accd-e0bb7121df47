import SwiftUI

/// 统一的分页底部视图组件
///
/// 提供一致的分页加载状态显示，包括加载中和无更多数据状态。
/// 可在整个应用中复用，确保一致的用户体验。
struct PaginationFooterView: View {
    
    // MARK: - Properties
    
    /// 是否正在加载更多
    let isLoadingMore: Bool
    
    /// 是否还有更多数据
    let hasMoreData: Bool
    
    /// 是否有数据（用于判断是否显示"没有更多"）
    let hasData: Bool
    
    /// 加载文本，默认为"加载中..."
    let loadingText: String
    
    /// 无更多数据文本，默认为"没有更多内容了"
    let noMoreDataText: String
    
    // MARK: - Initialization
    
    /// 初始化分页底部视图
    /// - Parameters:
    ///   - isLoadingMore: 是否正在加载更多
    ///   - hasMoreData: 是否还有更多数据
    ///   - hasData: 是否有数据
    ///   - loadingText: 加载文本
    ///   - noMoreDataText: 无更多数据文本
    init(
        isLoadingMore: Bool,
        hasMoreData: Bool,
        hasData: Bool,
        loadingText: String = "加载中...",
        noMoreDataText: String = "没有更多内容了"
    ) {
        self.isLoadingMore = isLoadingMore
        self.hasMoreData = hasMoreData
        self.hasData = hasData
        self.loadingText = loadingText
        self.noMoreDataText = noMoreDataText
    }
    
    // MARK: - Body
    
    var body: some View {
        Group {
            if isLoadingMore && hasData {
                loadingMoreView
            } else if !hasMoreData && hasData {
                noMoreDataView
            }
        }
    }
    
    // MARK: - Private Views
    
    /// 加载更多视图
    private var loadingMoreView: some View {
        HStack(spacing: 8) {
            ProgressView()
                .scaleEffect(0.8)
            
            Text(loadingText)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 16)
    }
    
    /// 无更多数据视图
    private var noMoreDataView: some View {
        Text(noMoreDataText)
            .font(.system(size: 14))
            .foregroundColor(.secondary)
            .padding(.vertical, 16)
            .padding(.bottom, 10)
    }
}

// MARK: - Convenience Extensions

extension PaginationFooterView {
    /// 创建标准的分页底部视图
    /// - Parameters:
    ///   - isLoadingMore: 是否正在加载更多
    ///   - hasMoreData: 是否还有更多数据
    ///   - hasData: 是否有数据
    /// - Returns: 分页底部视图
    static func standard(
        isLoadingMore: Bool,
        hasMoreData: Bool,
        hasData: Bool
    ) -> PaginationFooterView {
        PaginationFooterView(
            isLoadingMore: isLoadingMore,
            hasMoreData: hasMoreData,
            hasData: hasData
        )
    }
}

// MARK: - Preview

#Preview("分页底部视图") {
    VStack(spacing: 20) {
        // 加载更多状态
        PaginationFooterView.standard(
            isLoadingMore: true,
            hasMoreData: true,
            hasData: true
        )
        .background(Color.gray.opacity(0.1))
        
        // 无更多数据状态
        PaginationFooterView.standard(
            isLoadingMore: false,
            hasMoreData: false,
            hasData: true
        )
        .background(Color.gray.opacity(0.1))
        
        // 无数据状态（不显示）
        PaginationFooterView.standard(
            isLoadingMore: false,
            hasMoreData: true,
            hasData: false
        )
        .background(Color.gray.opacity(0.1))
    }
    .padding()
}
