import Foundation
import SwiftUI

final class FeaturedDestinationService: ObservableObject {
    static let shared = FeaturedDestinationService()
    @Published private(set) var featuredDestinations: [Destination] = []
    private var hasLoaded = false
    private init() {}

    func loadFeaturedDestinationsIfNeeded() async {
        guard !hasLoaded else { return }
        do {
            let data: [Destination] = try await NetworkService.shared.request(.get("/destination/featured"))
            await MainActor.run {
                self.featuredDestinations = data
                self.hasLoaded = true
            }
        } catch {
            Log.error("❌ 精选目的地数据加载失败: \(error.localizedDescription)")
        }
    }
} 