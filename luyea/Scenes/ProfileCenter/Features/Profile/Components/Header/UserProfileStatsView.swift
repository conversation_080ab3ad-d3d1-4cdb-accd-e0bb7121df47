import SwiftUI

struct UserProfileStatsView: View {
    // MARK: - 属性
    private let stats = ProfileModels.UserStats(
        recommendations: 1200,
        likes: 3500
    )
    
    // MARK: - 视图
    var body: some View {
        VStack(spacing: 12) {
            HStack(spacing: 0) {
                statItem(title: "获得推荐", value: formatNumber(stats.recommendations))
                
                Divider()
                    .frame(height: 20)
                
                statItem(title: "获得喜欢", value: formatNumber(stats.likes))
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
        .padding(.horizontal, DesignSystemConstants.Spacing.standard)
    }
    
    // MARK: - 子视图
    private func statItem(title: String, value: String) -> some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Text(value)
                .font(.headline)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 辅助方法
    private func formatNumber(_ number: Int) -> String {
        if number >= 1000 {
            let k = Double(number) / 1000.0
            return String(format: "%.1fk", k)
        }
        return "\(number)"
    }
}

#Preview {
    UserProfileStatsView()
} 