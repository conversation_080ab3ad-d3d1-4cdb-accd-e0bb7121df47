import SwiftUI

/// 验证码输入组件
/// 
/// 职责：
/// - 提供验证码输入界面
/// - 管理发送验证码按钮状态
/// - 显示倒计时和重发功能
/// - 验证码格式验证
struct VerificationCodeInputView: View {
    
    // MARK: - Properties
    
    @Binding var verificationCode: String
    let codeSendState: AuthenticationModels.CodeSendState
    let sendCodeButtonTitle: String
    let canSendCode: Bool
    let isValid: Bool
    let onSendCode: () -> Void
    
    @FocusState private var isFocused: Bool
    
    // MARK: - Body
    
    var body: some View {
        fieldsetView
    }
    
    // MARK: - Private Views
    
    /// 根据状态返回对应的 Fieldset 视图
    private var fieldsetView: some View {
        // 错误状态优先：红色边框，无渐变
        let isErrorState = !verificationCode.isEmpty && !isValid

        return FieldsetInputView(
            borderColor: isErrorState ? .red : currentBorderColor,
            borderWidth: currentBorderWidth,
            labelText: currentLabelText,
            labelColor: currentLabelColor,
            borderGradient: isErrorState ? nil : currentBorderGradient
        ) {
            inputContent
        }
    }
    
    /// 输入框内容
    private var inputContent: some View {
        HStack(spacing: 16) {
            // 锁图标
            Image(systemName: AuthenticationConstants.Icons.lock)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(iconColor)
                .frame(width: 24, height: 24)

            // 输入框
            TextField(
                AuthenticationConstants.Text.verificationCodePlaceholder,
                text: $verificationCode
            )
            .font(.system(size: 16, weight: .medium))
            .keyboardType(.numberPad)
            .focused($isFocused)
            .onChange(of: verificationCode) { _, newValue in
                let filtered = newValue.filter { $0.isNumber }
                let limitedValue = filtered.count <= AuthenticationConstants.Validation.verificationCodeLength ?
                    filtered : String(filtered.prefix(AuthenticationConstants.Validation.verificationCodeLength))

                if limitedValue != verificationCode {
                    verificationCode = limitedValue
                }
            }

            // 验证状态图标/清空按钮
            if !verificationCode.isEmpty {
                if isValid {
                    // 有效状态显示成功图标
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.green)
                } else {
                    // 错误状态显示清空按钮
                    Button(action: {
                        verificationCode = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(.gray.opacity(0.6))
                    }
                    .buttonStyle(PlainButtonStyle())
                    .scaleEffect(isFocused ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: isFocused)
                }
            }

            // 发送验证码按钮
            Button(action: onSendCode) {
                Text(sendCodeButtonTitle)
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(sendCodeButtonTextColor)
                    .frame(minWidth: 80) // 设置最小宽度，确保按钮尺寸稳定
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(sendCodeButtonBackground)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(sendCodeButtonBorderColor, lineWidth: 1)
                            )
                    )
            }
            .disabled(!canSendCode)
            .opacity(canSendCode ? 1.0 : 0.6)
            .animation(.easeInOut(duration: 0.2), value: canSendCode)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .contentShape(Rectangle())
        .onTapGesture {
            isFocused = true
        }
    }
    
    // MARK: - Computed Properties

    /// 当前边框颜色
    private var currentBorderColor: Color {
        // 聚焦状态或验证通过状态 - 使用渐变，返回透明色让渐变生效
        if isFocused || (!verificationCode.isEmpty && isValid) {
            return .clear
        }
        // 所有其他状态 - 灰色边框
        else {
            return Color(.systemGray4)
        }
    }
    
    /// 当前边框宽度
    private var currentBorderWidth: CGFloat {
        if !verificationCode.isEmpty && !isValid {
            return 2
        } else if currentLabelText != nil {
            return 2
        } else if isFocused {
            return 2
        } else if !verificationCode.isEmpty && isValid {
            return 2
        } else {
            return 1
        }
    }
    
    /// 当前标签文本
    private var currentLabelText: String? {
        if !verificationCode.isEmpty && !isValid {
            // 错误状态优先显示
            return AuthenticationConstants.Text.verificationCodeError
        } else {
            // 显示发送状态
            switch codeSendState {
            case .idle:
                return nil
            case .sending:
                return "发送中..."
            case .sent:
                return "已发送"
            case .failed:
                return "发送失败，请稍后重试" // 发送失败时提示重试
            case .cooldown:
                return "已发送" // 倒计时期间继续显示"已发送"
            case .canResend:
                return nil // 可重新发送状态不显示额外提示
            }
        }
    }
    
    /// 当前标签颜色
    private var currentLabelColor: Color {
        if !verificationCode.isEmpty && !isValid {
            // 输入错误状态 - 红色
            return .red
        } else if case .sent = codeSendState, currentLabelText == "已发送" {
            // 发送成功状态的"已发送"标签 - 绿色（成功标记）
            return .green
        } else if case .cooldown = codeSendState, currentLabelText == "已发送" {
            // 倒计时期间的"已发送"标签 - 绿色（成功标记）
            return .green
        } else if case .failed = codeSendState {
            // 发送失败状态 - 红色
            return .red
        } else if isFocused {
            // 聚焦状态 - 蓝色
            return .blue
        } else {
            // 默认状态 - 次要颜色
            return .secondary
        }
    }
    




    /// 当前边框渐变
    private var currentBorderGradient: LinearGradient? {
        // 聚焦状态或验证通过状态 - 都使用蓝紫渐变
        if isFocused || (!verificationCode.isEmpty && isValid) {
            return LinearGradient(
                gradient: Gradient(colors: [.blue, .purple]),
                startPoint: .leading,
                endPoint: .trailing
            )
        }
        // 其他状态不使用渐变
        else {
            return nil
        }
    }

    private var iconColor: Color {
        if !verificationCode.isEmpty && !isValid {
            // 错误状态 - 红色
            return .red
        } else if isFocused || (!verificationCode.isEmpty && isValid) {
            // 聚焦状态或验证通过状态 - 蓝色（与渐变保持一致）
            return .blue
        } else {
            // 默认状态 - 灰色
            return .gray
        }
    }

    private var sendCodeButtonBackground: Color {
        canSendCode ? .blue : Color(.tertiarySystemBackground)
    }

    private var sendCodeButtonTextColor: Color {
        canSendCode ? .white : .gray
    }

    private var sendCodeButtonBorderColor: Color {
        canSendCode ? .clear : .gray.opacity(0.3)
    }
}

#Preview("验证码输入组件") {
    VStack(spacing: 16) {
        VerificationCodeInputView(
            verificationCode: .constant(""),
            codeSendState: .idle,
            sendCodeButtonTitle: "获取验证码",
            canSendCode: true,
            isValid: false,
            onSendCode: {}
        )

        VerificationCodeInputView(
            verificationCode: .constant("123456"),
            codeSendState: .idle,
            sendCodeButtonTitle: "获取验证码",
            canSendCode: true,
            isValid: true,
            onSendCode: {}
        )

        VerificationCodeInputView(
            verificationCode: .constant("123"),
            codeSendState: .idle,
            sendCodeButtonTitle: "获取验证码",
            canSendCode: true,
            isValid: false,
            onSendCode: {}
        )

        VerificationCodeInputView(
            verificationCode: .constant(""),
            codeSendState: .sent(expiresAt: Date().addingTimeInterval(300)),
            sendCodeButtonTitle: "重新发送",
            canSendCode: true,
            isValid: false,
            onSendCode: {}
        )
    }
    .padding()
    .appBackground()
}


