import SwiftUI
import Foundation



/// 图片缓存服务协议，便于依赖注入和单元测试
protocol ImageCacheServiceProtocol {
    func getImage(for url: URL) async -> UIImage?
    func getSwiftUIImage(for url: URL) async -> Image?
    func clearCache()
    func clearMemoryCache()
    func getCacheSize() -> Int64
}

/// 图片缓存服务实现类
///
/// 提供高效的图片缓存功能，支持内存缓存和磁盘缓存。
/// 采用LRU策略管理缓存，自动处理内存警告和缓存清理。
/// 支持UIImage和SwiftUI Image两种格式的缓存。
///
/// 并发安全：使用 @unchecked Sendable 确保线程安全，内部使用队列同步访问
final class ImageCacheService: ImageCacheServiceProtocol, @unchecked Sendable {
    /// 单例实例
    static let shared = ImageCacheService()

    /// 内存缓存（UIImage）
    private let uiImageCache = NSCache<NSString, UIImage>()

    /// 内存缓存（SwiftUI Image）
    private let swiftUIImageCache = NSCache<NSString, ImageWrapper>()

    /// 文件管理器
    private let fileManager = FileManager.default

    /// 缓存操作队列
    private let cacheQueue = DispatchQueue(label: "com.luyea.imagecache", qos: .userInitiated)

    /// 网络请求队列
    private let networkQueue = DispatchQueue(label: "com.luyea.imagenetwork", qos: .userInitiated)

    /// 磁盘缓存目录
    private let cacheDirectory: URL

    /// URL会话
    private let urlSession: URLSession

    /// 正在进行的下载任务管理器（线程安全）
    private let downloadTaskManager = DownloadTaskManager()

    /// 并发控制 Actor（限制同时下载的图片数量）
    private let downloadLimiter = DownloadLimiter(maxConcurrentDownloads: 6)

    /// 私有初始化方法，确保单例模式
    private init() {
        // 配置内存缓存限制
        uiImageCache.countLimit = AppConfig.Cache.maxImageCount
        uiImageCache.totalCostLimit = AppConfig.Cache.maxSize * 1024 * 1024 // 转换为字节

        swiftUIImageCache.countLimit = AppConfig.Cache.maxImageCount
        swiftUIImageCache.totalCostLimit = AppConfig.Cache.maxSize * 1024 * 1024

        // 创建磁盘缓存目录
        let cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)[0]
        cacheDirectory = cachesDirectory.appendingPathComponent(FileConstants.imageCacheDirectoryName)
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)

        // 配置URL会话 - 专门为图片下载优化
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 15  // 图片请求超时15秒
        config.timeoutIntervalForResource = 30  // 资源下载超时30秒
        config.requestCachePolicy = .returnCacheDataElseLoad
        config.waitsForConnectivity = false  // 不等待网络连接
        urlSession = URLSession(configuration: config)

        // 监听内存警告
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )

        // 监听应用进入后台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        Log.debug("🖼️ 图片缓存服务初始化完成")
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Public Methods

    /// 获取UIImage格式的图片
    ///
    /// 按照内存缓存 -> 磁盘缓存 -> 网络下载的顺序获取图片。
    /// 自动处理缓存存储和LRU策略。
    ///
    /// - Parameter url: 图片URL
    /// - Returns: UIImage对象，如果获取失败则返回nil
    func getImage(for url: URL) async -> UIImage? {
        // 1. 检查内存缓存
        if let cachedImage = getUIImageFromMemory(for: url) {
            // 内存缓存命中，不记录日志（减少日志噪音）
            return cachedImage
        }

        // 2. 检查磁盘缓存
        if let diskImage = await getUIImageFromDisk(for: url) {
            // 磁盘缓存命中，不记录日志（减少日志噪音）
            // 存入内存缓存
            setUIImageToMemory(diskImage, for: url)
            return diskImage
        }

        // 3. 网络下载
        return await downloadUIImage(from: url)
    }

    /// 获取SwiftUI Image格式的图片
    ///
    /// 专为SwiftUI优化的图片获取方法，返回Image对象。
    ///
    /// - Parameter url: 图片URL
    /// - Returns: SwiftUI Image对象，如果获取失败则返回nil
    func getSwiftUIImage(for url: URL) async -> Image? {
        // 1. 检查SwiftUI图片缓存
        if let cachedImage = getSwiftUIImageFromMemory(for: url) {
            Log.debug("🎯 [ImageCache] SwiftUI内存缓存命中: \(url.lastPathComponent)")
            return cachedImage
        }

        // 2. 尝试从UIImage缓存获取并转换
        if let uiImage = await getImage(for: url) {
            let swiftUIImage = Image(uiImage: uiImage)
            setSwiftUIImageToMemory(swiftUIImage, for: url)
            return swiftUIImage
        }

        return nil
    }

    /// 仅从内存缓存获取SwiftUI Image（用于快速检查）
    ///
    /// - Parameter url: 图片URL
    /// - Returns: SwiftUI Image对象，如果内存中没有则返回nil
    func getSwiftUIImageFromMemoryOnly(for url: URL) -> Image? {
        return getSwiftUIImageFromMemory(for: url)
    }

    /// 从内存缓存中移除特定URL的图片
    func removeFromMemoryCache(for url: URL) {
        let key = cacheFileName(for: url) as NSString
        uiImageCache.removeObject(forKey: key)
        swiftUIImageCache.removeObject(forKey: key)
    }

    /// 清除内存缓存（保留磁盘缓存）
    func clearMemoryCache() {
        uiImageCache.removeAllObjects()
        swiftUIImageCache.removeAllObjects()
        Log.info("🧹 [ImageCache] 内存缓存已清除")
    }

    /// 清除所有缓存（内存+磁盘）
    func clearCache() {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }

            // 清除内存缓存
            self.uiImageCache.removeAllObjects()
            self.swiftUIImageCache.removeAllObjects()

            // 清除磁盘缓存
            try? self.fileManager.removeItem(at: self.cacheDirectory)
            try? self.fileManager.createDirectory(at: self.cacheDirectory, withIntermediateDirectories: true)

            Log.info("🗑️ [ImageCache] 所有缓存已清除")
        }
    }

    /// 获取缓存大小（字节）
    ///
    /// - Returns: 磁盘缓存占用的字节数
    func getCacheSize() -> Int64 {
        var totalSize: Int64 = 0

        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            for file in files {
                let attributes = try fileManager.attributesOfItem(atPath: file.path)
                if let fileSize = attributes[.size] as? Int64 {
                    totalSize += fileSize
                }
            }
        } catch {
            Log.error("❌ [ImageCache] 计算缓存大小失败: \(error)")
        }

        return totalSize
    }

    // MARK: - Private Methods - Memory Cache

    /// 从内存缓存获取UIImage
    func getUIImageFromMemory(for url: URL) -> UIImage? {
        let key = url.absoluteString as NSString
        return uiImageCache.object(forKey: key)
    }

    /// 将UIImage存入内存缓存
    private func setUIImageToMemory(_ image: UIImage, for url: URL) {
        let cost = Int(image.size.width * image.size.height * 4) // 估算内存占用
        let key = url.absoluteString as NSString
        uiImageCache.setObject(image, forKey: key, cost: cost)
    }

    /// 从内存缓存获取SwiftUI Image
    private func getSwiftUIImageFromMemory(for url: URL) -> Image? {
        let key = url.absoluteString as NSString
        return swiftUIImageCache.object(forKey: key)?.image
    }

    /// 将SwiftUI Image存入内存缓存
    private func setSwiftUIImageToMemory(_ image: Image, for url: URL) {
        let wrapper = ImageWrapper(image: image)
        let key = url.absoluteString as NSString
        swiftUIImageCache.setObject(wrapper, forKey: key, cost: 1000) // 固定成本
    }

    // MARK: - Private Methods - Disk Cache

    /// 从磁盘缓存获取UIImage
    private func getUIImageFromDisk(for url: URL) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }

                let fileName = self.cacheFileName(for: url)
                let filePath = self.cacheDirectory.appendingPathComponent(fileName)

                guard self.fileManager.fileExists(atPath: filePath.path),
                      let data = try? Data(contentsOf: filePath),
                      let image = UIImage(data: data) else {
                    continuation.resume(returning: nil)
                    return
                }

                continuation.resume(returning: image)
            }
        }
    }

    /// 将UIImage存入磁盘缓存
    private func setUIImageToDisk(_ image: UIImage, for url: URL) {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            guard let data = image.jpegData(compressionQuality: 0.8) else { return }

            let fileName = self.cacheFileName(for: url)
            let filePath = self.cacheDirectory.appendingPathComponent(fileName)

            do {
                try data.write(to: filePath)
                // 磁盘存储成功，不记录日志（减少日志噪音）
            } catch {
                Log.warning("⚠️ [ImageCache] 磁盘存储失败: \(fileName)")
            }
        }
    }

    // MARK: - Private Methods - Network

    /// 从网络下载UIImage（优化版：支持请求去重和并发控制）
    private func downloadUIImage(from url: URL) async -> UIImage? {
        // 检查是否已有正在进行的下载任务
        if let existingTask = await downloadTaskManager.getTask(for: url) {
            Log.debug("🔄 [ImageCache] 复用正在进行的下载任务: \(url.lastPathComponent)")
            return await existingTask.value
        }

        // 创建新的下载任务
        let downloadTask = Task<UIImage?, Never> {
            // 使用现代并发控制
            return await downloadLimiter.performDownload {
                do {
                    // 开始下载，不记录日志（减少日志噪音）
                    let (data, response) = try await urlSession.data(from: url)

                    // 验证响应
                    guard let httpResponse = response as? HTTPURLResponse else {
                        Log.warning("⚠️ [ImageCache] 无效的HTTP响应: \(url.lastPathComponent)")
                        return nil
                    }

                    guard httpResponse.statusCode == 200 else {
                        Log.warning("⚠️ [ImageCache] HTTP错误 \(httpResponse.statusCode): \(url.lastPathComponent)")
                        return nil
                    }

                    guard let image = UIImage(data: data) else {
                        Log.warning("⚠️ [ImageCache] 图片数据无效: \(url.lastPathComponent)")
                        return nil
                    }

                    // 存入缓存
                    await MainActor.run {
                        self.setUIImageToMemory(image, for: url)
                    }
                    setUIImageToDisk(image, for: url)

                    // 下载完成，不记录日志（减少日志噪音）
                    return image

                } catch is CancellationError {
                    // 下载被取消，不记录日志（减少日志噪音）
                    return nil
                } catch {
                    // 只记录重要的错误，减少日志噪音
                    if let urlError = error as? URLError {
                        switch urlError.code {
                        case .timedOut:
                            Log.warning("⏰ [ImageCache] 下载超时: \(url.lastPathComponent)")
                        case .notConnectedToInternet, .networkConnectionLost:
                            Log.warning("📶 [ImageCache] 网络连接问题: \(url.lastPathComponent)")
                        case .cancelled:
                            // 取消操作不记录日志
                            break
                        default:
                            Log.warning("⚠️ [ImageCache] 网络错误: \(url.lastPathComponent)")
                        }
                    } else {
                        Log.warning("⚠️ [ImageCache] 下载失败: \(url.lastPathComponent)")
                    }
                    return nil
                }
            }
        }

        await downloadTaskManager.setTask(downloadTask, for: url)
        let result = await downloadTask.value
        await downloadTaskManager.removeTask(for: url)

        return result
    }

    // MARK: - Private Methods - Utilities

    /// 生成缓存文件名
    private func cacheFileName(for url: URL) -> String {
        return url.absoluteString.data(using: .utf8)?.base64EncodedString()
            .replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "+", with: "-") ?? UUID().uuidString
    }



    /// 处理内存警告
    @objc private func handleMemoryWarning() {
        Log.warning("⚠️ [ImageCache] 收到内存警告，清理缓存")
        clearMemoryCache()

        Task {
            let tasks = await downloadTaskManager.getAllTasks()
            for (_, task) in tasks {
                task.cancel()
            }
            await downloadTaskManager.cancelAllTasks()
        }
    }

    /// 清理正在进行的下载任务（用于应用进入后台等场景）
    func cancelAllDownloads() {
        Task {
            let tasks = await downloadTaskManager.getAllTasks()
            for (_, task) in tasks {
                task.cancel()
            }
            await downloadTaskManager.cancelAllTasks()
        }
    }

    /// 处理应用进入后台
    @objc private func handleAppDidEnterBackground() {
        Log.debug("📱 [ImageCache] 应用进入后台，取消下载任务")
        cancelAllDownloads()
    }
}

// MARK: - Helper Classes

/// SwiftUI Image包装器，用于NSCache存储
private class ImageWrapper {
    let image: Image

    init(image: Image) {
        self.image = image
    }
}

// MARK: - Download Limiter Actor

/// 下载限制器 - 使用 Swift 并发控制下载数量
private actor DownloadLimiter {
    private let maxConcurrentDownloads: Int
    private var currentDownloads: Int = 0
    private var waitingTasks: [CheckedContinuation<Void, Never>] = []

    init(maxConcurrentDownloads: Int) {
        self.maxConcurrentDownloads = maxConcurrentDownloads
    }

    /// 执行下载任务（带并发控制）
    func performDownload<T>(_ operation: @Sendable () async -> T) async -> T {
        // 等待获取下载槽位
        await acquireSlot()

        // 执行下载
        let result = await operation()

        // 释放槽位
        releaseSlot()

        return result
    }

    /// 获取下载槽位
    private func acquireSlot() async {
        if currentDownloads < maxConcurrentDownloads {
            currentDownloads += 1
        } else {
            // 等待槽位释放
            await withCheckedContinuation { continuation in
                waitingTasks.append(continuation)
            }
            currentDownloads += 1
        }
    }

    /// 释放下载槽位
    private func releaseSlot() {
        currentDownloads -= 1

        // 唤醒等待的任务
        if !waitingTasks.isEmpty {
            let continuation = waitingTasks.removeFirst()
            continuation.resume()
        }
    }
}

/// 下载任务管理器 - 线程安全的任务管理
actor DownloadTaskManager {
    private var ongoingDownloads: [URL: Task<UIImage?, Never>] = [:]

    /// 获取正在进行的下载任务
    func getTask(for url: URL) -> Task<UIImage?, Never>? {
        return ongoingDownloads[url]
    }

    /// 设置下载任务
    func setTask(_ task: Task<UIImage?, Never>, for url: URL) {
        ongoingDownloads[url] = task
    }

    /// 移除下载任务
    func removeTask(for url: URL) {
        ongoingDownloads.removeValue(forKey: url)
    }

    /// 取消所有下载任务
    func cancelAllTasks() {
        for (_, task) in ongoingDownloads {
            task.cancel()
        }
        ongoingDownloads.removeAll()
    }

    /// 获取所有正在进行的任务（用于内存警告处理）
    func getAllTasks() -> [(URL, Task<UIImage?, Never>)] {
        return Array(ongoingDownloads)
    }
}