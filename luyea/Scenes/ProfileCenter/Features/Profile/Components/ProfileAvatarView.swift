import SwiftUI

/// 个人中心头像组件，无状态组件
struct ProfileAvatarView: View {
    let avatarUrl: String
    let isCurrentUser: Bool
    let onEditProfile: () -> Void
    
    // MARK: - Body
    var body: some View {
        ZStack(alignment: .bottomTrailing) {
            avatarImage
            if isCurrentUser {
                editButton
            }
        }
    }
    
    // MARK: - 头像图片
    private var avatarImage: some View {
        CachedAsyncImage(
            url: URL(string: avatarUrl)
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Circle()
                .fill(Color.gray.opacity(0.2))
                .overlay(
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: ProfileCenterConstants.avatarSize * 0.6))
                        .foregroundColor(.gray.opacity(0.6))
                )
        } errorView: {
            Circle()
                .fill(Color.red.opacity(0.1))
                .overlay(
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: ProfileCenterConstants.avatarSize * 0.6))
                        .foregroundColor(.red.opacity(0.6))
                )
        }
        .frame(width: ProfileCenterConstants.avatarSize, height: ProfileCenterConstants.avatarSize)
        .clipShape(Circle())
    }
    
    // MARK: - 编辑按钮
    private var editButton: some View {
        Button(action: onEditProfile) {
            Image(systemName: "pencil.circle.fill")
                .font(.system(size: ProfileCenterConstants.avatarEditIconSize))
                .foregroundColor(.accentColor)
                .background(Color.white)
                .clipShape(Circle())
        }
    }
}

#Preview {
    ProfileAvatarView(
        avatarUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop",
        isCurrentUser: true,
        onEditProfile: {}
    )
} 