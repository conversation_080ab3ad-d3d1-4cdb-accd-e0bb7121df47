import SwiftUI

/// 目的地选择按钮组件
///
/// 提供目的地选择功能的按钮，可复用于不同的筛选场景。
/// 具有统一的样式和交互行为。
struct DestinationButtonView: View {
    
    // MARK: - Properties

    /// 当前选中的位置文本
    let selectedLocation: String

    /// 是否显示喜欢状态（爱心图标）
    let showLikedIcon: Bool

    /// 按钮点击回调
    let onTap: () -> Void
    
    // MARK: - Constants
    
    private enum Constants {
        static let cornerRadius: CGFloat = 8
        static let horizontalPadding: CGFloat = 12
        static let verticalPadding: CGFloat = 6
        static let shadowRadius: CGFloat = 3
        static let shadowOffset: CGFloat = 1
        static let borderOpacity: Double = 0.1
        static let shadowOpacity: Double = 0.04
        static let iconSize: CGFloat = 13
        static let textSize: CGFloat = 14
        static let chevronSize: CGFloat = 11
        static let iconSpacing: CGFloat = 6
    }
    
    // MARK: - Body
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: Constants.iconSpacing) {
                // 动态图标：喜欢状态显示爱心，否则显示地图
                Image(systemName: showLikedIcon ? "heart.fill" : "map")
                    .font(.system(size: Constants.iconSize, weight: .medium))
                    .foregroundColor(showLikedIcon ? .red : .primary.opacity(0.7))

                // 位置文本
                Text(selectedLocation)
                    .font(.system(size: Constants.textSize, weight: .medium))
                    .foregroundColor(.primary)

                // 下拉箭头
                Image(systemName: "chevron.down")
                    .font(.system(size: Constants.chevronSize, weight: .medium))
                    .foregroundColor(.primary.opacity(0.5))
            }
            .padding(.horizontal, Constants.horizontalPadding)
            .padding(.vertical, Constants.verticalPadding)
            .background(buttonBackground)
            .overlay(buttonBorder)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Style Components
    
    /// 按钮背景
    private var buttonBackground: some View {
        RoundedRectangle(cornerRadius: Constants.cornerRadius)
            .fill(Color(.systemBackground))
            .shadow(
                color: Color.primary.opacity(Constants.shadowOpacity),
                radius: Constants.shadowRadius,
                x: 0,
                y: Constants.shadowOffset
            )
    }

    /// 按钮边框
    private var buttonBorder: some View {
        RoundedRectangle(cornerRadius: Constants.cornerRadius)
            .stroke(Color.secondary.opacity(Constants.borderOpacity), lineWidth: 0.5)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        DestinationButtonView(
            selectedLocation: "精选",
            showLikedIcon: false,
            onTap: {}
        )

        DestinationButtonView(
            selectedLocation: "喜欢",
            showLikedIcon: true,
            onTap: {}
        )

        DestinationButtonView(
            selectedLocation: "杭州",
            showLikedIcon: false,
            onTap: {}
        )

        DestinationButtonView(
            selectedLocation: "上海市浦东新区",
            showLikedIcon: false,
            onTap: {}
        )
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
