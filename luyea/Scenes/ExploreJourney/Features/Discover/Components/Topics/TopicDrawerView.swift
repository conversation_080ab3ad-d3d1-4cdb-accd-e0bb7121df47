import SwiftUI

struct TopicDrawerView: View {
    @Binding var isShowing: Bool
    @ObservedObject var discoverViewModel: DiscoverViewModel
    var onTopicsChanged: ((Set<String>) -> Void)? // 传递最新选中集合
    
    // 定义网格布局
    private let columns = [
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible())
    ]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .top) {
                // 背景遮罩层 - 增强弱化效果，类似系统sheet
                if isShowing {
                    ZStack {
                        // 主遮罩层 - 使用更轻柔的透明度，覆盖整个屏幕
                        Color.primary.opacity(0.2)

                        // 渐变遮罩层，从顶部到底部逐渐加深，营造深度感
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.primary.opacity(0.02), location: 0.0),
                                .init(color: Color.primary.opacity(0.08), location: 0.4),
                                .init(color: Color.primary.opacity(0.15), location: 1.0)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    }
                    .frame(width: geometry.size.width, height: UIScreen.main.bounds.height)
                    .position(x: geometry.size.width / 2, y: UIScreen.main.bounds.height / 2)
                    .opacity(isShowing ? 1 : 0)
                    .animation(.easeOut(duration: 0.2), value: isShowing)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            isShowing = false
                        }
                    }
                }

                VStack(spacing: 0) {
                    if isShowing {
                        VStack(spacing: 0) {
                        // 顶部操作栏
                        HStack {
                            Text("探索主题")
                                .font(.system(size: 15, weight: .medium))
                                .foregroundColor(.primary.opacity(0.8))
                            
                            Spacer()
                            
                            if !discoverViewModel.selectedTopicIds.isEmpty {
                                Button(action: {
                                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                        // 清空所有已选话题并触发数据重新加载
                                        discoverViewModel.clearAllTopicSelections()
                                    }
                                    // 通知父组件状态变化
                                    onTopicsChanged?(discoverViewModel.selectedTopicIds)
                                }) {
                                    HStack(spacing: 4) {
                                        Image(systemName: "trash")
                                            .font(.system(size: 11))
                                            .foregroundColor(.red.opacity(0.8))
                                        Text("清空")
                                            .font(.system(size: 13, weight: .medium))
                                            .foregroundColor(.red.opacity(0.8))
                                    }
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(Color.red.opacity(0.08))
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 4)
                                            .stroke(Color.red.opacity(0.2), lineWidth: 0.5)
                                    )
                                }
                                .buttonStyle(ScaleButtonStyle())
                            }
                        }
                        .padding(.horizontal, 12)
                        .padding(.top, 0)
                        .padding(.bottom, 8)
                        
                        Divider()
                            .padding(.horizontal, 12)
                        
                        if discoverViewModel.isTopicsLoading {
                            ProgressView()
                                .padding()
                        } else {
                            LazyVGrid(columns: columns, spacing: 6) {
                                ForEach(discoverViewModel.filteredTopics) { topic in
                                    TopicSelectionButton(
                                        topic: topic,
                                        isSelected: discoverViewModel.selectedTopicIds.contains(topic.id),
                                        onToggle: handleTopicToggle
                                    )
                                    .id(topic.id) // 帮助SwiftUI更好地识别和复用视图
                                }
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                        }
                    }
                    .background(Color(.systemBackground))
                    .bottomCornerRadius(8)
                    .padding(.horizontal, 12)
                }
            }
            .frame(maxWidth: .infinity)
            .offset(y: isShowing ? 0 : -50)
            .opacity(isShowing ? 1 : 0)
            .scaleEffect(y: isShowing ? 1 : 0.8, anchor: .top)
            .animation(.spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0), value: isShowing)
            }
        }
        .onAppear {
            // 添加滚动监听
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("ScrollViewDidScroll"),
                object: nil,
                queue: .main
            ) { _ in
                if isShowing {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                        isShowing = false
                    }
                }
            }
            
            // 添加 tab 切换监听
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("TabDidChange"),
                object: nil,
                queue: .main
            ) { _ in
                if isShowing {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                        isShowing = false
                    }
                }
            }
        }
        .onDisappear {
            // 移除滚动监听
            NotificationCenter.default.removeObserver(
                self,
                name: NSNotification.Name("ScrollViewDidScroll"),
                object: nil
            )
            // 移除 tab 切换监听
            NotificationCenter.default.removeObserver(
                self,
                name: NSNotification.Name("TabDidChange"),
                object: nil
            )
        }
    }

    // MARK: - Action Handlers

    /// 处理话题切换
    private func handleTopicToggle(_ topicId: String) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            discoverViewModel.toggleTopicSelection(topicId)
        }

        // 优化：防抖处理，避免频繁触发
        onTopicsChanged?(discoverViewModel.selectedTopicIds)
    }
}

// MARK: - Topic Selection Button

/// 优化的话题选择按钮组件
private struct TopicSelectionButton: View {
    let topic: Topic
    let isSelected: Bool
    let onToggle: (String) -> Void

    var body: some View {
        Button(action: {
            onToggle(topic.id)
        }) {
            TopicTagView(
                topic: topic,
                style: .selectable(isSelected: isSelected)
            )
        }
        .buttonStyle(ScaleButtonStyle())
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isSelected)
    }
}



// MARK: - Preview
struct TopicDrawerView_Previews: PreviewProvider {
    @State static var isShowing = true

    static var previews: some View {
        TopicDrawerView(
            isShowing: $isShowing,
            discoverViewModel: DiscoverViewModel()
        )
    }
}
 