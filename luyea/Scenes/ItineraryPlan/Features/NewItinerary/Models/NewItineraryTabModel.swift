import Foundation
import SwiftUI

/// “添加行程”功能中不同创建方式的 Tab 定义
///
/// 使用枚举架构提供类型安全的Tab管理，支持SwiftUI的现代化开发模式。
/// 集中管理Tab的视觉属性和标识信息，便于维护和扩展。
enum TabModel: String, CaseIterable, Identifiable {
    case create = "创建行程"
    case smart = "智能规划"
    case importGuide = "导入攻略"

    // MARK: - Identifiable

    var id: String { rawValue }

    // MARK: - Visual Properties

    /// 默认状态图标
    var iconName: String {
        switch self {
        case .create:
            return "plus.circle"
        case .smart:
            return "wand.and.stars"
        case .importGuide:
            return "doc.text.magnifyingglass"
        }
    }

    /// 选中状态图标（提供更好的视觉反馈）
    var selectedIconName: String {
        switch self {
        case .create:
            return "plus.circle.fill"
        case .smart:
            return "wand.and.stars"
        case .importGuide:
            return "doc.text.magnifyingglass"
        }
    }

    /// Tab主题色（用于选中状态和强调显示）
    var accentColor: Color {
        switch self {
        case .create:
            return .blue
        case .smart:
            return .blue
        case .importGuide:
            return .blue
        }
    }

    // MARK: - Computed Properties

    /// Tab的简短描述
    var description: String {
        switch self {
        case .create:
            return "手动创建个性化行程"
        case .smart:
            return "AI智能规划推荐路线"
        case .importGuide:
            return "导入现有攻略模板"
        }
    }
}