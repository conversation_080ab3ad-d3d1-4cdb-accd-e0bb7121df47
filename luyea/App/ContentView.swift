//
//  ContentView.swift
//  luyea
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/11.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var viewModel = ContentViewViewModel()
    @Environment(\.authenticationManager) private var authManager
    @EnvironmentObject private var audioPlayerManager: AudioPlayerManager
    @State private var isShowingLogin = false

    var body: some View {
        ZStack {
            // 使用统一的TabBar容器
            TabBarContainerView()

            // 启动图
            if viewModel.isShowingSplash {
                SplashView(isShowingSplash: $viewModel.isShowingSplash)
                    .transition(.opacity)
                    .animation(.easeInOut, value: viewModel.isShowingSplash)
                    .zIndex(1)
            }

            // 悬浮音频播放器 - 独立于全局动画
            AudioPlayerFloatingView()
                .zIndex(999) // 确保播放器始终在最顶层
        }
        .sheet(isPresented: $isShowingLogin) {
            AuthenticationView()
        }
        .onAppear {
            // 同步初始状态
            isShowingLogin = authManager.isShowingLogin
        }
        .onReceive(authManager.$isShowingLogin) { newValue in
            isShowingLogin = newValue
        }
        .onChange(of: isShowingLogin) { _, newValue in
            // 当本地状态改变时，同步到 authManager
            if newValue != authManager.isShowingLogin {
                if newValue {
                    authManager.presentLogin()
                } else {
                    authManager.dismissAuthentication()
                }
            }
        }
    }
}

#Preview {
    ContentView()
}
