# 🏗️ Luyea 项目架构规范文档

## 📚 文档目录

- [📋 快速理解指南](#-快速理解指南)
- [✅ 理解验证清单](#-理解验证清单)
- [🏗️ 分层架构详解](#️-分层架构详解)
- [🔗 依赖关系与数据流](#-依赖关系与数据流)
- [⚠️ 常见误区与澄清](#️-常见误区与澄清)
- [🏗️ 业务模块架构详细规范](#️-业务模块架构详细规范)
- [📝 命名规范标准](#-命名规范标准)
- [🎯 视图层级命名体系](#-视图层级命名体系)
- [🛠️ 开发实践指南](#️-开发实践指南)
- [🎯 实施检查清单](#-实施检查清单)

## 📋 快速理解指南

### 🎯 核心概念
Luyea采用**混合模块化架构**：
- **🌍 全局Core层**：`luyea/Core/` - 整个应用的基础设施和通用服务，采用分层架构
- **🎬 业务模块层**：`luyea/Scenes/` - 具体的业务功能模块，采用扁平化架构便于开发和维护

### ⚡ 架构特点
```
✅ 混合架构设计：全局采用分层架构，模块采用扁平化架构
✅ Service层普及：全局和模块都有各自的Service层
✅ 职责明确：每个目录都有清晰的职责边界
✅ 依赖清晰：模块Service可以依赖全局Service
```

## 🏗️ 分层架构详解

### 🌍 全局Core层（luyea/Core/）
```
luyea/Core/
├── 🔧 Services/                    # 全局基础服务
│   ├── NetworkService.swift       # 网络请求服务
│   ├── ImageCacheService.swift    # 图片缓存服务
│   └── LocationDataService.swift # 位置数据服务
├── 👨‍💼 Managers/                    # 全局状态管理
├── 📊 Models/                      # 全局数据模型
├── 🛠️ Utils/                       # 全局工具
├── 🎨 UI/                          # 全局UI组件
└── ⚙️ Constants/                   # 全局常量
```

### 🎬 业务模块层（luyea/Scenes/ModuleName/）
```
Scenes/ModuleName/                  # 具体业务模块
├── 📊 Models/                       # 模块共用数据模型
├── ⚙️ Constants/                    # 模块专用常量
├── 🛠️ Utils/                        # 模块专用工具（如需要）
├── 🔧 Services/                     # 主视图专用服务
├── 🧠 ViewModels/                   # 主视图专用ViewModel
├── 🧩 Components/                   # 主视图专用组件
├── 🚀 Features/                     # 子功能实现层
│   ├── FeatureA/                   # 功能A
│   │   ├── Features/               # 功能A的子功能（如需要）
│   │   │   ├── SubFeatureA1/       # 子功能A1（简单结构）
│   │   │   │   ├── Services/       # 子功能A1专用服务（如需要）
│   │   │   │   ├── ViewModels/     # 子功能A1视图模型
│   │   │   │   └── Views/          # 子功能A1视图
│   │   │   └── SubFeatureA2/       # 子功能A2（简单结构）
│   │   ├── Components/             # 功能A专用组件
│   │   ├── Services/               # 功能A专用服务
│   │   ├── ViewModels/             # 功能A视图模型
│   │   └── Views/                  # 功能A视图
│   └── FeatureB/                   # 功能B
│       ├── Features/               # 功能B的子功能（如需要）
│       ├── Components/             # 功能B专用组件
│       ├── Services/               # 功能B专用服务
│       ├── ViewModels/             # 功能B视图模型
│       └── Views/                  # 功能B视图
├── 🤝 Shared/                       # 模块内共享层
└── 🚪 Views/                        # 模块入口视图层
```

**🎯 核心原则：**
- **就近原则**: 功能相关的Service放在对应的Feature目录中
- **职责分离**: 模块根目录的Services负责主视图的数据聚合和业务逻辑
- **Service分工**: 全局Service提供基础能力，功能Service处理具体业务逻辑
- **依赖管理**: 功能Service可以依赖全局Service，但不能跨功能依赖
- **结构统一**: 模块根目录和子功能都遵循相同的目录组织原则

## ✅ 理解验证清单

**阅读完本文档后，请确认以下理解是否正确：**

- [ ] **混合架构**: 全局采用分层架构，模块采用扁平化架构
- [ ] **Service层职责**: 全局Service提供基础能力，功能Service处理具体业务逻辑
- [ ] **依赖关系**: ViewModel → 功能Service → 全局Service，层次清晰
- [ ] **子模块组织**: 使用`Features`目录组织子功能，保持命名一致性
- [ ] **视图命名**: 主入口视图用`{Name}View`，内容视图用`{Name}ContentView`
- [ ] **服务命名**: 功能服务用`{Feature}Service.swift`格式
- [ ] **目录层级**: 从项目根目录计算，避免超过7层目录嵌套，推荐6层以内
- [ ] **文件后缀**: 所有视图文件必须以`View.swift`结尾

**如果以上任何一项不确定，请重新阅读对应章节。**

## 🎯 快速参考卡片

```
┌─────────────────────────────────────────────────────────────┐
│                    🏗️ Luyea 架构速查卡                        │
├─────────────────────────────────────────────────────────────┤
│ 🌍 全局Core层: luyea/Core/                                  │
│   ✅ Services/ - 基础设施服务（网络、缓存、位置等）             │
│   ✅ Managers/ - 全局状态管理                                │
│   ✅ Models/ - 全局数据模型                                  │
│   ✅ Utils/ - 全局工具                                       │
│                                                             │
│ 🎬 业务模块层: luyea/Scenes/ModuleName/                     │
│   ✅ Models/ - 模块共用数据模型                              │
│   ✅ Constants/ - 模块专用常量                               │
│   ✅ Services/ - 主视图专用服务（可选）                      │
│   ✅ ViewModels/ - 主视图专用ViewModel                       │
│   ✅ Components/ - 主视图专用组件                            │
│   ✅ Features/ - 子功能实现层                                │
│   ✅ Shared/ - 模块内共享层                                  │
│   ✅ Views/ - 模块入口视图层                                 │
│                                                             │
│ 📝 命名规范:                                                │
│   ✅ {Name}View.swift - 主入口视图                          │
│   ✅ {Name}ContentView.swift - 内容视图                     │
│   ✅ {Name}Service.swift - 功能服务                         │
│   ✅ Features/ - 子功能容器                                  │
│   ✅ Features/SubFeature/Features/ - 允许嵌套               │
│                                                             │
│ 🔍 Components vs Features:                                 │
│   📦 Components - 主视图UI元素，数据展示，简单交互          │
│   🚀 Features - 独立业务功能，完整流程，独立导航            │
│                                                             │
│ 🚨 核心原则:                                                │
│   • 功能Service就近放置，便于维护和理解                       │
│   • Service层分工明确：基础设施、功能专用、主视图专用         │
│   • 依赖关系清晰，支持独立开发和测试                          │
│   • 所有视图文件以View.swift结尾                             │
└─────────────────────────────────────────────────────────────┘
```

## 🔗 依赖关系与数据流

### 📊 模块内部依赖关系
```
🚪 Views     ←→  🧩 Features  ←→  🏛️ Core
   ↓              ↓              ↓
🤝 Shared    ←→  🤝 Shared    ←→  🤝 Shared
```

### 🌍 跨层级依赖关系
```
功能ViewModel → 功能Service → 全局Core/Services
主视图Service → 全局Core/Services
业务模块 → 全局Core/Utils    （直接导入）
业务模块 → 全局Core/UI       （直接导入）
业务模块 → 全局Core/Models   （直接导入）
```

### ⚡ 依赖规则
- **⬆️ 向上依赖**: 下层可以依赖上层，上层禁止依赖下层
- **🚫 横向隔离**: 同层级模块间不直接依赖，通过Shared层通信
- **🔄 共享复用**: 跨模块功能通过全局Core或Shared层实现
- **📱 单向数据流**: 数据从全局Core → 模块Services → Features → Views单向流动
- **💉 服务注入**: 通过依赖注入管理Service之间的依赖关系

### 🎯 分层架构核心理念

#### **📊 架构组织原则**
```
全局Core层采用分层架构：
├── Services/    # 服务层 - 处理业务逻辑和数据
├── Models/      # 模型层 - 定义数据结构
├── Utils/       # 工具层 - 提供辅助功能
└── Constants/   # 常量层 - 管理配置参数

业务模块层采用扁平化架构：
├── Models/      # 模块共用数据模型
├── Constants/   # 模块专用常量
├── Services/    # 主视图专用服务
├── ViewModels/  # 主视图专用ViewModel
├── Components/  # 主视图专用组件
└── Features/    # 子功能集合
```

#### **🔗 清晰的依赖关系**
```
业务模块 → 全局Core （可以依赖）
模块Service → 全局Service （可以依赖）
模块间 ↔ 模块间 （禁止直接依赖）
```

#### **⚡ 核心设计原则**
- **单一职责**：每个文件和目录只负责一个明确的功能
- **架构适配**：全局采用分层架构，模块采用扁平化架构，各自适配最佳实践
- **依赖清晰**：依赖关系明确，便于理解和测试
- **职责分离**：基础设施与业务逻辑分离
- **可扩展性**：支持新模块和新功能的快速添加

## 📁 项目目录结构详解

### 🏗️ 项目根目录组织
```
luyea/
├── 📖 README.md                    # 项目介绍文档
├── 📦 luyea.xcodeproj/            # Xcode项目配置
├── 🔧 scripts/                    # 构建部署脚本
└── 💻 luyea/                      # 源代码目录（Xcode可见）
    ├── 📋 ARCHITECTURE.md         # 架构规范文档
    ├── 🎯 cursorrules.md          # AI开发规则
    ├── 🚀 App/                    # 应用入口点
    ├── 🏛️ Core/                   # 全局基础设施层
    ├── 🎬 Scenes/                 # 业务模块层
    └── 📁 Resources/              # 静态资源文件
```

### 🎯 目录设计原则
- **📱 开发友好**: 开发文档与代码在同一环境，便于查阅
- **🔄 职责分离**: 全局基础设施与业务模块分离
- **📚 就近原则**: 相关文件就近组织，便于维护

## 🏗️ 架构层级说明

### 📊 双层架构体系



## ⚠️ 常见误区与澄清

### ❌ 误区一：Service层组织混乱
```bash
# ❌ 错误理解：功能Service放在模块根目录中
Scenes/ExploreJourney/Services/
├── ExploreService.swift     # ❌ 应该在Features/Explore/Services/
├── DiscoverService.swift    # ❌ 应该在Features/Discover/Services/
└── NearbyService.swift      # ❌ 应该在Features/Nearby/Services/

# ✅ 正确理解：Service按职责分层组织
# 全局Core/Services/ - 基础设施服务
luyea/Core/Services/
├── NetworkService.swift     # ✅ 网络请求基础服务
├── CacheService.swift       # ✅ 缓存管理基础服务
└── LocationService.swift    # ✅ 位置服务基础服务

# 模块Services/ - 主视图专用服务（可选）
Scenes/ExploreJourney/Services/
└── ExploreJourneyService.swift  # ✅ 主视图数据聚合服务

# 功能Services/ - 功能专用服务
Features/Discover/Services/
└── DiscoverService.swift    # ✅ 发现功能专用服务

Features/Explore/Services/
└── ExploreService.swift     # ✅ 探索功能专用服务
```

### ❌ 误区二：Service和Manager的区别不清
```bash
# ✅ 正确理解：Service vs Manager
luyea/Core/
├── Services/               # 数据和业务逻辑服务
│   ├── NetworkService.swift      # 网络请求服务
│   ├── ImageCacheService.swift   # 图片缓存服务
│   └── LocationDataService.swift # 位置数据服务
├── Managers/               # 状态和资源管理
│   ├── ToastManager.swift        # 全局提示管理
│   ├── LocationManager.swift     # 位置权限管理
│   └── TabBarStateManager.swift  # 标签栏状态管理

# Service特点：处理数据和业务逻辑
# Manager特点：管理状态和资源生命周期
```

### ❌ 误区三：Components和Features职责混淆
```bash
# ❌ 错误：将独立功能放在Components中
ItineraryPlan/
├── Components/
│   ├── ItineraryListView.swift     # ✅ 正确：主视图展示组件
│   └── NewItineraryFlow/           # ❌ 错误：独立业务流程应该是Features
│       ├── CreateView.swift
│       └── SelectView.swift

# ✅ 正确：明确区分Components和Features
ItineraryPlan/
├── Components/                     # 主视图专用组件
│   ├── ItineraryListView.swift     # 行程列表展示
│   ├── ItineraryCardView.swift     # 行程卡片展示
│   └── ItineraryHeaderView.swift   # 页面头部
├── Features/                       # 独立子功能
│   └── NewItinerary/               # 创建行程完整流程
│       ├── Features/               # NewItinerary的子功能
│       ├── Components/             # NewItinerary专用组件
│       ├── Services/               # NewItinerary专用服务
│       └── Views/                  # NewItinerary视图
```

#### **🎯 判断原则**
- **Components**: 主视图中直接使用的UI元素，主要负责数据展示
- **Features**: 需要独立导航的完整业务功能，包含完整流程

### ❌ 误区四：视图命名不规范
```bash
# ❌ 错误命名
DiscoverFeatureView.swift           # 包含Feature字样
DiscoverDetailFeatureView.swift     # 包含Feature字样
DiscoverFilter.swift               # 缺少View后缀

# ✅ 正确命名
DiscoverView.swift                  # 主入口视图
DiscoverDetailView.swift            # 子功能入口视图
DiscoverDetailContentView.swift     # 子功能内容视图
DiscoverFilterBarView.swift         # 专用组件视图
```

## 🏗️ 业务模块架构详细规范

### 📊 模块根目录层 - 主视图和共享基础设施
```
Scenes/ModuleName/
├── 📊 Models/                   # 模块共用数据模型
│   └── {Module}Model.swift      # 模块共用业务模型
├── ⚙️ Constants/                # 模块专用常量
│   └── {Module}Constants.swift  # 业务常量
├── 🛠️ Utils/                    # 模块专用工具（如需要）
├── 🔧 Services/                 # 主视图专用服务（可选）
│   └── {Module}Service.swift    # 主视图数据聚合服务
├── 🧠 ViewModels/               # 主视图专用ViewModel
│   └── {Module}ViewModel.swift  # 主视图业务逻辑
└── 🧩 Components/               # 主视图专用组件
    ├── {Module}ListView.swift   # 主视图列表组件
    └── {Module}CardView.swift   # 主视图卡片组件
```

**职责定义：**
- **Models**: 模块内多个功能共用的数据模型
- **Constants**: 模块专用常量，按业务功能分组定义
- **Utils**: 模块专用工具类和扩展（如需要）
- **Services**: 主视图专用服务，负责聚合主视图所需的数据和业务逻辑（可选）
- **ViewModels**: 主视图专用ViewModel，管理主视图的状态和业务逻辑
- **Components**: 主视图专用组件，在主视图中直接使用的UI元素

#### **📋 Constants设计规范**

**分层原则：**
- **全局Constants** (`luyea/Core/Constants/`): App级别通用常量
  - `DesignSystemConstants.swift`: 统一的设计系统常量（动画、间距、圆角等）
  - `AppConfig.swift`: 应用配置参数（网络、缓存等）
  - `AppConstants.swift`: 应用基本信息
- **模块Constants** (`Scenes/ModuleName/Constants/`): 模块专用常量

**设计系统常量优先原则：**
- ✅ **优先使用全局设计系统**: 所有UI相关常量优先使用 `DesignSystemConstants`
- ✅ **统一设计语言**: 动画、间距、圆角等使用统一标准
- ✅ **避免重复定义**: 禁止在模块中重复定义基础设施常量
- ✅ **平滑迁移**: 使用 `@deprecated` 标记指导迁移

**定义原则：**
- ✅ **模块专用**: 只定义本模块内使用的业务常量
- ✅ **枚举分组**: 按业务功能或职责分组
- ✅ **避免重复**: 全局通用的常量应使用顶层Core中的Constants
- ✅ **语义清晰**: 常量名称要明确表达用途和含义

#### **📝 标准示例（基于ItineraryPlanConstants）**

```swift
// Scenes/ItineraryPlan/Constants/ItineraryPlanConstants.swift
import Foundation
import SwiftUI

/// 行程计划模块常量定义
enum ItineraryPlanConstants {

    // MARK: - 核心文本常量（仅保留重复使用的）
    enum Text {
        static let noItinerariesTitle = "还没有行程计划"
        static let noItinerariesSubtitle = "开始规划你的第一次旅行吧"
        static let loadingError = "加载失败"
        static let retryAction = "重试"
    }

    // MARK: - 核心布局常量（仅保留设计系统级别的）
    enum Layout {
        static let cardCornerRadius: CGFloat = 18
        static let maxDisplayedItineraries = 3
        static let maxDisplayedFootprints = 6
    }

    // MARK: - 核心动画常量（仅保留复杂配置）
    enum Animation {
        static let cardExpansion: SwiftUI.Animation = .spring(response: 0.45, dampingFraction: 0.85)
    }

    // MARK: - 状态颜色（业务逻辑相关）
    enum StatusColors {
        static let inProgress = Color.blue
        static let completed = Color.gray
        static let upcoming = Color.green
    }

    // MARK: - 业务配置
    enum Config {
        static let cacheRefreshInterval: TimeInterval = 300 // 5分钟
        static let searchDebounceInterval: TimeInterval = 0.3
    }
}
```

#### **🎯 常量分组指导**

**Text（文本常量）**
- 模块内重复使用的文本
- 错误提示、空状态文案
- 按钮标题、提示信息

**Layout（布局常量）**
- 模块特有的布局参数
- 卡片圆角、间距、尺寸
- 显示数量限制

**Animation（动画常量）**
- 模块专用的动画配置
- 复杂的动画参数组合
- 交互动效设置

**StatusColors（状态颜色）**
- 业务状态相关的颜色
- 模块特有的颜色语义
- 状态指示器颜色

**Config（业务配置）**
- 缓存刷新间隔
- 搜索防抖时间
- 业务逻辑参数

#### **❌ 避免的常量定义**

```swift
// ❌ 错误：全局通用的常量不应在模块中定义
enum ItineraryPlanConstants {
    enum Global {
        static let screenWidth = UIScreen.main.bounds.width  // 应该在全局Core中
        static let primaryColor = Color.blue                 // 应该在全局Core中
    }
}

// ❌ 错误：单次使用的常量不需要定义
enum ItineraryPlanConstants {
    enum OneTime {
        static let buttonTitle = "确定"  // 只用一次，直接写在代码中
    }
}
```

#### **✅ 推荐的常量定义**

```swift
// ✅ 正确：模块专用且重复使用的常量
enum ItineraryPlanConstants {
    enum Validation {
        static let maxTitleLength = 50
        static let minDays = 1
        static let maxDays = 30
    }

    enum API {
        static let defaultPageSize = 20
        static let maxRetryCount = 3
    }

    enum UserDefaults {
        static let lastViewedItineraryKey = "ItineraryPlan.lastViewedItinerary"
        static let sortPreferenceKey = "ItineraryPlan.sortPreference"
    }
}
```

#### **🎨 设计系统常量使用指南**

**优先级原则：**
1. **首选全局设计系统**: 优先使用 `DesignSystemConstants` 中的标准值
2. **避免硬编码**: 禁止在代码中直接使用魔法数字
3. **语义化使用**: 使用语义化别名而非具体数值

**使用示例：**
```swift
// ✅ 正确：使用设计系统常量
.padding(DesignSystemConstants.Spacing.standard)
.cornerRadius(DesignSystemConstants.CornerRadius.card)
withAnimation(DesignSystemConstants.standardEaseAnimation) { ... }

// ❌ 错误：硬编码数值
.padding(16)
.cornerRadius(12)
withAnimation(.easeInOut(duration: 0.3)) { ... }

// ❌ 错误：在模块中重复定义
enum MyModuleConstants {
    static let standardSpacing: CGFloat = 16  // 应使用全局常量
}
```

**迁移指导：**
- 遇到 `@deprecated` 警告时，按提示迁移到新常量
- 新功能开发直接使用 `DesignSystemConstants`
- 定期清理废弃的常量定义

#### **🔧 使用最佳实践**

**1. 命名规范**
- 使用描述性名称，避免缩写
- 包含单位信息（如时间、尺寸）
- 使用一致的命名模式

**2. 类型安全**
- 使用具体类型而非泛型
- 利用Swift的类型系统避免错误
- 为复杂类型提供类型别名

**3. 文档注释**
- 为复杂常量添加注释
- 说明使用场景和注意事项
- 提供示例值的含义

**4. 分组逻辑**
- 按功能职责分组，不按类型分组
- 保持分组的一致性
- 避免过度细分导致查找困难

**Service层设计原则：**
- ✅ **业务聚合**: 将多个全局Service的调用组合成功能专用的业务用例
- ✅ **数据处理**: 处理功能专用的数据转换和缓存逻辑
- ✅ **错误处理**: 统一处理功能相关的错误和异常情况
- ✅ **依赖注入**: 通过构造函数注入全局Core服务

**Service层实现示例：**

#### **功能专用Service示例**
```swift
// Features/Discover/Services/DiscoverService.swift
class DiscoverService {
    private let networkService: NetworkService
    private let cacheService: CacheService
    private let locationService: LocationService

    init(
        networkService: NetworkService = .shared,
        cacheService: CacheService = .shared,
        locationService: LocationService = .shared
    ) {
        self.networkService = networkService
        self.cacheService = cacheService
        self.locationService = locationService
    }

    // 发现功能专用的业务逻辑
    func loadDiscoverItems() async throws -> [DiscoverItem] {
        let location = try await locationService.getCurrentLocation()

        if let cachedItems = cacheService.getDiscoverItems(for: location) {
            return cachedItems
        }

        let items = try await networkService.fetchDiscoverItems(near: location)
        let processedItems = processDiscoverItems(items)
        cacheService.saveDiscoverItems(processedItems, for: location)

        return processedItems
    }

    func searchDiscoverItems(query: String) async throws -> [DiscoverItem] {
        let items = try await networkService.searchDiscoverItems(query: query)
        return processDiscoverItems(items)
    }

    private func processDiscoverItems(_ items: [DiscoverItem]) -> [DiscoverItem] {
        return items.filter { $0.isValid }
                   .sorted { $0.createdAt > $1.createdAt }
    }
}
```

#### **主视图Service示例（可选）**
```swift
// Services/ExploreJourneyService.swift
class ExploreJourneyService {
    private let discoverService: DiscoverService
    private let exploreService: ExploreService

    init(
        discoverService: DiscoverService = DiscoverService(),
        exploreService: ExploreService = ExploreService()
    ) {
        self.discoverService = discoverService
        self.exploreService = exploreService
    }

    // 跨功能的协调业务逻辑
    func loadRecommendedJourney() async throws -> RecommendedJourney {
        async let discoverItems = discoverService.loadDiscoverItems()
        async let exploreItems = exploreService.loadExploreItems()

        let (discover, explore) = try await (discoverItems, exploreItems)

        return RecommendedJourney(discover: discover, explore: explore)
    }
}
```

### 🚀 Features层 - 子功能实现
```
Scenes/ModuleName/Features/
├── 🎯 PrimaryFeature/           # 主要功能模块
│   ├── {Feature}View.swift      # 功能主入口视图
│   ├── Services/                # 功能专用服务
│   │   └── {Feature}Service.swift # 功能业务服务
│   ├── ViewModels/              # 功能视图模型
│   │   └── {Feature}ViewModel.swift
│   ├── Views/                   # 功能内容视图
│   │   └── {Feature}ContentView.swift
│   ├── Components/              # 功能专用组件
│   └── Features/                # 子功能集合（当需要子模块时）
│       ├── SubFeatureA/         # 子功能A
│       │   ├── {SubFeature}View.swift    # 子功能入口视图
│       │   ├── Services/        # 子功能专用服务
│       │   ├── ViewModels/      # 子功能视图模型
│       │   └── Views/           # 子功能内容视图
│       └── SubFeatureB/         # 子功能B
├── 🔍 FeatureB/                 # 功能模块B
│   ├── Features/                # 功能B的子功能（如需要）
│   ├── Components/              # 功能B专用组件
│   ├── Services/                # 功能B专用服务
│   ├── ViewModels/              # 功能B视图模型
│   └── Views/                   # 功能B视图
└── 👤 FeatureC/                 # 功能模块C
    ├── Features/                # 功能C的子功能（如需要）
    ├── Components/              # 功能C专用组件
    ├── Services/                # 功能C专用服务
    ├── ViewModels/              # 功能C视图模型
    └── Views/                   # 功能C视图
```

**组织原则：**
- **🎯 功能内聚**: 按完整的业务功能分组，功能相关的Service就近放置
- **🏗️ 内部分层**: 每个功能模块内部都有完整的分层结构
- **🚫 模块隔离**: 功能模块间不直接依赖，通过全局Core层或Shared层通信
- **📦 功能完整**: 每个功能包含自己的Service、ViewModel、View等所有组件
- **🔧 就近原则**: 功能专用的Service放在功能目录内，便于维护和理解

### 🤝 Shared层 - 模块内共享组件
```
Shared/
├── 📐 Layout/                   # 布局组件 (容器、面板、导航等)
├── 🎨 UI/                      # UI状态组件
│   ├── States/                 # 状态视图 (空状态、错误状态、加载状态)
│   ├── Overlays/               # 覆盖层 (弹窗、遮罩、提示等)
│   └── Modifiers/              # 修饰器 (动画、效果、样式等)
├── 🧱 Components/               # 通用组件 (输入框、按钮、选择器等)
├── 🛠️ Utils/                    # 工具类 (视图工具、计算工具等)
└── 📊 Models/                   # 跨模块共享的数据模型
```

**共享标准：**
- **🔢 复用频率**: 被2个或以上功能模块使用的组件
- **🎨 状态通用**: 通用的UI状态展示组件（加载、错误、空状态等）
- **📐 布局复用**: 可在多个场景使用的布局和容器组件
- **🛠️ 工具共享**: 跨模块的工具类、扩展和辅助功能

### 🚪 Views层 - 模块入口视图
```
Views/
└── 📱 ModuleNameView.swift      # 模块主入口视图 (对外统一接口)
```

**职责定义：**
- **🚪 统一入口**: 模块的主入口和协调者
- **🔗 功能整合**: 整合各功能模块，提供统一接口
- **📱 对外接口**: 作为模块对外的唯一接口
- **🎯 路由管理**: 处理模块内部的导航和路由
- 处理模块级别的导航和状态

## 🏗️ 子模块组织规范

### 📊 子模块层级规则

#### **🎯 基本原则**
1. **最大深度限制**: 从项目根目录计算，目录结构不超过7层，推荐6层以内
2. **命名一致性**: 统一使用`Features`作为子模块容器
3. **职责清晰**: 每个子模块都有明确的功能边界
4. **依赖简单**: 子模块间避免复杂依赖关系
5. **嵌套控制**: Features嵌套建议不超过2层（Feature → SubFeature → 停止）

#### **📁 标准子模块结构**
```
FeatureModule/
├── Core/                        # 功能基础设施（可选）
├── Features/                    # 子功能集合（当需要时）
│   ├── SubFeatureA/            # 子功能A（遵循标准子模块结构）
│   │   ├── Features/           # SubFeatureA的子功能（如需要）
│   │   ├── Components/         # SubFeatureA专用组件
│   │   ├── Services/           # SubFeatureA专用服务
│   │   ├── ViewModels/         # SubFeatureA业务逻辑
│   │   └── Views/              # SubFeatureA视图
│   └── SubFeatureB/            # 子功能B（同样遵循标准结构）
├── Components/                  # 主功能专用组件
├── Services/                    # 主功能专用服务
├── ViewModels/                  # 主功能业务逻辑
└── Views/                       # 主功能视图
```

#### **🎯 子模块结构原则**
- **统一性**: 每个子模块都遵循相同的目录结构
- **递归性**: Features可以嵌套，每层都保持标准结构
- **职责分离**: Components存放UI组件，Features存放子功能
- **可扩展性**: 任何层级的子模块都可以继续拆分
- **嵌套限制**: Features嵌套建议不超过2层，避免过度复杂化

#### **📏 嵌套深度指导**
```
✅ 推荐结构（2层嵌套）:
Features/MainFeature/Features/SubFeature/

❌ 避免结构（3层嵌套）:
Features/MainFeature/Features/SubFeature/Features/DeepFeature/

🎯 替代方案：将深层功能提升为同级Features
Features/MainFeature/
Features/SubFeature/
Features/DeepFeature/
```

#### **🔍 Components vs Features 区分原则**

##### **📦 Components（组件）**
- **定义**: 在主视图中直接使用的UI元素和展示组件
- **使用场景**:
  - 主视图中的列表、卡片、头部等UI元素
  - 数据展示和简单交互（点击、展开、滚动等）
  - 可复用的UI组件
  - 不包含复杂业务逻辑的视图元素
- **特征**:
  - 主要负责数据展示
  - 交互相对简单
  - 可以在主视图中直接组合使用
  - 通常不需要独立的导航

##### **🚀 Features（子功能）**
- **定义**: 独立的业务功能模块，包含完整的功能流程
- **使用场景**:
  - 需要独立导航的功能页面
  - 包含多个步骤或页面的业务流程
  - 有独立状态管理需求的功能
  - 可能被其他模块复用的完整功能
- **特征**:
  - 有独立的导航入口
  - 包含完整的业务流程
  - 有自己的状态管理和服务
  - 遵循完整的子模块结构（Features/Components/Services/ViewModels/Views）

#### **📋 Service架构设计原则**

##### **主视图Service放置规则**
- **位置**: 模块根目录的`Services/`目录
- **命名**: `{ModuleName}Service.swift`
- **职责**: 聚合主视图所需数据，直接调用全局Service（不调用子功能Service）
- **调用**: 主视图ViewModel → 主视图Service → 全局Service

##### **子功能Service调用规则**
- **独立性**: 子功能Service应该独立，不依赖主视图Service
- **数据获取**: 直接调用全局Service或模块Shared Service
- **通信方式**: 通过事件、依赖注入或Shared层与主视图通信

#### **📋 实际应用示例**

##### **ItineraryPlan模块示例**
```
ItineraryPlan/
├── Models/                         # 模块共用数据模型
│   ├── ItineraryModel.swift       # ✅ 行程数据模型
│   ├── AttractionModel.swift      # ✅ 景点数据模型
│   └── FootprintModel.swift       # ✅ 足迹数据模型
├── Constants/                      # 模块专用常量
│   └── ItineraryPlanConstants.swift # ✅ 行程计划常量
├── Services/                       # 主视图专用服务
│   └── ItineraryPlanService.swift # ✅ 主视图数据聚合Service
├── ViewModels/                     # 主视图专用ViewModel
│   └── ItineraryPlanViewModel.swift # ✅ 主视图业务逻辑
├── Components/                     # 主视图专用组件
│   ├── ItineraryListView.swift    # ✅ Components: 行程列表展示
│   ├── ItineraryCardView.swift    # ✅ Components: 行程卡片展示
│   ├── FootprintsView.swift       # ✅ Components: 足迹统计展示
│   ├── ItineraryHeaderView.swift  # ✅ Components: 页面头部
│   └── EmptyItineraryView.swift   # ✅ Components: 空状态展示
├── Features/                       # 子功能模块
│   ├── NewItinerary/              # ✅ Features: 创建行程（独立业务流程）
│   │   ├── Features/              # NewItinerary的子功能
│   │   │   └── CreateItinerary/   # 创建行程的具体实现
│   │   ├── Components/            # NewItinerary专用组件
│   │   ├── Services/              # NewItinerary专用服务
│   │   │   └── NewItineraryService.swift # ✅ 创建行程Service
│   │   └── Views/                 # NewItinerary视图
│   ├── AllItineraries/            # ✅ Features: 所有行程（独立页面）
│   │   ├── ViewModels/
│   │   │   └── AllItinerariesViewModel.swift # ✅ 所有行程ViewModel
│   │   └── Views/
│   ├── ItineraryDetail/           # ✅ Features: 行程详情（独立页面）
│   └── Footprints/                # ✅ Features: 足迹管理（独立功能）
├── Shared/                         # 模块内共享
└── Views/                          # 模块入口视图
    └── ItineraryPlanView.swift     # 模块入口视图
```

##### **Service调用关系**
```
主视图: ItineraryPlanViewModel → ItineraryPlanService → NetworkService
子功能: NewItineraryViewModel → NewItineraryService → NetworkService
子功能: AllItinerariesViewModel → AllItinerariesService → NetworkService
```

##### **扁平化结构的优势**
- **结构统一**: 模块根目录和子功能都遵循相同的目录组织原则
- **简洁明了**: 去除Core中间层，减少目录嵌套深度
- **职责清晰**: 每个目录的职责一目了然
- **易于导航**: 开发者能快速找到所需文件

##### **区分判断标准**
- **选择Components**: 如果是在主视图中直接使用的UI元素
- **选择Features**: 如果需要独立导航或包含完整业务流程

#### **🔧 Service设计最佳实践**

##### **主视图Service设计原则**
```swift
// ItineraryPlan/Services/ItineraryPlanService.swift
class ItineraryPlanService {
    private let networkService: NetworkServiceProtocol

    // ✅ 聚合主视图所需的所有数据
    func loadMainViewData() async throws -> MainViewData {
        async let itineraries = loadItineraries()
        async let footprints = loadFootprints()
        return try await MainViewData(
            itineraries: itineraries,
            footprints: footprints
        )
    }

    // ✅ 提供主视图级别的业务操作
    func refreshAllData() async throws -> MainViewData { ... }
}
```

##### **子功能Service独立性原则**
```swift
// ✅ 正确：子功能Service独立调用全局Service
class NewItineraryService {
    private let networkService: NetworkServiceProtocol

    func createItinerary(data: ItineraryCreationData) async throws -> ItineraryModel {
        return try await networkService.request(.post(APIPaths.itineraryCreate).body(data))
    }
}

// ❌ 错误：子功能Service依赖主视图Service
class NewItineraryService {
    private let itineraryPlanService: ItineraryPlanService // ❌ 不推荐
}
```

#### **🔄 协调器 vs Service 的区别**

**如果需要复杂的状态协调或导航管理，应该使用协调器（Coordinator）而不是Service：**

```swift
// ✅ 正确：使用协调器处理复杂的状态协调
class ItineraryStateCoordinator: ObservableObject {
    @Published var itineraries: [ItineraryModel] = []
    @Published var selectedItinerary: ItineraryModel?

    func addItinerary(_ itinerary: ItineraryModel) {
        itineraries.append(itinerary)
    }

    func updateItinerary(_ itinerary: ItineraryModel) {
        // 协调多个视图的状态更新
    }
}

// ✅ 正确：使用导航协调器处理复杂导航
class ItineraryNavigationCoordinator: ObservableObject {
    @Published var navigationPath = NavigationPath()

    func navigateToNewItinerary() {
        navigationPath.append(ItineraryDestination.newItinerary)
    }
}
```

**协调器 vs Service 职责对比：**

| 方面 | Service | Coordinator |
|------|---------|-------------|
| **职责** | 数据获取和业务逻辑 | 状态协调和流程管理 |
| **关注点** | 单一功能的实现 | 多个组件间的协调 |
| **使用场景** | 数据聚合、API调用 | 导航管理、状态同步 |
| **生命周期** | 按需创建 | 长期存在 |

#### **🚦 子模块创建判断标准**
- **功能复杂度**: 超过3个相关视图文件时考虑拆分为Features
- **导航需求**: 需要独立导航的功能应创建为Features
- **业务完整性**: 包含完整业务流程的功能应创建为Features
- **复用价值**: 可能被其他模块复用的功能应独立为Features
- **团队协作**: 多人同时开发同一功能时需要拆分为Features

#### **📋 子模块命名规范**
- **主功能**: 使用功能名称，如`Discover`、`NewItinerary`
- **子功能**: 使用具体功能描述，如`DiscoverDetail`、`AttractionSelection`
- **容器目录**: 统一使用`Features`作为子功能容器，每层子模块都遵循标准结构
- **视图层级**: 遵循主入口→内容实现的命名模式

#### **📁 Components命名规范**
- **展示组件**: 使用`{Content}View`格式，如`ItineraryListView`、`ItineraryCardView`
- **交互组件**: 使用`{Action}{Element}View`格式，如`CreateButtonView`、`FilterBarView`
- **状态组件**: 使用`{State}View`格式，如`EmptyStateView`、`LoadingView`
- **容器组件**: 使用`{Content}Container`格式，如`ItineraryContentContainer`

#### **🚀 Features命名规范**
- **业务功能**: 使用业务名称，如`NewItinerary`、`ItineraryDetail`
- **管理功能**: 使用`{Entity}Management`格式，如`UserManagement`
- **流程功能**: 使用`{Process}Flow`格式，如`PaymentFlow`
- **入口视图**: 使用`{FeatureName}View`格式，如`NewItineraryView`

## 🔗 依赖关系与数据流规范

### 📊 依赖层次图
```
🚪 Views     ←→  🧩 Features  ←→  🏛️ Core
   ↓              ↓              ↓
🤝 Shared    ←→  🤝 Shared    ←→  🤝 Shared
```

### 🎯 依赖规则
- **⬆️ 向上依赖**: 下层可以依赖上层，上层禁止依赖下层
- **🚫 横向隔离**: 同层级模块间不直接依赖，通过Shared层通信
- **🔄 共享复用**: 跨模块功能通过Shared层实现
- **📱 单向数据流**: 数据从Core → Features → Views单向流动

### ⚡ 性能优化规则
- **懒加载**: 大型组件使用`LazyVStack`/`LazyHStack`
- **状态最小化**: 只在必要时使用`@StateObject`，优先`@State`
- **缓存策略**: 网络数据、图片、计算结果必须缓存
- **内存管理**: 避免循环引用，及时释放大对象

## 📝 命名规范标准

### 🎯 文件命名规范 (PascalCase + 后缀)

#### **📱 视图文件命名规范**
```swift
// ✅ 主入口视图（功能根目录）
DiscoverView.swift              // 主功能入口视图
DiscoverDetailView.swift        // 子功能入口视图
NewItineraryView.swift          // 主功能入口视图

// ✅ 内容实现视图（Views目录下）
DiscoverContentView.swift       // 主功能内容视图
DiscoverDetailContentView.swift // 子功能内容视图
NewItineraryContentView.swift   // 主功能内容视图

// ✅ 专用组件视图（Components目录下）
DiscoverFilterBarView.swift     // 功能专用组件
DestinationButtonView.swift     // 功能专用组件
UserProfileAvatarView.swift     // 功能专用组件

// ❌ 错误示例
DiscoverFeatureView.swift       // 包含Feature字样
DiscoverDetailFeatureView.swift // 包含Feature字样
DiscoverFilter.swift           // 缺少View后缀
DestinationBtn.swift           // 使用缩写
```

#### **🏗️ 其他文件命名规范**
```swift
// ✅ 正确示例
UserProfileViewModel.swift      // 视图模型
UserProfileModel.swift         // 数据模型
LocationService.swift          // 服务类
ToastManager.swift             // 管理器
DateUtils.swift               // 工具类
Cacheable.swift               // 协议 (形容词形式)
ItineraryConstants.swift      // 常量文件

// ❌ 错误示例
userProfile.swift             // 小写开头
UserProfileVC.swift          // 使用缩写
UserProfileHelper.swift     // 使用Helper术语
```

### 📁 目录命名规范 (PascalCase + 业务术语)
```
✅ 推荐使用
NewItinerary/                 # 业务功能名称
UserProfile/                  # 用户概念
Components/                   # 组件分类
Features/                     # 子功能容器
States/                      # 状态分类

❌ 避免使用
Controllers/                 # 技术术语
Helpers/                     # 模糊概念
Utils/                       # 仅在Core层使用
Components/Features/         # 避免在Components中放置子功能
```

### 🎯 视图层级命名体系

#### **📊 命名层级规则**
```
功能模块/
├── {ModuleName}View.swift           # 🚪 主入口视图
├── Features/
│   └── {SubFeature}/
│       ├── {SubFeature}View.swift   # 🚪 子功能入口视图
│       └── Views/
│           └── {SubFeature}ContentView.swift  # 🎨 子功能内容视图
├── Components/
│   └── {Component}View.swift        # 🧩 功能专用组件
└── Views/
    └── {ModuleName}ContentView.swift # 🎨 主功能内容视图
```

#### **🎨 视图命名模式**
| 视图类型 | 命名模式 | 位置 | 职责 |
|---------|---------|------|------|
| 🚪 主入口视图 | `{ModuleName}View` | 功能根目录 | 协调整个功能模块 |
| 🚪 子功能入口 | `{SubFeature}View` | 子功能根目录 | 协调子功能模块 |
| 🎨 主内容视图 | `{ModuleName}ContentView` | Views目录 | 主功能具体实现 |
| 🎨 子内容视图 | `{SubFeature}ContentView` | 子功能Views目录 | 子功能具体实现 |
| 🧩 专用组件 | `{Component}View` | Components目录 | 特定UI组件 |

#### **📋 实际命名示例**
```swift
// ✅ Discover模块示例
Discover/
├── DiscoverView.swift                    # 主入口
├── Features/
│   └── DiscoverDetail/
│       ├── DiscoverDetailView.swift      # 子功能入口
│       └── Views/
│           └── DiscoverDetailContentView.swift  # 子功能内容
├── Components/
│   ├── DiscoverFilterBarView.swift       # 专用组件
│   └── DestinationButtonView.swift       # 专用组件
└── Views/
    └── DiscoverContentView.swift         # 主功能内容

// ✅ NewItinerary模块示例
NewItinerary/
├── NewItineraryView.swift                # 主入口
├── Features/
│   └── CreateItinerary/                  # 子功能（遵循标准结构）
│       ├── CreateItineraryView.swift     # 子功能入口
│       ├── Components/                   # CreateItinerary专用组件
│       ├── Services/                     # CreateItinerary专用服务
│       ├── ViewModels/                   # CreateItinerary业务逻辑
│       └── Views/
│           └── CreateItineraryContentView.swift # 子功能内容
├── Components/                           # NewItinerary专用组件
└── Views/
    └── NewItineraryContentView.swift     # 主功能内容
```

### 🎨 组件分类与归属原则
| 组件类型 | 归属位置 | 使用范围 | 示例 |
|---------|---------|---------|------|
| 🌍 全局通用 | `Core/UI/Components/` | 整个应用 | 基础UI组件、全局状态视图 |
| 🏢 模块专用 | `Features/Module/Components/` | 单个模块 | 功能特定的UI组件 |
| 🤝 跨模块共享 | `Shared/Components/` | 2-3个模块 | 通用输入组件、选择器等 |
| 📊 状态视图 | `Shared/UI/States/` | 状态展示 | 错误状态、加载状态、空状态 |

## 🎨 UI/UX 设计与性能规范

### 🎯 设计系统标准
```swift
// ✅ 推荐：使用系统设计语言
struct PrimaryButton: View {
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.accentColor)
                .cornerRadius(12)
        }
        .buttonStyle(.plain) // 避免重复动画
    }
}

// ❌ 避免：过度自定义
struct CustomButton: View {
    // 复杂的自定义实现...
}
```

### 📱 交互体验规范
| 交互类型 | 推荐方案 | 性能要求 | 示例 |
|---------|---------|---------|------|
| 🔄 弹窗展示 | `.sheet`, `.popover` | < 16ms响应 | 筛选面板、详情页 |
| 📊 状态反馈 | 系统Loading组件 | 即时显示 | 网络请求、数据加载 |
| 🎭 动画效果 | `.animation(.easeInOut)` | 60fps流畅 | 页面转场、状态变化 |
| 👆 手势处理 | 系统标准手势 | 无冲突 | 滑动返回、长按菜单 |

### ⚡ 性能优化清单
#### 🚀 渲染性能
- **懒加载列表**: 超过20项使用`LazyVStack`
- **图片优化**: 网络图片缓存 + 尺寸压缩
- **视图复用**: 相似组件抽取为可复用组件
- **状态最小化**: 减少不必要的`@State`变量

#### 💾 内存管理
```swift
// ✅ 正确：避免循环引用
class ViewModel: ObservableObject {
    private weak var coordinator: Coordinator?

    func loadData() {
        dataService.fetch { [weak self] result in
            self?.handleResult(result)
        }
    }
}

// ❌ 错误：可能导致内存泄漏
class ViewModel: ObservableObject {
    private let coordinator: Coordinator // 强引用

    func loadData() {
        dataService.fetch { result in // 强引用self
            self.handleResult(result)
        }
    }
}
```

### 📐 布局设计规范
- **🛡️ 安全区域**: 内容区域遵循`safeAreaInsets`，地图可扩展至边缘
- **📏 间距系统**: 8pt基础单位 (8, 16, 24, 32, 40pt)
- **🎨 图标规范**: SF Symbols优先，统一尺寸(.font(.title3))
- **🌗 深色模式**: 所有颜色支持自动适配深色模式

## 🔄 状态管理与数据流

### 📊 状态管理层次
```swift
// 🏛️ 全局状态 - EnvironmentObject
@main
struct LuyeaApp: App {
    @StateObject private var appState = AppStateManager()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
        }
    }
}

// 🎬 页面状态 - StateObject
struct ProfileView: View {
    @StateObject private var viewModel = ProfileViewModel()

    var body: some View {
        // 页面内容
    }
}

// 🧩 组件状态 - State
struct SearchBar: View {
    @State private var searchText = ""

    var body: some View {
        TextField("搜索", text: $searchText)
    }
}

// 🤝 父子通信 - Binding
struct ParentView: View {
    @State private var isPresented = false

    var body: some View {
        ChildView(isPresented: $isPresented)
    }
}
```

### 🚫 状态管理禁忌
- **❌ 全局单例**: 除特殊场景外禁止使用全局单例
- **❌ 过度共享**: 不要将局部状态提升为全局状态
- **❌ 状态冗余**: 避免在多个地方维护相同状态
- **❌ 深层传递**: 超过3层的状态传递应使用Environment

## 🛠️ 开发实践指南

### 🚀 新建模块标准流程
```bash
# 1️⃣ 创建扁平化模块目录结构
mkdir -p NewModule/{Models,Constants,Utils,Services,ViewModels,Components,Features,Shared,Views}

# 2️⃣ 初始化基础文件
touch NewModule/Models/.gitkeep
touch NewModule/Constants/NewModuleConstants.swift
touch NewModule/Views/NewModuleView.swift

# 3️⃣ 按需创建主视图相关文件
touch NewModule/Services/NewModuleService.swift
touch NewModule/ViewModels/NewModuleViewModel.swift

# 4️⃣ 创建主要子功能模块
mkdir -p NewModule/Features/MainFeature/{Services,ViewModels,Views,Components}
touch NewModule/Features/MainFeature/MainFeatureView.swift
touch NewModule/Features/MainFeature/Services/MainFeatureService.swift

# 5️⃣ 按需创建更多子功能模块（当功能复杂时）
mkdir -p NewModule/Features/SubFeatureName/{Features,Components,Services,ViewModels,Views}
```

### 🔄 现有模块重构流程
```bash
# 1️⃣ 识别需要拆分的复杂功能
# 2️⃣ 创建Features目录
mkdir -p ExistingFeature/Features

# 3️⃣ 移动子功能到Features
mv ExistingFeature/SubFeature ExistingFeature/Features/

# 4️⃣ 更新引用关系
# 5️⃣ 验证构建和功能完整性
```

### 🎯 ViewModel使用Service最佳实践

#### **✅ 推荐方式：通过依赖注入使用Service**
```swift
class DiscoverViewModel: ObservableObject {
    @Published var items: [DiscoverItem] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let discoverService: DiscoverService

    init(discoverService: DiscoverService = DiscoverService()) {
        self.discoverService = discoverService
    }

    @MainActor
    func loadData() {
        isLoading = true

        Task {
            do {
                let items = try await discoverService.loadDiscoverItems()
                self.items = items
                self.isLoading = false
            } catch {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
}
```

#### **🎯 核心原则**
- **依赖注入**: ViewModel通过构造函数注入Service，便于测试
- **职责分离**: ViewModel专注视图状态管理，Service处理业务逻辑
- **错误处理**: 统一在ViewModel中处理Service返回的错误
- **异步处理**: 使用async/await处理异步Service调用

### 🎨 组件设计最佳实践

#### ✅ 优秀组件示例
```swift
// 原子化设计 - 单一职责
struct CircularImageView: View {
    let imageURL: URL?
    let size: CGFloat
    let placeholder: String

    var body: some View {
        CachedAsyncImage(url: imageURL) { image in
            image.resizable()
        } placeholder: {
            Text(placeholder)
                .foregroundColor(.secondary)
        }
        .frame(width: size, height: size)
        .clipShape(Circle())
    }
}

// Props传递 - 避免内部状态
struct ListItemRow: View {
    let title: String
    let subtitle: String
    let imageURL: URL?
    let onTap: () -> Void

    var body: some View {
        HStack {
            CircularImageView(
                imageURL: imageURL,
                size: 44,
                placeholder: title.prefix(1).uppercased()
            )
            VStack(alignment: .leading) {
                Text(title)
                    .font(.headline)
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .onTapGesture(perform: onTap)
    }
}
```

### 🔄 重构安全指南
1. **📊 影响分析**: 使用Xcode查找引用，评估变更影响范围
2. **🧪 测试先行**: 重构前确保有足够的测试覆盖
3. **⚡ 小步快跑**: 每次只重构一个小模块，立即验证
4. **📝 文档同步**: 重构完成后立即更新相关文档
5. **🔍 代码审查**: 重构代码必须经过团队审查

## 🧪 测试与质量保证

### 📋 测试覆盖要求
- **🎯 核心业务逻辑**: 单元测试覆盖率 > 90%
- **🖱️ 关键用户流程**: UI测试覆盖率 > 80%
- **⚡ 性能关键路径**: 性能测试 + 基准测试
- **🔒 数据安全**: 敏感数据处理必须有测试

### 🚀 性能监控指标
```swift
// 性能关键代码必须有注释
func loadData() async {
    // 性能要求: < 500ms 完成数据加载
    // 内存要求: 峰值 < 50MB
    let startTime = CFAbsoluteTimeGetCurrent()

    defer {
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        Logger.performance.info("数据加载耗时: \(timeElapsed)s")
    }

    // 实现代码...
}
```

## 📚 Git工作流与协作

### 🌿 分支策略
- **🎯 main**: 生产就绪代码，受保护分支
- **🚀 develop**: 开发集成分支，功能测试
- **✨ feature/**: 功能开发分支，命名格式: `feature/用户故事-简短描述`
- **🐛 bugfix/**: 问题修复分支，命名格式: `bugfix/问题描述`

### 💬 提交规范
```bash
# 提交格式: <类型>(<范围>): <描述>
feat(module): 添加新功能模块
fix(core): 修复核心服务问题
refactor(arch): 重构架构层级
perf(ui): 优化UI渲染性能
docs(arch): 更新架构文档
```

## 📈 架构扩展与演进指南

### 🆕 添加新模块流程
1. **📋 需求分析**: 明确模块功能边界和依赖关系
2. **🏗️ 架构设计**: 创建扁平化目录结构
3. **💻 核心实现**: 实现模块主入口视图和核心功能
4. **🔗 集成测试**: 更新全局导航和路由，验证集成效果

### 🔄 重构现有模块策略
1. **🔍 问题识别**: 评估当前架构问题和技术债务
2. **📊 影响评估**: 分析重构对其他模块的影响范围
3. **⚡ 渐进实施**: 制定分步骤重构计划，小步快跑
4. **✅ 质量保证**: 验证功能完整性和性能表现

### 🧱 组件提取与复用
1. **🔍 重复识别**: 使用工具识别重复代码模式
2. **💰 价值评估**: 评估提取组件的复用价值和维护成本
3. **🎨 接口设计**: 设计灵活通用的组件接口
4. **📦 层级迁移**: 将组件迁移到合适的Shared层级

## 🎯 实施检查清单

### 新模块创建检查清单
- [ ] 创建扁平化目录结构（Models/Constants/Services/ViewModels/Components/Features/Shared/Views）
- [ ] 创建模块共用数据模型（Models/）
- [ ] 创建模块专用常量（Constants/ModuleNameConstants.swift）
- [ ] 实现模块主入口视图（Views/ModuleNameView.swift）
- [ ] 按需创建主视图服务和ViewModel（Services/、ViewModels/）

### Constants定义检查清单
- [ ] 只定义模块专用的常量，避免全局通用常量
- [ ] 使用枚举按业务功能分组（Text/Layout/Animation/Config等）
- [ ] 常量命名具有描述性，包含类型和单位信息
- [ ] 避免定义只使用一次的常量
- [ ] 为复杂常量添加注释说明
- [ ] 验证常量不与全局Core/Constants重复
- [ ] 设置模块级别ViewModel（如需要）
- [ ] 按功能组织Features子模块，使用Features容器（如需要）
- [ ] 确保目录层级不超过7层，推荐6层以内
- [ ] 提取共享组件到Shared层
- [ ] 验证依赖关系符合规范
- [ ] 进行构建测试确保编译通过

### Service层组织检查清单
- [ ] 功能Service放在对应的Feature/Services/目录中
- [ ] 主视图专用Service（如需要）放在模块根目录的Services/中
- [ ] 功能Service通过依赖注入使用全局Service
- [ ] Service方法体现具体的业务用例，不是简单的数据传递
- [ ] Service处理功能专用的数据转换和缓存逻辑
- [ ] Service统一处理功能相关的错误和异常
- [ ] ViewModel通过依赖注入使用功能Service
- [ ] 避免ViewModel直接调用多个全局Service
- [ ] 功能间不直接依赖彼此的Service

### 子模块组织检查清单
- [ ] 子功能使用Features目录统一管理
- [ ] 确保每层子模块都遵循标准结构（Features/Components/Services/ViewModels/Views）
- [ ] Features嵌套不超过2层（Feature → SubFeature → 停止）
- [ ] 每个子模块职责单一明确
- [ ] 子模块命名清晰描述功能
- [ ] 子模块间依赖关系简单清晰
- [ ] 深层功能考虑提升为同级Features而非继续嵌套

### Components vs Features 检查清单
- [ ] Components只包含主视图中直接使用的UI元素
- [ ] Features包含需要独立导航的完整业务功能
- [ ] 没有将独立业务流程放在Components中
- [ ] 没有将简单UI组件创建为Features
- [ ] Components主要负责数据展示和简单交互
- [ ] Features包含完整的状态管理和业务逻辑

### Service架构检查清单
- [ ] 主视图Service放在模块根目录的Services/目录中
- [ ] 主视图Service负责聚合主视图所需的所有数据
- [ ] 子功能Service保持独立，不依赖主视图Service
- [ ] 子功能Service直接调用全局Service或模块Shared Service
- [ ] Service命名遵循{ModuleName}Service或{FeatureName}Service格式
- [ ] 避免Service之间的循环依赖
- [ ] 通过依赖注入管理Service之间的关系

### 视图命名规范检查清单
- [ ] 主入口视图使用`{ModuleName}View`格式
- [ ] 子功能入口视图使用`{SubFeature}View`格式
- [ ] 内容视图使用`{Name}ContentView`格式
- [ ] 组件视图使用`{Component}View`格式
- [ ] 业务服务使用`{Module}Service`格式
- [ ] 避免使用`Feature`字样在文件名中
- [ ] 所有视图文件都以`View`结尾
- [ ] 文件名与struct名称保持一致

### 重构现有模块检查清单
- [ ] 分析现有架构问题和改进点
- [ ] 制定详细的重构计划和步骤
- [ ] 备份现有代码和功能
- [ ] 创建新的目录结构
- [ ] 逐步迁移文件到新结构
- [ ] 更新所有import引用关系
- [ ] 验证功能完整性和一致性
- [ ] 进行全面的构建和功能测试

### 代码审查检查清单
- [ ] 目录结构符合扁平化架构规范
- [ ] 文件命名遵循PascalCase规范
- [ ] 依赖关系符合向上依赖原则
- [ ] 组件职责单一明确
- [ ] 共享组件正确放置在Shared层
- [ ] 代码注释充分清晰
- [ ] 性能优化措施到位
- [ ] 错误处理完善

## 📚 架构实施参考

### ✅ 良好实践案例
1. **标准模块架构**: 扁平化架构实现
   - 模块根目录管理共享模型、服务和常量
   - Features层按功能域组织，内部结构清晰
   - Shared层提供跨模块复用的UI组件和工具
   - Views层作为模块统一入口，协调各功能

2. **组件设计模式**: 清晰的组件职责分离
   - 原子化组件设计，单一职责明确
   - 通过Props传递数据，避免内部状态耦合
   - 合理的组件层次和复用策略

### ❌ 反面案例避免
- **技术导向分组**: 避免按技术类型分组（如：Views/、ViewModels/、Models/）
- **过深目录嵌套**: 避免超过7层的目录结构，推荐最多6层
- **模块直接依赖**: 避免功能模块间的直接依赖关系
- **代码重复**: 避免重复代码未提取到共享层
- **职责混乱**: 避免文件职责不明确或过于复杂
- **命名不一致**: 避免同时使用Features和Components作为子模块容器
- **结构不一致**: 避免子模块结构不规范，确保每层都遵循标准子模块结构
- **Components职责越界**: 避免在Components中放置独立的业务功能
- **Features职责不足**: 避免将简单的UI组件创建为Features
- **导航混乱**: 避免Components包含独立的导航逻辑

## 🔄 持续改进

### 架构演进
- 定期评估架构适应性
- 根据业务发展调整结构
- 重构技术债务
- 优化性能瓶颈

### 团队协作
- 统一架构理解
- 代码审查标准
- 最佳实践分享
- 新人培训指导

---







## 📈 架构价值与优势

### 🎯 **核心价值**
- **🏗️ 结构清晰**: 混合架构设计，全局分层+模块扁平化，便于理解和维护
- **🔧 职责明确**: 每层都有清晰的职责边界和接口定义
- **🚀 开发高效**: 支持团队并行开发，提高开发效率
- **🧪 易于测试**: 分层设计便于单元测试和集成测试
- **📈 可扩展性**: 支持项目长期发展和功能扩展

### 🚀 **持续改进方向**
- **⚡ 性能优化**: 持续监控和优化关键路径性能
- **🧪 测试覆盖**: 提升自动化测试覆盖率
- **📚 文档完善**: 保持文档与代码同步更新
- **🤖 工具支持**: 开发架构检查和自动化工具
- **👥 团队培训**: 加强团队对架构规范的理解和执行

---

*本文档是Luyea项目架构的权威指南，采用分层模块化架构设计，确保项目的可维护性、可扩展性和团队协作效率。所有开发活动都应严格遵循此规范。*
