import SwiftUI

/// 美化的加载动画视图
/// 
/// 提供现代化、优雅的加载动画效果，支持自定义文案和样式。
/// 使用毛玻璃效果、渐变色彩和流畅动画，提升用户体验。
struct BeautifulLoadingView: View {
    
    // MARK: - Properties
    
    /// 主要文案
    let title: String
    
    /// 描述文案
    let subtitle: String?
    
    /// 是否显示
    let isVisible: Bool
    
    /// 主题色
    let accentColor: Color
    
    // MARK: - Initialization
    
    /// 初始化美化加载视图
    /// 
    /// - Parameters:
    ///   - title: 主要文案，默认为"加载中"
    ///   - subtitle: 描述文案，可选
    ///   - isVisible: 是否显示
    ///   - accentColor: 主题色，默认使用系统主题色
    init(
        title: String = "加载中",
        subtitle: String? = "正在获取最新数据...",
        isVisible: Bool = true,
        accentColor: Color = .accentColor
    ) {
        self.title = title
        self.subtitle = subtitle
        self.isVisible = isVisible
        self.accentColor = accentColor
    }
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 渐变遮罩背景
            LinearGradient(
                colors: [
                    Color.primary.opacity(0.05),
                    Color.primary.opacity(0.15),
                    Color.primary.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // 加载卡片
            VStack(spacing: 16) {
                // 自定义旋转动画
                ZStack {
                    // 外圈装饰环
                    Circle()
                        .stroke(
                            LinearGradient(
                                colors: [accentColor.opacity(0.2), Color.clear],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                        .frame(width: 44, height: 44)
                    
                    // 主要进度环
                    Circle()
                        .trim(from: 0, to: 0.7)
                        .stroke(
                            LinearGradient(
                                colors: [accentColor, accentColor.opacity(0.3)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            style: StrokeStyle(lineWidth: 3, lineCap: .round)
                        )
                        .frame(width: 36, height: 36)
                        .rotationEffect(.degrees(isVisible ? 360 : 0))
                        .animation(
                            Animation.linear(duration: 1.0).repeatForever(autoreverses: false),
                            value: isVisible
                        )
                    
                    // 中心点
                    Circle()
                        .fill(accentColor)
                        .frame(width: 6, height: 6)
                        .scaleEffect(isVisible ? 1.2 : 0.8)
                        .animation(
                            Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true),
                            value: isVisible
                        )
                }
                
                // 文字和描述
                VStack(spacing: 6) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                        .foregroundColor(.primary)
                    
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(.secondary)
                            .opacity(0.8)
                    }
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
            .background(
                // 毛玻璃效果背景
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(
                        color: Color.primary.opacity(0.08),
                        radius: 12,
                        x: 0,
                        y: 6
                    )
            )
            .scaleEffect(isVisible ? 1.0 : 0.8)
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(
                .spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.3),
                value: isVisible
            )
        }
    }
}

// MARK: - Convenience Extensions

extension BeautifulLoadingView {
    /// 创建简单的加载视图
    static func simple(isVisible: Bool = true) -> BeautifulLoadingView {
        BeautifulLoadingView(
            title: "加载中",
            subtitle: nil,
            isVisible: isVisible
        )
    }
    
    /// 创建数据加载视图
    static func dataLoading(isVisible: Bool = true) -> BeautifulLoadingView {
        BeautifulLoadingView(
            title: "加载中",
            subtitle: "正在获取最新数据...",
            isVisible: isVisible
        )
    }
    
    /// 创建搜索加载视图
    static func searching(isVisible: Bool = true) -> BeautifulLoadingView {
        BeautifulLoadingView(
            title: "搜索中",
            subtitle: "正在查找相关内容...",
            isVisible: isVisible
        )
    }
}

// MARK: - Preview

#Preview("美化加载动画") {
    VStack(spacing: 40) {
        BeautifulLoadingView.simple()
            .frame(height: 200)
        
        BeautifulLoadingView.dataLoading()
            .frame(height: 200)
        
        BeautifulLoadingView.searching()
            .frame(height: 200)
    }
    .padding()
}
