import SwiftUI
import Combine

/// TabBar 统一状态管理器
///
/// 全局唯一的TabBar状态管理器，负责管理TabBar的所有状态和行为。
/// 提供统一的TabBar控制接口，确保状态管理的一致性和可维护性。
/// 禁止外部直接修改TabBar状态，所有操作必须通过此管理器进行。
///
/// 核心设计原则：
/// - 单一数据源：所有TabBar状态都由此管理器统一管理
/// - 封装性：外部无法直接修改内部状态，只能通过公开方法操作
/// - 一致性：确保所有TabBar相关的UI组件状态同步
/// - 可观察性：提供完整的状态变化监听机制
/// - 线程安全：所有操作都在主线程执行
///
/// 主要功能：
/// - 统一管理TabBar的显示隐藏状态
/// - 管理当前选中的标签页
/// - 提供状态变化的监听机制
/// - 控制TabBar相关的动画效果
/// - 维护TabBar的布局参数
/// - 集成全局UI状态管理
///
/// 使用示例：
/// ```swift
/// // 隐藏TabBar
/// TabBarStateManager.shared.hide()
///
/// // 显示TabBar
/// TabBarStateManager.shared.show()
///
/// // 切换标签页
/// TabBarStateManager.shared.selectTab(.exploreJourney)
///
/// // 监听状态变化
/// TabBarStateManager.shared.$isVisible
///     .sink { isVisible in
///         // 处理显示状态变化
///     }
/// ```
@MainActor
final class TabBarStateManager: ObservableObject {

    // MARK: - Singleton

    /// 全局唯一实例
    ///
    /// 使用单例模式确保整个应用只有一个TabBar状态管理器，
    /// 避免状态分散和不一致的问题。
    static let shared = TabBarStateManager()

    // MARK: - Published Properties

    /// TabBar是否可见
    ///
    /// 控制TabBar的显示和隐藏状态。
    /// 此属性为只读，外部无法直接修改，只能通过公开方法操作。
    /// 状态变化会自动触发相关UI更新和动画效果。
    @Published private(set) var isVisible: Bool = true

    /// 当前选中的标签页
    ///
    /// 管理用户当前所在的标签页。
    /// 切换标签页时会自动处理相关的数据加载和UI更新。
    /// 外部可以监听此属性的变化，但建议通过selectTab方法进行修改。
    @Published var selectedTab: AppTab = .itineraryPlan

    /// TabBar动画状态
    ///
    /// 指示TabBar是否正在执行显示/隐藏动画。
    /// 在动画期间可以防止重复操作，确保动画的流畅性。
    /// 此属性为只读，由管理器内部自动维护。
    @Published private(set) var isAnimating: Bool = false
    
    // MARK: - Private Properties

    /// 状态变化的发布者
    ///
    /// 用于向外部发布TabBar状态变化事件。
    /// 支持监听显示隐藏状态变化和标签页切换事件。
    private let stateChangeSubject = PassthroughSubject<TabBarStateChange, Never>()

    /// 动画配置
    ///
    /// 统一管理TabBar相关的动画参数，确保动画效果的一致性。
    private let animationConfig = TabBarAnimationConfig()

    /// 用于存储Combine订阅
    ///
    /// 管理内部状态监听的订阅生命周期。
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    /// 私有初始化方法，确保单例模式
    ///
    /// 初始化时会设置内部状态观察机制，
    /// 确保状态变化能够正确传播和处理。
    private init() {
        setupStateObservation()
    }
    
    // MARK: - Public Methods - 显示隐藏控制

    /// 显示TabBar
    ///
    /// 以动画方式显示TabBar。如果TabBar已经可见，则不执行任何操作。
    /// 这是显示TabBar的唯一正确方式，禁止外部直接修改isVisible属性。
    /// 动画完成后会发送状态变化通知。
    ///
    /// - Parameter animated: 是否使用动画，默认为true
    func show(animated: Bool = true) {
        // 防止重复操作
        guard !isVisible else { return }

        if animated {
            performAnimatedStateChange(to: true)
        } else {
            isVisible = true
            notifyStateChange(.visibility(isVisible))
        }
    }

    /// 隐藏TabBar
    ///
    /// 以动画方式隐藏TabBar。如果TabBar已经隐藏，则不执行任何操作。
    /// 这是隐藏TabBar的唯一正确方式，禁止外部直接修改isVisible属性。
    /// 动画完成后会发送状态变化通知。
    ///
    /// - Parameter animated: 是否使用动画，默认为true
    func hide(animated: Bool = true) {
        // 防止重复操作
        guard isVisible else { return }

        if animated {
            performAnimatedStateChange(to: false)
        } else {
            isVisible = false
            notifyStateChange(.visibility(isVisible))
        }
    }

    /// 切换TabBar显示状态
    ///
    /// 在显示和隐藏之间切换TabBar状态。
    /// 根据当前状态自动选择显示或隐藏操作。
    ///
    /// - Parameter animated: 是否使用动画，默认为true
    func toggle(animated: Bool = true) {
        if isVisible {
            hide(animated: animated)
        } else {
            show(animated: animated)
        }
    }

    /// 设置TabBar可见性
    ///
    /// 根据指定的可见性状态显示或隐藏TabBar。
    /// 提供更直观的可见性控制接口。
    ///
    /// - Parameters:
    ///   - visible: 目标可见性状态
    ///   - animated: 是否使用动画，默认为true
    func setVisible(_ visible: Bool, animated: Bool = true) {
        if visible {
            show(animated: animated)
        } else {
            hide(animated: animated)
        }
    }
    
    // MARK: - Public Methods - 标签页控制

    /// 选择指定的标签页
    ///
    /// 切换到指定的标签页，并触发相关的数据加载和UI更新。
    /// 如果已经是当前标签页，则不执行任何操作。
    /// 切换标签页时会自动显示TabBar（如果当前隐藏）。
    ///
    /// - Parameters:
    ///   - tab: 要选择的标签页
    ///   - animated: 是否使用动画，默认为true
    func selectTab(_ tab: AppTab, animated: Bool = true) {
        // 防止重复操作
        guard selectedTab != tab else { return }

        let previousTab = selectedTab

        // 执行标签页切换
        if animated {
            withAnimation(animationConfig.tabSwitchAnimation) {
                selectedTab = tab
            }
        } else {
            selectedTab = tab
        }

        // 发送状态变化通知
        notifyStateChange(.tabSelection(from: previousTab, to: tab))

        // 切换标签页时自动显示TabBar
        if !isVisible {
            show(animated: animated)
        }
    }

    // MARK: - Public Methods - 状态查询

    /// 获取当前TabBar是否可见
    ///
    /// 提供只读的可见性状态查询接口。
    ///
    /// - Returns: TabBar当前是否可见
    var currentVisibility: Bool {
        return isVisible
    }

    /// 获取当前选中的标签页
    ///
    /// 提供只读的当前标签页查询接口。
    ///
    /// - Returns: 当前选中的标签页
    var currentTab: AppTab {
        return selectedTab
    }

    /// 获取当前是否正在执行动画
    ///
    /// 提供只读的动画状态查询接口。
    ///
    /// - Returns: 是否正在执行动画
    var currentAnimationState: Bool {
        return isAnimating
    }

    // MARK: - Public Methods - 状态监听

    /// 获取状态变化的发布者
    ///
    /// 用于监听TabBar状态的变化，包括显示隐藏和标签页切换。
    /// 外部可以通过此发布者监听所有TabBar状态变化事件。
    ///
    /// - Returns: 状态变化的发布者
    func stateChangePublisher() -> AnyPublisher<TabBarStateChange, Never> {
        stateChangeSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Private Methods - 内部状态管理

    /// 设置状态观察
    ///
    /// 初始化内部状态监听机制，确保状态变化能够正确传播。
    /// 主要监听标签页切换事件，实现自动显示TabBar的逻辑。
    private func setupStateObservation() {
        // 监听选中标签页的变化
        // 当用户切换标签页时，如果TabBar当前隐藏，则自动显示
        $selectedTab
            .dropFirst() // 忽略初始值，避免初始化时的不必要操作
            .sink { _ in
                // 标签页切换时的自动显示逻辑已在selectTab方法中处理
                // 这里保留监听机制，用于未来可能的扩展需求
            }
            .store(in: &cancellables)
    }

    /// 执行带动画的状态变化
    ///
    /// 处理TabBar显示隐藏的动画逻辑。
    /// 确保动画期间不会有重复操作，保证动画的流畅性。
    ///
    /// - Parameter newState: 目标可见性状态
    private func performAnimatedStateChange(to newState: Bool) {
        // 防止动画期间的重复操作
        guard !isAnimating else { return }

        // 设置动画状态标记
        isAnimating = true

        // 执行动画
        withAnimation(animationConfig.visibilityAnimation) {
            isVisible = newState
        }

        // 动画完成后重置动画状态并发送通知
        DispatchQueue.main.asyncAfter(deadline: .now() + animationConfig.animationDuration) { [weak self] in
            guard let self = self else { return }
            self.isAnimating = false
            self.notifyStateChange(.visibility(newState))
        }
    }

    /// 发送状态变化通知
    ///
    /// 向外部发布TabBar状态变化事件。
    /// 所有状态变化都会通过此方法统一发布。
    ///
    /// - Parameter change: 状态变化类型
    private func notifyStateChange(_ change: TabBarStateChange) {
        stateChangeSubject.send(change)
    }
}

// MARK: - Supporting Types

/// TabBar状态变化类型
///
/// 定义TabBar可能发生的状态变化事件类型。
/// 用于状态变化通知和监听机制。
enum TabBarStateChange: Equatable {
    /// 显示状态变化
    /// - Parameter Bool: 新的可见性状态（true为显示，false为隐藏）
    case visibility(Bool)

    /// 标签页选择变化
    /// - Parameters:
    ///   - from: 之前选中的标签页
    ///   - to: 新选中的标签页
    case tabSelection(from: AppTab, to: AppTab)

    /// 状态变化的描述信息
    var description: String {
        switch self {
        case .visibility(let isVisible):
            return "TabBar可见性变化: \(isVisible ? "显示" : "隐藏")"
        case .tabSelection(let from, let to):
            return "标签页切换: \(from.title) -> \(to.title)"
        }
    }
}

/// TabBar动画配置
///
/// 统一管理TabBar相关的动画参数，确保动画效果的一致性。
/// 所有动画时长和效果都在此处集中配置。
private struct TabBarAnimationConfig {
    /// 显示隐藏动画
    /// 使用缓入缓出动画，提供平滑的显示隐藏效果
    let visibilityAnimation: Animation = .easeInOut(duration: 0.3)

    /// 标签页切换动画
    /// 使用弹簧动画，提供自然的切换效果
    let tabSwitchAnimation: Animation = .spring(response: 0.35, dampingFraction: 0.7)

    /// 动画持续时间
    /// 用于计算动画完成时间，确保状态同步
    let animationDuration: TimeInterval = 0.3
}

// MARK: - Environment Integration

/// TabBar状态环境键
///
/// 用于在SwiftUI环境值系统中传递TabBar状态管理器。
/// 确保所有子视图都能访问到统一的状态管理器实例。
struct TabBarStateEnvironmentKey: @preconcurrency EnvironmentKey {
    @MainActor
    static let defaultValue: TabBarStateManager = TabBarStateManager.shared
}

extension EnvironmentValues {
    /// TabBar状态管理器环境值
    ///
    /// 通过环境值机制访问TabBar状态管理器。
    /// 子视图可以通过@Environment(\.tabBarState)获取管理器实例。
    var tabBarState: TabBarStateManager {
        get { self[TabBarStateEnvironmentKey.self] }
        set { self[TabBarStateEnvironmentKey.self] = newValue }
    }
}

// MARK: - Convenience Extensions

extension TabBarStateManager {

    /// 便捷方法：立即隐藏TabBar（无动画）
    ///
    /// 适用于需要立即隐藏TabBar的场景，如全屏播放等。
    func hideImmediately() {
        hide(animated: false)
    }

    /// 便捷方法：立即显示TabBar（无动画）
    ///
    /// 适用于需要立即显示TabBar的场景。
    func showImmediately() {
        show(animated: false)
    }

    /// 便捷方法：切换到指定标签页（无动画）
    ///
    /// 适用于需要立即切换标签页的场景。
    ///
    /// - Parameter tab: 目标标签页
    func selectTabImmediately(_ tab: AppTab) {
        selectTab(tab, animated: false)
    }

    /// 便捷方法：重置到默认状态
    ///
    /// 将TabBar重置为默认状态：显示且选中第一个标签页。
    /// 适用于应用启动或重置场景。
    ///
    /// - Parameter animated: 是否使用动画，默认为true
    func resetToDefault(animated: Bool = true) {
        selectTab(.itineraryPlan, animated: animated)
        show(animated: animated)
    }
}


