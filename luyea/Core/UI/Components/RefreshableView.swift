import SwiftUI

/// 支持下拉刷新的通用修饰器，避免ScrollView嵌套
struct RefreshableView<Content: View>: View {
    let content: () -> Content
    let onRefresh: () async -> Void

    /// 初始化
    /// - Parameters:
    ///   - content: 内容视图构建闭包
    ///   - onRefresh: 下拉刷新回调
    init(@ViewBuilder content: @escaping () -> Content,
         onRefresh: @escaping () async -> Void) {
        self.content = content
        self.onRefresh = onRefresh
    }

    // MARK: - Body
    var body: some View {
        content()
            .refreshable {
                await onRefresh()
            }
    }
}

// MARK: - UIScrollView 扩展（监听滚动到底部）
extension UIScrollView {
    static let scrollViewDidScrollToBottomNotification = Notification.Name("scrollViewDidScrollToBottom")
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let offsetY = scrollView.contentOffset.y
        let contentHeight = scrollView.contentSize.height
        let screenHeight = scrollView.frame.size.height
        if offsetY > contentHeight - screenHeight - 50 {
            NotificationCenter.default.post(name: UIScrollView.scrollViewDidScrollToBottomNotification, object: nil)
        }
    }
}

// MARK: - 滚动偏移量 PreferenceKey
private struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
} 