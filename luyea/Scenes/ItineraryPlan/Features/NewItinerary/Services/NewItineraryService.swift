import Foundation
import Combine

// MARK: - Supporting Types

/// 新建行程错误类型
enum NewItineraryError: LocalizedError {
    case invalidData([String])
    case networkError(Error)
    case validationFailed(String)
    case draftNotFound

    var errorDescription: String? {
        switch self {
        case .invalidData(let errors):
            return "数据验证失败: \(errors.joined(separator: ", "))"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .validationFailed(let message):
            return "验证失败: \(message)"
        case .draftNotFound:
            return "未找到草稿数据"
        }
    }
}

/// 验证结果
struct ValidationResult {
    let isValid: Bool
    let errors: [String]
}

/// 行程创建结果
struct ItineraryCreationResult {
    let itineraryId: String
    let status: String
    let estimatedCost: Double?
    let suggestedOptimizations: [String]
}

/// 费用估算
struct CostEstimation {
    let totalCost: Double
    let breakdown: [CostBreakdownItem]
    let currency: String
}

/// 费用明细项
struct CostBreakdownItem: Codable {
    let category: String
    let amount: Double
    let description: String
}

// MARK: - Request/Response Models

/// 创建行程请求
struct CreateItineraryRequest: Codable {
    let title: String
    let departureLocation: String
    let departureDate: String
    let destinations: [DestinationRequest]
    let attractions: [AttractionRequest]
    let tripDays: Int
    let tripType: String
    let budget: String
    let preferences: ItineraryPreferences
}

/// 目的地请求
struct DestinationRequest: Codable {
    let id: String
    let name: String
}

/// 景点请求
struct AttractionRequest: Codable {
    let id: String
    let name: String
}

/// 行程偏好
struct ItineraryPreferences: Codable {
    let recommendationEnabled: Bool
}

/// 创建行程响应
struct ItineraryCreationResponse: Codable {
    let itineraryId: String
    let status: String
    let estimatedCost: Double?
    let suggestions: [String]
}

/// 景点搜索响应
struct AttractionSearchResponse: Codable {
    let attractions: [AttractionModel]
    let totalCount: Int
}

/// 景点推荐响应
struct AttractionRecommendationResponse: Codable {
    let recommendations: [AttractionModel]
    let totalCount: Int
}

/// 热门目的地响应
struct PopularDestinationsResponse: Codable {
    let destinations: [Destination]
    let totalCount: Int
}

/// 费用估算请求
struct CostEstimationRequest: Codable {
    let destinations: [String]
    let attractions: [String]
    let tripDays: Int
    let tripType: String
}

/// 费用估算响应
struct CostEstimationResponse: Codable {
    let totalCost: Double
    let breakdown: [CostBreakdownItem]
    let currency: String
}

/// 新建行程服务协议
protocol NewItineraryServiceProtocol {
    /// 创建行程
    func createItinerary(_ data: ItineraryCreationData) async throws -> ItineraryCreationResult
    
    /// 搜索景点
    func searchAttractions(query: String, location: String?) async throws -> [AttractionModel]
    
    /// 获取推荐景点
    func getRecommendedAttractions(for destinations: [Destination]) async throws -> [AttractionModel]
    
    /// 获取热门目的地
    func getPopularDestinations() async throws -> [Destination]
    
    /// 验证行程数据
    func validateItineraryData(_ data: ItineraryCreationData) -> ValidationResult
    
    /// 获取预估费用
    func getEstimatedCost(for data: ItineraryCreationData) async throws -> CostEstimation
    
    /// 保存草稿
    func saveDraft(_ data: ItineraryCreationData) async throws
    
    /// 加载草稿
    func loadDraft() async throws -> ItineraryCreationData?
}

/// 新建行程服务实现
final class NewItineraryService: NewItineraryServiceProtocol {
    
    // MARK: - Dependencies
    
    /// 网络服务
    private let networkService: NetworkServiceProtocol
    
    /// 本地存储服务
    private let storageService: FileManagerService
    
    // MARK: - Cache Properties
    
    /// 搜索结果缓存
    private var searchCache: [String: [AttractionModel]] = [:]
    
    /// 推荐景点缓存
    private var recommendationCache: [String: [AttractionModel]] = [:]
    
    /// 热门目的地缓存
    private var popularDestinationsCache: [Destination] = []
    
    /// 缓存时间戳
    private var cacheTimestamps: [String: Date] = [:]
    
    /// 缓存有效期（秒）
    private let cacheValidityDuration: TimeInterval = 300 // 5分钟
    
    // MARK: - Initialization
    
    init(networkService: NetworkServiceProtocol = NetworkService.shared,
         storageService: FileManagerService = FileManagerService.shared) {
        self.networkService = networkService
        self.storageService = storageService
    }
    
    // MARK: - Public Methods
    
    /// 创建行程
    func createItinerary(_ data: ItineraryCreationData) async throws -> ItineraryCreationResult {
        // 验证数据
        let validation = validateItineraryData(data)
        guard validation.isValid else {
            throw NewItineraryError.invalidData(validation.errors)
        }
        
        // 构建请求数据
        let requestData = buildCreateItineraryRequest(data)
        
        // 发送创建请求
        let response: ItineraryCreationResponse = try await networkService.request(
            .post(APIPaths.createItinerary)
            .body(try JSONEncoder().encode(requestData))
        )
        
        // 清除草稿
        try? await clearDraft()
        
        return ItineraryCreationResult(
            itineraryId: response.itineraryId,
            status: response.status,
            estimatedCost: response.estimatedCost,
            suggestedOptimizations: response.suggestions
        )
    }
    
    /// 搜索景点
    func searchAttractions(query: String, location: String?) async throws -> [AttractionModel] {
        // 检查缓存
        let cacheKey = "\(query)_\(location ?? "")"
        if let cachedResults = getCachedSearchResults(for: cacheKey) {
            return cachedResults
        }
        
        // 构建搜索请求
        var request = APIRequest
            .get(APIPaths.searchAttractions)
            .query("q", query)
        
        if let location = location {
            request = request.query("location", location)
        }
        
        // 执行搜索
        let response: AttractionSearchResponse = try await networkService.request(request)
        
        // 更新缓存
        updateSearchCache(cacheKey, results: response.attractions)
        
        return response.attractions
    }
    
    /// 获取推荐景点
    func getRecommendedAttractions(for destinations: [Destination]) async throws -> [AttractionModel] {
        let destinationIds = destinations.map { $0.id }.joined(separator: ",")
        
        // 检查缓存
        if let cachedRecommendations = getCachedRecommendations(for: destinationIds) {
            return cachedRecommendations
        }
        
        // 构建推荐请求
        let request = APIRequest
            .get(APIPaths.getRecommendedAttractions)
            .query("destinations", destinationIds)
        
        let response: AttractionRecommendationResponse = try await networkService.request(request)
        
        // 更新缓存
        updateRecommendationCache(destinationIds, results: response.recommendations)
        
        return response.recommendations
    }
    
    /// 获取热门目的地
    func getPopularDestinations() async throws -> [Destination] {
        // 检查缓存
        if let cachedDestinations = getCachedPopularDestinations() {
            return cachedDestinations
        }
        
        // 从网络获取
        let response: PopularDestinationsResponse = try await networkService.request(
            .get(APIPaths.getPopularDestinations)
        )
        
        // 更新缓存
        updatePopularDestinationsCache(response.destinations)
        
        return response.destinations
    }
    
    /// 验证行程数据
    func validateItineraryData(_ data: ItineraryCreationData) -> ValidationResult {
        // 直接使用模型的验证方法，避免重复代码
        let (isValid, errors) = data.validate()
        return ValidationResult(isValid: isValid, errors: errors)
    }
    
    /// 获取预估费用
    func getEstimatedCost(for data: ItineraryCreationData) async throws -> CostEstimation {
        let requestData = CostEstimationRequest(
            destinations: data.destinations.map { $0.id },
            attractions: data.attractions.map { $0.id },
            tripDays: data.tripDays,
            tripType: data.tripType.apiValue
        )
        
        let response: CostEstimationResponse = try await networkService.request(
            .post(APIPaths.estimateItineraryCost)
            .body(try JSONEncoder().encode(requestData))
        )
        
        return CostEstimation(
            totalCost: response.totalCost,
            breakdown: response.breakdown,
            currency: response.currency
        )
    }
    
    /// 保存草稿
    func saveDraft(_ data: ItineraryCreationData) async throws {
        let draftData = try JSONEncoder().encode(data)
        try storageService.writeData(draftData, fileName: "itinerary_draft.json")
    }

    /// 加载草稿
    func loadDraft() async throws -> ItineraryCreationData? {
        guard storageService.fileExists(fileName: "itinerary_draft.json") else {
            return nil
        }

        let draftData = try storageService.readData(fileName: "itinerary_draft.json")
        return try JSONDecoder().decode(ItineraryCreationData.self, from: draftData)
    }
    
    // MARK: - Private Methods
    
    /// 构建创建行程请求
    private func buildCreateItineraryRequest(_ data: ItineraryCreationData) -> CreateItineraryRequest {
        return CreateItineraryRequest(
            title: generateItineraryTitle(data),
            departureLocation: data.departureLocation,
            departureDate: data.departureDateString,
            destinations: data.destinations.map { DestinationRequest(id: $0.id, name: $0.name) },
            attractions: data.attractions.map { AttractionRequest(id: $0.id, name: $0.name) },
            tripDays: data.tripDays,
            tripType: data.tripType.apiValue,
            budget: data.budgetString,
            preferences: ItineraryPreferences(
                recommendationEnabled: data.recommendationEnabled
            )
        )
    }
    
    /// 生成行程标题
    private func generateItineraryTitle(_ data: ItineraryCreationData) -> String {
        let destinations = data.destinations.map { $0.name }
        let attractions = data.attractions.map { $0.name }
        
        if !destinations.isEmpty {
            return "\(destinations.joined(separator: "、"))\(data.tripDays)日游"
        } else if !attractions.isEmpty {
            return "\(attractions.prefix(2).joined(separator: "、"))等\(data.tripDays)日游"
        } else {
            return "\(data.tripDays)日游"
        }
    }
    
    /// 清除草稿
    private func clearDraft() async throws {
        try? storageService.deleteFile(fileName: "itinerary_draft.json")
    }
    
    // MARK: - Cache Management
    
    /// 获取缓存的搜索结果
    private func getCachedSearchResults(for key: String) -> [AttractionModel]? {
        guard isCacheValid(for: "search_\(key)") else { return nil }
        return searchCache[key]
    }
    
    /// 获取缓存的推荐结果
    private func getCachedRecommendations(for key: String) -> [AttractionModel]? {
        guard isCacheValid(for: "recommendation_\(key)") else { return nil }
        return recommendationCache[key]
    }
    
    /// 获取缓存的热门目的地
    private func getCachedPopularDestinations() -> [Destination]? {
        guard isCacheValid(for: "popular_destinations") else { return nil }
        return popularDestinationsCache.isEmpty ? nil : popularDestinationsCache
    }
    
    /// 检查缓存是否有效
    private func isCacheValid(for key: String) -> Bool {
        guard let timestamp = cacheTimestamps[key] else { return false }
        return Date().timeIntervalSince(timestamp) < cacheValidityDuration
    }
    
    /// 更新搜索缓存
    private func updateSearchCache(_ key: String, results: [AttractionModel]) {
        searchCache[key] = results
        cacheTimestamps["search_\(key)"] = Date()
    }
    
    /// 更新推荐缓存
    private func updateRecommendationCache(_ key: String, results: [AttractionModel]) {
        recommendationCache[key] = results
        cacheTimestamps["recommendation_\(key)"] = Date()
    }
    
    /// 更新热门目的地缓存
    private func updatePopularDestinationsCache(_ destinations: [Destination]) {
        popularDestinationsCache = destinations
        cacheTimestamps["popular_destinations"] = Date()
    }
    
    /// 清除所有缓存
    func clearAllCache() {
        searchCache.removeAll()
        recommendationCache.removeAll()
        popularDestinationsCache.removeAll()
        cacheTimestamps.removeAll()
    }
}
