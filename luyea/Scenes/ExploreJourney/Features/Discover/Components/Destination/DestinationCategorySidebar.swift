import SwiftUI

struct DestinationCategorySidebar: View {
    let categories: [String]
    @Binding var selectedIndex: Int

    var body: some View {
        VStack(alignment: .center, spacing: 8) {
            ForEach(categories.indices, id: \.self) { idx in
                Button(action: { selectedIndex = idx }) {
                    Text(categories[idx])
                        .font(.system(size: 16, weight: selectedIndex == idx ? .bold : .regular))
                        .foregroundColor(selectedIndex == idx ? .primary : Color(.systemGray3))
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 2)
                }
                .buttonStyle(PlainButtonStyle())
            }
            Spacer()
        }
        .padding(.vertical, 8)
    }
} 