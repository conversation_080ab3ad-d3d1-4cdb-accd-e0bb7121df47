import Foundation

/// LocationManager预加载服务
///
/// 专门负责在应用启动时预加载LocationManager，实现饿汉式加载
/// 提升位置服务的使用性能，后台静默执行，用户无感知
final class LocationPreloadService {
    
    // MARK: - Singleton
    
    /// 单例实例
    static let shared = LocationPreloadService()
    
    // MARK: - Properties
    
    /// 预加载状态
    private(set) var isPreloaded = false
    
    // MARK: - Initialization
    
    private init() {
        Log.info("📍 LocationPreloadService初始化完成")
    }
    
    // MARK: - Public Methods
    
    /// 开始预加载LocationManager
    /// 后台静默执行，用户无感知
    /// - Parameter completion: 预加载完成回调（可选）
    func startPreloading(completion: (() -> Void)? = nil) {
        guard !isPreloaded else {
            Log.debug("📍 LocationManager已预加载，跳过重复操作")
            completion?()
            return
        }
        
        Log.info("📍 开始预加载LocationManager...")
        let startTime = CFAbsoluteTimeGetCurrent()
        
        Task {
            // 预加载LocationManager，实现饿汉式加载
            LocationManager.preload()
            
            await MainActor.run {
                let loadTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
                self.isPreloaded = true
                
                Log.success("📍 LocationManager预加载完成，耗时: \(String(format: "%.2f", loadTime))ms")
                completion?()
            }
        }
    }
    
    /// 检查预加载状态
    /// - Returns: 是否已完成预加载
    func checkPreloadStatus() -> Bool {
        return isPreloaded
    }
}
