import Foundation
import Combine

/// 全部行程服务协议
protocol AllItinerariesServiceProtocol {
    /// 获取分页行程数据
    func fetchItineraries(page: Int, pageSize: Int, status: String?) async throws -> PaginationResponse<ItineraryModel>

    /// 刷新行程数据
    func refreshItineraries(status: String?) async throws -> PaginationResponse<ItineraryModel>

    /// 合并分页数据
    func mergePageData(existing: [ItineraryModel], newPage: PaginationResponse<ItineraryModel>, isRefresh: Bool) -> [ItineraryModel]

    /// 计算加载更多的阈值
    func shouldLoadMore(currentIndex: Int, totalItems: Int, threshold: Int) -> Bool

    /// 处理网络错误
    func handleNetworkError(_ error: Error) -> AllItinerariesError
}

/// 全部行程服务实现
final class AllItinerariesService: AllItinerariesServiceProtocol {
    
    // MARK: - Dependencies
    
    /// 网络服务
    private let networkService: NetworkServiceProtocol
    
    // MARK: - Cache Properties
    
    /// 分页数据缓存 - 按状态分组
    private var paginationCache: [String: PaginationResponse<ItineraryModel>] = [:]
    
    /// 缓存时间戳 - 按状态分组
    private var cacheTimestamps: [String: Date] = [:]
    
    /// 缓存有效期（秒）
    private let cacheValidityDuration: TimeInterval = 180 // 3分钟
    
    // MARK: - Initialization
    
    init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }
    
    // MARK: - Public Methods
    
    /// 获取分页行程数据
    func fetchItineraries(page: Int, pageSize: Int, status: String?) async throws -> PaginationResponse<ItineraryModel> {
        let cacheKey = buildCacheKey(status: status, page: page)
        
        // 检查缓存（仅对第一页进行缓存）
        if page == 1, let cachedData = getCachedData(for: cacheKey) {
            return cachedData
        }
        
        // 构建查询参数
        var queryParams = [
            "page": String(page),
            "pageSize": String(pageSize)
        ]
        
        // 添加状态筛选参数
        if let status = status, status != "全部" {
            queryParams["status"] = status
        }
        
        // 构建API请求
        let request = APIRequest
            .get(APIPaths.allItinerariesList)
            .query(queryParams)
        
        // 执行请求
        let response: PaginationResponse<ItineraryModel> = try await networkService.request(request)
        
        // 缓存第一页数据
        if page == 1 {
            updateCache(response, for: cacheKey)
        }
        
        return response
    }
    
    /// 刷新行程数据
    func refreshItineraries(status: String?) async throws -> PaginationResponse<ItineraryModel> {
        // 清除相关缓存
        clearCache(for: status)
        
        // 获取第一页数据
        return try await fetchItineraries(page: 1, pageSize: 10, status: status)
    }
    
    // MARK: - Private Methods - Cache Management
    
    /// 构建缓存键
    private func buildCacheKey(status: String?, page: Int) -> String {
        let statusKey = status ?? "all"
        return "\(statusKey)_page_\(page)"
    }
    
    /// 获取缓存数据
    private func getCachedData(for key: String) -> PaginationResponse<ItineraryModel>? {
        guard isCacheValid(for: key) else { return nil }
        return paginationCache[key]
    }
    
    /// 检查缓存是否有效
    private func isCacheValid(for key: String) -> Bool {
        guard let timestamp = cacheTimestamps[key] else { return false }
        return Date().timeIntervalSince(timestamp) < cacheValidityDuration
    }
    
    /// 更新缓存
    private func updateCache(_ response: PaginationResponse<ItineraryModel>, for key: String) {
        paginationCache[key] = response
        cacheTimestamps[key] = Date()
    }
    
    /// 清除指定状态的缓存
    private func clearCache(for status: String?) {
        let statusKey = status ?? "all"
        
        // 清除该状态相关的所有缓存
        let keysToRemove = paginationCache.keys.filter { $0.hasPrefix(statusKey) }
        for key in keysToRemove {
            paginationCache.removeValue(forKey: key)
            cacheTimestamps.removeValue(forKey: key)
        }
    }
    
    /// 清除所有缓存
    private func clearAllCache() {
        paginationCache.removeAll()
        cacheTimestamps.removeAll()
    }
}

// MARK: - Business Logic Extensions

extension AllItinerariesService {
    
    /// 合并分页数据
    func mergePageData(
        existing: [ItineraryModel],
        newPage: PaginationResponse<ItineraryModel>,
        isRefresh: Bool
    ) -> [ItineraryModel] {
        if isRefresh {
            return newPage.items
        } else {
            return existing + newPage.items
        }
    }
    
    /// 验证分页参数
    func validatePaginationParams(page: Int, pageSize: Int) -> Bool {
        return page > 0 && pageSize > 0 && pageSize <= 100
    }
    
    /// 计算加载更多的阈值
    func shouldLoadMore(currentIndex: Int, totalItems: Int, threshold: Int) -> Bool {
        return currentIndex >= totalItems - threshold
    }
}

// MARK: - Error Handling

extension AllItinerariesService {
    
    /// 处理网络错误
    func handleNetworkError(_ error: Error) -> AllItinerariesError {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .noInternetConnection:
                return .networkUnavailable
            case .timeout:
                return .timeout
            case .serverError:
                return .serverError
            default:
                return .loadFailed(error)
            }
        } else {
            return .loadFailed(error)
        }
    }
}

// MARK: - Supporting Types

/// 全部行程相关错误类型
enum AllItinerariesError: Error, LocalizedError {
    case loadFailed(Error)
    case networkUnavailable
    case timeout
    case serverError
    case invalidPagination
    
    var errorDescription: String? {
        switch self {
        case .loadFailed(let error):
            return "数据加载失败: \(error.localizedDescription)"
        case .networkUnavailable:
            return "网络连接不可用，请检查网络设置"
        case .timeout:
            return "请求超时，请稍后重试"
        case .serverError:
            return "服务器暂时不可用，请稍后重试"
        case .invalidPagination:
            return "分页参数无效"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .networkUnavailable:
            return "请检查网络连接后重试"
        case .timeout, .serverError:
            return "请稍后重试"
        case .invalidPagination:
            return "请检查分页参数"
        case .loadFailed:
            return "请重试或联系客服"
        }
    }
}
