import Foundation

// MARK: - Mock配置协议

/// Mock配置协议，支持类型擦除
public protocol MockPaginationConfigProtocol {
    var name: String { get }
    var apiPath: String { get }
    var queryParams: [MockQueryParam] { get }

    /// 创建对应的策略
    func createStrategy() -> AnyMockPaginationStrategy
}

/// Mock统一的分页配置
public struct MockPaginationConfig<T: Codable>: MockPaginationConfigProtocol {
    public let name: String
    public let apiPath: String
    public let modelType: T.Type
    public let queryParams: [MockQueryParam]

    public init(name: String, apiPath: String, modelType: T.Type, queryParams: [MockQueryParam] = []) {
        self.name = name
        self.apiPath = apiPath
        self.modelType = modelType
        self.queryParams = queryParams
    }

    /// 自动创建策略，无需外部类型判断
    public func createStrategy() -> AnyMockPaginationStrategy {
        let strategy = MockStandardPaginationStrategy(config: self)
        return AnyMockPaginationStrategy(strategy)
    }
}

// MARK: - Mock策略配置管理

/// Mock分页策略配置管理器
///
/// 这个管理器负责处理配置的业务逻辑，具体的配置定义在 MockPaginationConfigs.swift 文件中
/// 日常维护时，通常只需要修改 MockPaginationConfigs.swift 文件中的配置即可
public struct MockPaginationStrategyConfigs {

    /// 所有可用的Mock策略实例
    /// 配置来源：MockPaginationConfigs.swift
    public static let strategies: [AnyMockPaginationStrategy] = {
        return mockPaginationConfigs.map { $0.createStrategy() }
    }()
    
    /// 根据API路径查找匹配的Mock策略
    /// - Parameter apiPath: API路径
    /// - Returns: 匹配的策略，如果没有找到则返回nil
    public static func findStrategy(for apiPath: String) -> AnyMockPaginationStrategy? {
        return strategies.first { $0.canHandle(apiPath: apiPath) }
    }
    
    /// 获取所有Mock策略的名称列表
    /// - Returns: 策略名称数组
    public static func getAllStrategyNames() -> [String] {
        return strategies.map { $0.name }
    }
    
    /// 验证Mock配置的完整性
    /// - Returns: 验证结果和错误信息
    public static func validateConfigs() -> (isValid: Bool, errors: [String]) {
        var errors: [String] = []

        // 检查是否有重复的API路径
        let apiPaths = mockPaginationConfigs.map { $0.apiPath }
        let uniquePaths = Set(apiPaths)
        if apiPaths.count != uniquePaths.count {
            errors.append("存在重复的API路径配置")
        }

        // 检查是否有重复的策略名称
        let names = mockPaginationConfigs.map { $0.name }
        let uniqueNames = Set(names)
        if names.count != uniqueNames.count {
            errors.append("存在重复的策略名称")
        }

        // 检查每个配置的查询参数
        for config in mockPaginationConfigs {
            let filterKeys = config.queryParams.filter { $0.operation == .filter }.map { $0.key }
            let sortKeys = config.queryParams.filter { $0.operation == .sort }.map { $0.key }

            if Set(filterKeys).count != filterKeys.count {
                errors.append("策略 '\(config.name)' 存在重复的筛选参数")
            }

            if Set(sortKeys).count != sortKeys.count {
                errors.append("策略 '\(config.name)' 存在重复的排序参数")
            }
        }

        return (errors.isEmpty, errors)
    }
}

// MARK: - Mock调试和日志支持

extension MockPaginationStrategyConfigs {
    
    public static func printConfigDetails() {
        Log.info("Mock分页配置: \(mockPaginationConfigs.count)个策略")

        for config in mockPaginationConfigs {
            let filterParams = config.queryParams.filter { $0.operation == .filter }
            let sortParams = config.queryParams.filter { $0.operation == .sort }
            Log.info("  \(config.name): \(config.apiPath) (筛选:\(filterParams.count), 排序:\(sortParams.count))")
        }

        let validation = validateConfigs()
        if !validation.isValid {
            Log.warning("Mock配置验证问题: \(validation.errors.joined(separator: ", "))")
        }
    }
}
