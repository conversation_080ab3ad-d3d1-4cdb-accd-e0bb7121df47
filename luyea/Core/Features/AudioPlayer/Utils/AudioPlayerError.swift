import SwiftUI

/// 音频播放器错误类型
///
/// 定义音频播放器过程中可能出现的各种错误类型。
enum AudioPlayerError: Error, LocalizedError {

    // MARK: - Error Types

    /// 音频信息无效
    case invalidAudioInfo(reason: String)

    /// 音频播放失败
    case audioPlaybackFailed(underlying: Error)

    // MARK: - LocalizedError Implementation

    /// 错误描述信息
    var errorDescription: String? {
        switch self {
        case .invalidAudioInfo(let reason):
            return "音频信息无效: \(reason)"

        case .audioPlaybackFailed(let underlying):
            return "音频播放失败: \(underlying.localizedDescription)"
        }
    }

    // MARK: - Error Recovery Properties

    /// 错误是否可恢复
    var isRecoverable: Bool {
        switch self {
        case .invalidAudioInfo:
            return false
        case .audioPlaybackFailed:
            return true
        }
    }

    /// 错误分类
    var category: ErrorCategory {
        switch self {
        case .invalidAudioInfo:
            return .audioInfo
        case .audioPlaybackFailed:
            return .playback
        }
    }
}

/// 错误分类
enum ErrorCategory {
    case audioInfo
    case playback
    case playerState
    case position
    case settings
}
