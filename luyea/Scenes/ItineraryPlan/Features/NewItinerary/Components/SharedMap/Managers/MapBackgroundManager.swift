import SwiftUI
import MapKit
import Combine

/// 地图背景管理器
///
/// 专门负责管理地图背景层的状态和行为。
/// 地图作为视觉背景，提供沉浸式体验，不参与业务逻辑。
///
/// 主要职责：
/// - 管理地图的显示状态和样式
/// - 处理地图的初始化和生命周期
/// - 控制地图的视觉效果和动画
/// - 为未来的地图交互功能预留接口
@MainActor
final class MapBackgroundManager: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 地图相机位置
    @Published var cameraPosition: MapCameraPosition = .automatic
    
    /// 地图是否就绪
    @Published var isReady = false
    
    /// 地图样式
    @Published var mapStyle: MapStyle = .standard(elevation: .flat, pointsOfInterest: .excludingAll)
    
    /// 是否显示用户位置
    @Published var showUserLocation = true
    
    /// 是否显示位置标签
    @Published var showLocationLabel = false
    
    /// 地图交互模式
    @Published var interactionModes: MapInteractionModes = .all
    
    // MARK: - Private Properties
    
    /// 位置管理器
    @ObservedObject private var locationManager = LocationManager.shared
    
    /// 是否已设置初始位置
    private var hasSetInitialPosition = false
    
    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init() {
        setupLocationObservation()
        Log.info("🗺️ 地图背景管理器初始化完成")
    }
    
    deinit {
        Log.debug("🗺️ 地图背景管理器已销毁")
    }
    
    // MARK: - Public Methods
    
    /// 初始化地图背景 - 简化版本
    func initialize() {
        Log.info("🗺️ 开始初始化地图背景")

        // 立即设置默认状态
        setDefaultMapPositionToChina()
        isReady = true

        // 后台处理位置相关逻辑
        Task.detached(priority: .utility) {
            await self.requestLocationPermission()
            await self.setInitialMapPosition()
        }

        Log.info("🗺️ 地图背景初始化完成")
    }
    
    /// 重置地图状态
    func reset() {
        hasSetInitialPosition = false
        showLocationLabel = false
        cameraPosition = .automatic
        isReady = false

        Log.debug("🗺️ 地图背景状态已重置")
    }
    
    /// 缩放到当前用户位置
    func zoomToCurrentUserLocation() {
        guard let coordinate = locationManager.location?.coordinate else {
            Log.warning("🗺️ 无法获取用户位置")
            return
        }
        
        let camera = MapCamera(
            centerCoordinate: coordinate,
            distance: 1000,
            heading: 0,
            pitch: 0
        )
        
        withAnimation(.easeInOut(duration: 0.8)) {
            cameraPosition = .camera(camera)
        }
        
        // 显示位置标签
        triggerLocationLabel()
        
        Log.debug("🗺️ 缩放到用户位置: \(coordinate)")
    }
    
    /// 设置地图样式
    func setMapStyle(_ style: MapStyle) {
        withAnimation(DesignSystemConstants.standardEaseAnimation) {
            mapStyle = style
        }
    }
    
    // MARK: - Private Methods
    
    /// 设置位置观察
    private func setupLocationObservation() {
        locationManager.$location
            .sink { [weak self] location in
                self?.handleLocationUpdate(location)
            }
            .store(in: &cancellables)
    }
    
    /// 请求位置权限 - 异步非阻塞版本
    private func requestLocationPermission() async {
        // 使用Task.detached避免阻塞主线程
        await Task.detached { [weak self] in
            await self?.locationManager.requestLocationPermission()
        }.value
    }
    
    /// 设置初始地图位置
    private func setInitialMapPosition() async {
        await MainActor.run {
            if let location = locationManager.location {
                // 有用户位置时，根据位置设置区域
                cameraPosition = .region(regionForCountry(coordinate: location.coordinate))
            } else {
                // 无用户位置时，默认显示中国
                setDefaultMapPositionToChina()
            }
            hasSetInitialPosition = true
        }
    }
    
    /// 处理位置更新
    private func handleLocationUpdate(_ newLocation: CLLocation?) {
        guard !hasSetInitialPosition, let location = newLocation else {
            if !hasSetInitialPosition {
                setDefaultMapPositionToChina()
                hasSetInitialPosition = true
            }
            return
        }
        
        withAnimation(.easeInOut(duration: 0.8)) {
            cameraPosition = .region(regionForCountry(coordinate: location.coordinate))
        }
        hasSetInitialPosition = true
    }
    
    /// 设置默认地图位置为中国
    private func setDefaultMapPositionToChina() {
        let chinaRegion = MKCoordinateRegion(
            center: NewItineraryConstants.Map.chinaCenter,
            span: NewItineraryConstants.Map.chinaSpan
        )
        cameraPosition = .region(chinaRegion)
    }
    
    /// 根据坐标确定区域
    private func regionForCountry(coordinate: CLLocationCoordinate2D) -> MKCoordinateRegion {
        let isChina = NewItineraryConstants.Map.chinaLatitudeRange.contains(coordinate.latitude) &&
                     NewItineraryConstants.Map.chinaLongitudeRange.contains(coordinate.longitude)

        if isChina {
            return MKCoordinateRegion(
                center: NewItineraryConstants.Map.chinaCenter,
                span: NewItineraryConstants.Map.chinaSpan
            )
        } else {
            return MKCoordinateRegion(
                center: coordinate,
                span: NewItineraryConstants.Map.chinaSpan
            )
        }
    }
    
    /// 触发位置标签显示
    private func triggerLocationLabel() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7, blendDuration: 0.3)) {
            showLocationLabel = true
        }
        
        Task {
            try? await Task.sleep(for: .seconds(2.5))
            await MainActor.run {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    showLocationLabel = false
                }
            }
        }
    }
}


