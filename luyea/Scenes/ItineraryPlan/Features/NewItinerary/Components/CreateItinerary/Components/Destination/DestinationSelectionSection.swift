import SwiftUI

/// 目的地选择区域组件
///
/// 功能特性：
/// - 目的地选择和展示
/// - 空状态和已选择状态的切换
/// - 目的地管理功能
struct DestinationSelectionSection: View {
    
    // MARK: - Properties

    @Binding var selectedDestinations: [Destination]
    @Binding var showDestinationPicker: Bool
    let onDestinationRemoved: ((Destination) -> Void)?

    // MARK: - State

    @State private var isShowingEmptyState: Bool = true
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            HStack {
                Text("选择目的地")
                    .font(.title2.weight(.bold))
                    .foregroundColor(.primary)

                Text("可选")
                    .font(.caption.weight(.medium))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.secondary.opacity(0.1))
                    .clipShape(Capsule())

                Spacer()
            }

            // 目的地选择/展示区域
            if isShowingEmptyState {
                destinationSelectionCard
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .scale(scale: 0.98)),
                        removal: .opacity.combined(with: .scale(scale: 1.02))
                    ))
            } else {
                selectedDestinationsView
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .scale(scale: 0.98)),
                        removal: .opacity.combined(with: .scale(scale: 1.02))
                    ))
            }
        }
        .animation(.easeInOut(duration: 0.15), value: isShowingEmptyState)
        .onAppear {
            isShowingEmptyState = selectedDestinations.isEmpty
        }
        .onChange(of: selectedDestinations) { _, newDestinations in
            // 使用更快的动画来避免视图重叠
            withAnimation(.easeInOut(duration: 0.15)) {
                isShowingEmptyState = newDestinations.isEmpty
            }
        }
    }
    
    // MARK: - Private Views
    
    /// 目的地选择卡片（空状态）
    private var destinationSelectionCard: some View {
        Button(action: { showDestinationPicker = true }) {
            HStack(spacing: 16) {
                // 图标
                Image(systemName: "location.circle")
                    .font(.title2)
                    .foregroundColor(.gray)

                // 内容
                VStack(alignment: .leading, spacing: 4) {
                    Text("选择目的地城市")
                        .font(.headline.weight(.medium))
                        .foregroundColor(.primary)

                    Text("支持多城市选择，为您推荐相关景点")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 箭头
                Image(systemName: "chevron.right")
                    .font(.caption.weight(.semibold))
                    .foregroundColor(.gray)
            }
            .padding(20)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 已选择目的地展示视图
    private var selectedDestinationsView: some View {
        VStack(spacing: 12) {
            // 编辑目的地按钮
            Button(action: { showDestinationPicker = true }) {
                HStack(spacing: 12) {
                    Image(systemName: "plus.circle")
                        .font(.title3)
                        .foregroundColor(.blue)

                    Text("添加或编辑目的地")
                        .font(.subheadline.weight(.medium))
                        .foregroundColor(.blue)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.caption.weight(.semibold))
                        .foregroundColor(.gray)
                }
                .padding(16)
                .background(Color.blue.opacity(0.05))
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .buttonStyle(PlainButtonStyle())

            // 已选择的目的地列表
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 8),
                GridItem(.flexible(), spacing: 8),
                GridItem(.flexible(), spacing: 8)
            ], spacing: 10) {
                ForEach(selectedDestinations) { destination in
                    SelectedDestinationCard(
                        destination: destination,
                        onRemove: {
                            // 通过回调让ViewModel处理移除逻辑，确保动画正确执行
                            onDestinationRemoved?(destination)
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .leading)),
                        removal: .scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .trailing))
                    ))
                }
            }
        }
    }
    
    // MARK: - Private Methods

    // 注意：移除逻辑已移至ViewModel中统一管理，确保动画效果一致
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // 空状态
        DestinationSelectionSection(
            selectedDestinations: .constant([]),
            showDestinationPicker: .constant(false),
            onDestinationRemoved: { destination in
                print("目的地被删除: \(destination.name)")
            }
        )

        // 有选择状态
        DestinationSelectionSection(
            selectedDestinations: .constant([
                Destination.samples[0],
                Destination.samples[1]
            ]),
            showDestinationPicker: .constant(false),
            onDestinationRemoved: { destination in
                print("目的地被删除: \(destination.name)")
            }
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
