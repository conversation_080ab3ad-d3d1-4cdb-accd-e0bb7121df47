import Foundation
import SwiftUI
import MapKit

/// 底部弹窗状态枚举
enum BottomSheetSnapState: String, CaseIterable {
    case full = "展开"
    case collapsed = "收缩"
}

/// 新建行程模块设计常量
///
/// 管理整个行程创建模块的设计常量，确保一致性和可维护性。
/// 替代分散在各处的硬编码数字，提供统一的设计语言。
enum NewItineraryConstants {
    
    // MARK: - 动画系统
    ///
    /// 注意：基础的动画常量已迁移到 DesignSystemConstants.Animation
    /// 这里保留模块专用的动画参数（如果有的话）
    enum Animation {
        // 当前没有模块专用的动画参数
        // 所有动画常量都已迁移到 DesignSystemConstants
    }
    
    // MARK: - 时间系统
    enum Timing {
        /// UI更新延迟 - 确保状态同步
        static let uiUpdateDelay: TimeInterval = 0.1

        /// 预加载延迟 - 优化用户体验
        static let preloadDelay: TimeInterval = 0.5

        /// 交互结束延迟 - 给动画完成时间
        static let interactionEndDelay: TimeInterval = 0.5

        /// 内容层预加载延迟 - 确保UI稳定后再预加载
        static let contentPreloadDelay: TimeInterval = 0.8

        // 注意：防抖延迟已迁移到全局配置：
        // - 使用 DesignSystemConstants.Interaction.debounceDelay
    }
    
    // MARK: - 交互阈值
    enum Threshold {
        /// 最小拖拽距离 - 避免误触
        static let minimumDrag: CGFloat = 10
        
        /// 速度阈值 - 判断快速拖拽
        static let velocityThreshold: CGFloat = 200
        
        /// 位置容差 - 微小移动保护
        static let positionTolerance: CGFloat = 20
        
        /// 低速度阈值 - 用于精细判断
        static let lowVelocityThreshold: CGFloat = 50
    }
    
    // MARK: - 布局系统
    ///
    /// 注意：基础的布局常量已迁移到 DesignSystemConstants
    /// 这里只保留模块专用的布局参数
    enum Layout {
        // MARK: - 模块专用布局常量

        /// 折叠状态弹窗高度
        static let collapsedSheetHeight: CGFloat = 180

        /// 浮动按钮底部间距
        static let floatingButtonBottomPadding: CGFloat = 180

        /// 顶部安全间距
        static let topClearance: CGFloat = 90
    }
    
    // MARK: - 文本系统
    enum Text {
        static let destinationPlaceholder = "目的地或行程名称，如：东京5日家庭游"
        static let smartPlanningComingSoon = "智能规划即将登场"
        static let smartPlanningDescription = "告诉我们你的想法，让 AI 为你量身定制完美行程。"
        static let pasteGuidePlaceholder = "请在此处粘贴攻略全文或链接..."
    }
    
    // MARK: - 数据系统
    enum Data {
        /// 默认行程时长
        static let defaultTripDuration: TimeInterval = 86400 * 3 // 3天
        
        /// 热门搜索关键词
        static let popularSearchQueries = ["西湖", "故宫", "长城", "外滩"]
        
        /// 搜索缓存大小限制
        static let searchCacheLimit = 50
    }
    
    // MARK: - 地图系统
    enum Map {
        static let chinaCenter = CLLocationCoordinate2D(latitude: 35.8617, longitude: 104.1954)
        static let chinaLatitudeRange = 18.0...54.0
        static let chinaLongitudeRange = 73.0...135.0
        static let chinaSpan = MKCoordinateSpan(latitudeDelta: 30, longitudeDelta: 40)
        static let zoomSpan = MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
        static let fullSheetHeightRatio: CGFloat = 0.9
    }
}

// MARK: - 便利扩展
///
/// 注意：动画扩展已迁移到 DesignSystemConstants
/// 如果需要模块专用的动画扩展，可以在这里添加
