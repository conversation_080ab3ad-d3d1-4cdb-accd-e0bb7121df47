import SwiftUI

/// 已选择地点展示区域组件
///
/// 功能特性：
/// - 已选择地点的网格展示
/// - 地点数量统计
/// - 地点移除功能
struct SelectedAttractionsSection: View {
    
    // MARK: - Properties

    @Binding var selectedAttractions: [AttractionModel]
    let onAttractionRemoved: ((AttractionModel) -> Void)?
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            HStack {
                Text("已选择地点")
                    .font(.title2.weight(.bold))
                    .foregroundColor(.primary)

                Spacer()

                Text("\(selectedAttractions.count) 个")
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.secondary)
                    .animation(.easeInOut(duration: 0.3), value: selectedAttractions.count)
            }

            // 景点网格
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12)
            ], spacing: 16) {
                ForEach(selectedAttractions) { attraction in
                    AttractionCard(
                        attraction: attraction,
                        onRemove: {
                            // 通过回调让ViewModel处理移除逻辑，确保动画正确执行
                            onAttractionRemoved?(attraction)
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .leading)),
                        removal: .scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .trailing))
                    ))
                }
            }
        }
    }
    
    // MARK: - Private Methods

    // 注意：移除逻辑已移至ViewModel中统一管理，确保动画效果一致
}

// MARK: - Preview

#Preview {
    SelectedAttractionsSection(
        selectedAttractions: .constant([
            AttractionModel.samples[0],
            AttractionModel.samples[1],
            AttractionModel.samples[2]
        ]),
        onAttractionRemoved: { attraction in
            print("移除景点: \(attraction.name)")
        }
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
