import Foundation

/// 分页响应协议，用于标识分页响应类型
protocol PaginationResponseProtocol {
    associatedtype ItemType: Codable
    var currentPage: Int { get }
    var pageSize: Int { get }
    var hasMore: Bool { get }
    var items: [ItemType] { get }
}

/// 分页响应数据模型
///
/// 用于封装分页查询的响应数据，支持泛型以适配不同的数据类型。
/// 提供便捷的属性和方法来处理分页逻辑和状态判断。
///
/// - Generic Parameter T: 数据项的类型，必须遵循 Codable 协议
struct PaginationResponse<T: Codable>: Codable, PaginationResponseProtocol {
    typealias ItemType = T
    // MARK: - Properties

    /// 当前页码（从1开始）
    let currentPage: Int

    /// 每页数据数量
    let pageSize: Int

    /// 是否还有更多数据可加载
    let hasMore: Bool

    /// 当前页的数据列表
    let items: [T]

    // MARK: - Computed Properties

    /// 当前页是否为空
    var isEmpty: Bool {
        items.isEmpty
    }

    /// 是否为第一页
    var isFirstPage: Bool {
        currentPage <= 1
    }

    /// 是否为最后一页
    var isLastPage: Bool {
        !hasMore
    }

    /// 下一页页码（如果有的话）
    var nextPage: Int? {
        hasMore ? currentPage + 1 : nil
    }

    /// 上一页页码（如果有的话）
    var previousPage: Int? {
        currentPage > 1 ? currentPage - 1 : nil
    }

    // MARK: - Initialization

    /// 完整初始化方法
    ///
    /// - Parameters:
    ///   - currentPage: 当前页码
    ///   - pageSize: 每页数量
    ///   - hasMore: 是否还有更多数据
    ///   - items: 数据列表
    init(
        currentPage: Int,
        pageSize: Int,
        hasMore: Bool,
        items: [T]
    ) {
        self.currentPage = max(1, currentPage) // 确保页码至少为1
        self.pageSize = max(1, pageSize) // 确保每页数量至少为1
        self.hasMore = hasMore
        self.items = items
    }

    // MARK: - Static Factory Methods

    /// 创建空分页响应
    ///
    /// - Parameter pageSize: 每页数量，默认为20
    /// - Returns: 空的分页响应对象
    static func empty(pageSize: Int = 20) -> PaginationResponse<T> {
        PaginationResponse(
            currentPage: 1,
            pageSize: pageSize,
            hasMore: false,
            items: []
        )
    }

    /// 创建单页响应（所有数据在一页内）
    ///
    /// - Parameters:
    ///   - items: 数据列表
    ///   - pageSize: 每页数量，默认使用数据列表的长度
    /// - Returns: 单页分页响应对象
    static func singlePage(items: [T], pageSize: Int? = nil) -> PaginationResponse<T> {
        let actualPageSize = pageSize ?? max(items.count, 1)
        return PaginationResponse(
            currentPage: 1,
            pageSize: actualPageSize,
            hasMore: false,
            items: items
        )
    }
}


// MARK: - 分页请求参数模型

/// 分页请求参数
///
/// 用于构建分页查询请求的参数对象，提供多种便捷的初始化方式。
/// 包含页码验证和边界检查，确保请求参数的有效性。
struct PaginationRequest: Codable {
    // MARK: - Properties

    /// 请求的页码（从1开始）
    let page: Int

    /// 每页数据数量
    let pageSize: Int

    /// 排序字段（可选）
    let sortBy: String?

    /// 排序方向（可选）
    let sortOrder: SortOrder?

    // MARK: - Computed Properties

    /// 跳过的数据数量（用于数据库查询）
    var offset: Int {
        (page - 1) * pageSize
    }

    /// 限制的数据数量（用于数据库查询）
    var limit: Int {
        pageSize
    }

    // MARK: - Initialization

    /// 标准初始化方法
    ///
    /// - Parameters:
    ///   - page: 页码，默认为1
    ///   - pageSize: 每页数量，默认为20
    ///   - sortBy: 排序字段，可选
    ///   - sortOrder: 排序方向，可选
    init(
        page: Int = 1,
        pageSize: Int = 20,
        sortBy: String? = nil,
        sortOrder: SortOrder? = nil
    ) {
        self.page = max(1, page) // 确保页码至少为1
        self.pageSize = min(max(1, pageSize), 100) // 限制每页数量在1-100之间
        self.sortBy = sortBy
        self.sortOrder = sortOrder
    }

    /// 从当前页创建下一页请求
    ///
    /// - Parameter currentPage: 当前页码
    /// - Returns: 下一页的请求参数
    init(nextPageFrom currentPage: Int, pageSize: Int = 20) {
        self.init(page: currentPage + 1, pageSize: pageSize)
    }

    /// 从分页响应创建下一页请求
    ///
    /// - Parameter response: 当前分页响应
    /// - Returns: 下一页的请求参数，如果没有更多数据则返回nil
    static func nextPage<T>(from response: PaginationResponse<T>) -> PaginationRequest? {
        guard response.hasMore else { return nil }
        return PaginationRequest(page: response.currentPage + 1, pageSize: response.pageSize)
    }

    // MARK: - Convenience Methods

    /// 转换为查询参数字典
    ///
    /// - Returns: 包含分页参数的字典
    func toQueryParameters() -> [String: String] {
        var params = [
            "page": String(page),
            "pageSize": String(pageSize)
        ]

        if let sortBy = sortBy {
            params["sortBy"] = sortBy
        }

        if let sortOrder = sortOrder {
            params["sortOrder"] = sortOrder.rawValue
        }

        return params
    }
}

// MARK: - 排序方向枚举

/// 排序方向
enum SortOrder: String, Codable, CaseIterable {
    /// 升序
    case ascending = "asc"
    /// 降序
    case descending = "desc"

    /// 显示名称
    var displayName: String {
        switch self {
        case .ascending:
            return "升序"
        case .descending:
            return "降序"
        }
    }
}
