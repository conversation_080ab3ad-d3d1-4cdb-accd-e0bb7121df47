import SwiftUI
import Combine

/// TabBar状态观察器
///
/// 提供对TabBar状态变化的监听和响应机制。
/// 可以用于在TabBar状态发生变化时执行特定的业务逻辑。
///
/// 主要功能：
/// - 监听TabBar显示隐藏状态变化
/// - 监听标签页切换事件
/// - 提供状态变化的回调机制
/// - 支持条件性监听和过滤
///
/// 使用示例：
/// ```swift
/// TabBarStateObserver { change in
///     switch change {
///     case .visibility(let isVisible):
///         print("TabBar可见性变化: \(isVisible)")
///     case .tabSelection(let from, let to):
///         print("标签页切换: \(from) -> \(to)")
///     }
/// }
/// ```
struct TabBarStateObserver: View {
    
    // MARK: - Properties
    
    /// 状态变化回调
    private let onStateChange: (TabBarStateChange) -> Void
    
    /// 是否只监听可见性变化
    private let visibilityOnly: Bool
    
    /// 是否只监听标签页切换
    private let tabSelectionOnly: Bool
    
    // MARK: - State
    
    /// TabBar状态管理器
    @EnvironmentObject private var stateManager: TabBarStateManager
    
    /// 订阅存储
    @State private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    /// 初始化状态观察器
    ///
    /// - Parameters:
    ///   - visibilityOnly: 是否只监听可见性变化，默认为false
    ///   - tabSelectionOnly: 是否只监听标签页切换，默认为false
    ///   - onStateChange: 状态变化回调
    init(
        visibilityOnly: Bool = false,
        tabSelectionOnly: Bool = false,
        onStateChange: @escaping (TabBarStateChange) -> Void
    ) {
        self.visibilityOnly = visibilityOnly
        self.tabSelectionOnly = tabSelectionOnly
        self.onStateChange = onStateChange
    }
    
    // MARK: - Body
    
    var body: some View {
        Color.clear
            .onAppear {
                setupObservation()
            }
            .onDisappear {
                cancellables.removeAll()
            }
    }
    
    // MARK: - Private Methods
    
    /// 设置状态观察
    private func setupObservation() {
        // 监听状态管理器的状态变化发布者
        stateManager.stateChangePublisher()
            .sink { change in
                self.handleStateChange(change)
            }
            .store(in: &cancellables)

        // 如果需要监听可见性变化
        if !tabSelectionOnly {
            stateManager.$isVisible
                .dropFirst() // 忽略初始值
                .removeDuplicates()
                .sink { isVisible in
                    self.handleStateChange(.visibility(isVisible))
                }
                .store(in: &cancellables)
        }

        // 如果需要监听标签页切换
        if !visibilityOnly {
            let initialTab = stateManager.selectedTab
            stateManager.$selectedTab
                .dropFirst() // 忽略初始值
                .scan((initialTab, initialTab)) { previous, current in
                    (previous.1, current)
                }
                .sink { (from, to) in
                    if from != to {
                        self.handleStateChange(.tabSelection(from: from, to: to))
                    }
                }
                .store(in: &cancellables)
        }
    }
    
    /// 处理状态变化
    private func handleStateChange(_ change: TabBarStateChange) {
        // 根据过滤条件决定是否处理
        switch change {
        case .visibility:
            if !tabSelectionOnly {
                onStateChange(change)
            }
        case .tabSelection:
            if !visibilityOnly {
                onStateChange(change)
            }
        }
    }
}

// MARK: - View Extensions

extension View {
    
    /// 添加TabBar状态观察器
    ///
    /// 为视图添加TabBar状态变化的监听功能。
    ///
    /// - Parameters:
    ///   - visibilityOnly: 是否只监听可见性变化
    ///   - tabSelectionOnly: 是否只监听标签页切换
    ///   - onStateChange: 状态变化回调
    /// - Returns: 带有状态观察功能的视图
    ///
    /// 使用示例：
    /// ```swift
    /// MyView()
    ///     .observeTabBarState { change in
    ///         // 处理状态变化
    ///     }
    /// ```
    func observeTabBarState(
        visibilityOnly: Bool = false,
        tabSelectionOnly: Bool = false,
        onStateChange: @escaping (TabBarStateChange) -> Void
    ) -> some View {
        background(
            TabBarStateObserver(
                visibilityOnly: visibilityOnly,
                tabSelectionOnly: tabSelectionOnly,
                onStateChange: onStateChange
            )
        )
    }
    
    /// 监听TabBar可见性变化
    ///
    /// 专门用于监听TabBar显示隐藏状态变化的便捷方法。
    ///
    /// - Parameter onVisibilityChange: 可见性变化回调
    /// - Returns: 带有可见性监听功能的视图
    ///
    /// 使用示例：
    /// ```swift
    /// MyView()
    ///     .onTabBarVisibilityChange { isVisible in
    ///         print("TabBar可见性: \(isVisible)")
    ///     }
    /// ```
    func onTabBarVisibilityChange(
        _ onVisibilityChange: @escaping (Bool) -> Void
    ) -> some View {
        observeTabBarState(visibilityOnly: true) { change in
            if case .visibility(let isVisible) = change {
                onVisibilityChange(isVisible)
            }
        }
    }
    
    /// 监听标签页切换
    ///
    /// 专门用于监听标签页切换事件的便捷方法。
    ///
    /// - Parameter onTabChange: 标签页切换回调
    /// - Returns: 带有标签页切换监听功能的视图
    ///
    /// 使用示例：
    /// ```swift
    /// MyView()
    ///     .onTabBarTabChange { from, to in
    ///         print("标签页切换: \(from) -> \(to)")
    ///     }
    /// ```
    func onTabBarTabChange(
        _ onTabChange: @escaping (AppTab, AppTab) -> Void
    ) -> some View {
        observeTabBarState(tabSelectionOnly: true) { change in
            if case .tabSelection(let from, let to) = change {
                onTabChange(from, to)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    VStack {
        Text("TabBar状态观察器示例")
            .observeTabBarState { change in
                print("状态变化: \(change)")
            }
    }
    .environmentObject(TabBarStateManager.shared)
}
