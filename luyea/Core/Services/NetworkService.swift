import Foundation

/// 网络服务错误类型
///
/// 定义网络请求过程中可能出现的各种错误类型，便于错误处理和用户反馈。
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case invalidData
    case requestFailed(Error)
    case timeout
    case noInternetConnection
    case serverError(Int)
    case decodingError(Error)

    /// 错误描述信息
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL地址"
        case .invalidResponse:
            return "服务器响应无效"
        case .invalidData:
            return "数据格式错误"
        case .requestFailed(let error):
            return "请求失败: \(error.localizedDescription)"
        case .timeout:
            return "请求超时，请检查网络连接"
        case .noInternetConnection:
            return "网络连接不可用"
        case .serverError(let code):
            return "服务器错误 (状态码: \(code))"
        case .decodingError(let error):
            return "数据解析失败: \(error.localizedDescription)"
        }
    }
}

/// 网络服务协议，便于依赖注入和单元测试
protocol NetworkServiceProtocol {
    func request<T: Decodable>(_ api: APIRequest) async throws -> T
}

/// 网络服务实现类
///
/// 负责处理应用中的所有网络请求，支持Mock模式和真实网络请求。
/// 包含完善的错误处理、日志记录和性能优化。
final class NetworkService: NetworkServiceProtocol {
    /// 单例实例
    static let shared = NetworkService()

    /// URL会话对象
    private let session: URLSession

    /// JSON解码器
    private let jsonDecoder: JSONDecoder

    /// JSON编码器
    private let jsonEncoder: JSONEncoder

    /// 私有初始化方法，确保单例模式
    private init() {
        // 配置URL会话
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = AppConfig.Network.requestTimeout
        config.timeoutIntervalForResource = AppConfig.Network.downloadTimeout
        config.requestCachePolicy = .useProtocolCachePolicy
        self.session = URLSession(configuration: config)

        // 配置JSON解码器
        self.jsonDecoder = JSONDecoder()
        self.jsonDecoder.dateDecodingStrategy = .iso8601

        // 配置JSON编码器
        self.jsonEncoder = JSONEncoder()
        self.jsonEncoder.dateEncodingStrategy = .iso8601
    }

    // MARK: - Public Methods

    /// 带详细日志的 API 请求方法（用于单独调试特定接口）
    ///
    /// 即使全局关闭了详细网络日志，此方法也会强制显示请求和响应的详细信息
    ///
    /// - Parameter api: API请求配置对象
    /// - Returns: 解码后的响应数据
    /// - Throws: NetworkError 相关错误
    func requestWithDetailLog<T: Decodable>(_ api: APIRequest) async throws -> T {
        return try await request(api, forceDetailLog: true)
    }

    /// 通用 API 请求方法（协议要求的方法）
    ///
    /// 支持泛型返回类型，自动处理JSON编解码，包含完善的错误处理和日志记录。
    /// 在Mock环境下会自动读取本地JSON文件，便于开发和测试。
    ///
    /// - Parameter api: API请求配置对象
    /// - Returns: 解码后的响应数据
    /// - Throws: NetworkError 相关错误
    func request<T: Decodable>(_ api: APIRequest) async throws -> T {
        return try await request(api, forceDetailLog: false)
    }

    /// 通用 API 请求方法（带详细日志控制）
    ///
    /// 支持泛型返回类型，自动处理JSON编解码，包含完善的错误处理和日志记录。
    /// 在Mock环境下会自动读取本地JSON文件，便于开发和测试。
    ///
    /// - Parameters:
    ///   - api: API请求配置对象
    ///   - forceDetailLog: 强制显示此请求的详细日志（用于单独调试特定接口）
    /// - Returns: 解码后的响应数据
    /// - Throws: NetworkError 相关错误
    func request<T: Decodable>(_ api: APIRequest, forceDetailLog: Bool) async throws -> T {
        let startTime = CFAbsoluteTimeGetCurrent()

        // 记录请求开始
        Log.networkDetail("➡️ [API] \(api.method) \(api.path)", forceDetail: forceDetailLog) {
            buildRequestDetails(api)
        }

        do {
            let result: T

            // Mock环境处理
            if EnvironmentConfig.current == .mock {
                result = try await handleMockRequest(api, forceDetailLog: forceDetailLog)
            } else {
                result = try await handleNetworkRequest(api, forceDetailLog: forceDetailLog)
            }

            // 记录成功日志和性能信息
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            Log.success("✅ [API] \(api.path) 完成 (\(String(format: "%.2f", duration * 1000))ms)")

            return result

        } catch {
            // 记录错误日志
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            Log.error("❌ [API] \(api.path) 失败 (\(String(format: "%.2f", duration * 1000))ms): \(error)")
            throw error
        }
    }

    // MARK: - Private Methods

    /// 构建请求详情字符串
    private func buildRequestDetails(_ api: APIRequest) -> String {
        // 根据Log配置自动决定是否显示详细信息
        guard Log.isDetailedNetworkLoggingEnabled else {
            return "📍 \(api.method) \(NetworkConstants.baseURL)\(api.path)"
        }

        var details: [String] = []

        // 请求基本信息
        details.append("🚀 ==================== 完整请求信息 ====================")
        details.append("📍 URL: \(api.method) \(NetworkConstants.baseURL)\(api.path)")
        details.append("⏰ 时间: \(Date().ISO8601Format())")
        details.append("🔧 环境: \(EnvironmentConfig.current.rawValue)")

        // 查询参数 - 完整显示
        if !api.query.isEmpty {
            details.append("🔍 查询参数:")
            for (key, value) in api.query.sorted(by: { $0.key < $1.key }) {
                details.append("   • \(key) = \(value)")
            }
        } else {
            details.append("🔍 查询参数: 无")
        }

        // 请求头 - 完整显示（敏感信息脱敏）
        if !api.headers.isEmpty {
            details.append("📋 请求头:")
            for (key, value) in api.headers.sorted(by: { $0.key < $1.key }) {
                let isSensitive = key.lowercased().contains("authorization") ||
                                key.lowercased().contains("token") ||
                                key.lowercased().contains("cookie")
                let displayValue = isSensitive ? "***隐藏***" : value
                details.append("   • \(key): \(displayValue)")
            }
        } else {
            details.append("📋 请求头: 使用默认请求头")
        }

        // 请求体 - 完整显示并格式化
        if let body = api.body {
            details.append("📦 请求体 (\(body.count) bytes):")
            if let bodyString = String(data: body, encoding: .utf8) {
                // 尝试格式化JSON
                if let formattedJSON = formatJSONString(bodyString) {
                    details.append("📄 JSON格式:")
                    formattedJSON.split(separator: "\n").forEach { line in
                        details.append("   \(line)")
                    }
                } else {
                    // 非JSON格式，直接显示
                    details.append("📄 原始内容:")
                    bodyString.split(separator: "\n").forEach { line in
                        details.append("   \(line)")
                    }
                }
            } else {
                details.append("📄 二进制数据，无法显示为文本")
            }
        } else {
            details.append("📦 请求体: 无")
        }

        details.append("================================================")
        return details.joined(separator: "\n")
    }

    /// 构建响应详情字符串
    private func buildResponseDetails(_ response: HTTPURLResponse, data: Data) -> String {
        // 根据Log配置自动决定是否显示详细信息
        let statusEmoji = response.statusCode >= 200 && response.statusCode < 300 ? "✅" : "❌"
        guard Log.isDetailedNetworkLoggingEnabled else {
            return "\(statusEmoji) \(response.statusCode) (\(data.count) bytes)"
        }

        var details: [String] = []

        // 响应基本信息
        details.append("📥 ==================== 完整响应信息 ====================")
        details.append("\(statusEmoji) 状态码: \(response.statusCode) - \(HTTPURLResponse.localizedString(forStatusCode: response.statusCode))")
        details.append("⏰ 时间: \(Date().ISO8601Format())")

        // 响应URL
        if let url = response.url {
            details.append("🔗 URL: \(url.absoluteString)")
        }

        // 响应头 - 完整显示
        if !response.allHeaderFields.isEmpty {
            details.append("📋 响应头:")
            for (key, value) in response.allHeaderFields.sorted(by: { "\($0.key)" < "\($1.key)" }) {
                details.append("   • \(key): \(value)")
            }
        }

        // 响应体 - 完整显示并格式化
        details.append("📦 响应体 (\(data.count) bytes):")
        if data.count > 0 {
            if let responseString = String(data: data, encoding: .utf8) {
                // 尝试格式化JSON
                if let formattedJSON = formatJSONString(responseString) {
                    details.append("📄 JSON格式:")
                    formattedJSON.split(separator: "\n").forEach { line in
                        details.append("   \(line)")
                    }
                } else {
                    // 非JSON格式，直接显示
                    details.append("📄 原始内容:")
                    responseString.split(separator: "\n").forEach { line in
                        details.append("   \(line)")
                    }
                }
            } else {
                details.append("📄 二进制数据，无法显示为文本")
            }
        } else {
            details.append("📄 空响应")
        }

        details.append("================================================")
        return details.joined(separator: "\n")
    }

    /// 构建Mock详情字符串
    private func buildMockDetails(fileName: String, data: Data) -> String {
        // 根据Log配置自动决定是否显示详细信息
        guard Log.isDetailedNetworkLoggingEnabled else {
            return "📁 \(fileName) (\(data.count) bytes)"
        }

        var details: [String] = []

        // Mock基本信息
        details.append("🎭 ==================== Mock响应信息 ====================")
        details.append("📁 文件: \(fileName)")
        details.append("📦 大小: \(data.count) bytes")
        details.append("⏰ 时间: \(Date().ISO8601Format())")

        // Mock内容 - 完整显示并格式化
        if let jsonString = String(data: data, encoding: .utf8) {
            // 尝试格式化JSON
            if let formattedJSON = formatJSONString(jsonString) {
                details.append("📄 JSON格式:")
                formattedJSON.split(separator: "\n").forEach { line in
                    details.append("   \(line)")
                }
            } else {
                // 非JSON格式，直接显示
                details.append("📄 原始内容:")
                jsonString.split(separator: "\n").forEach { line in
                    details.append("   \(line)")
                }
            }
        } else {
            details.append("📄 无法解析为文本")
        }

        details.append("================================================")
        return details.joined(separator: "\n")
    }

    /// 格式化JSON字符串
    private func formatJSONString(_ jsonString: String) -> String? {
        guard let jsonData = jsonString.data(using: .utf8),
              let jsonObject = try? JSONSerialization.jsonObject(with: jsonData),
              let prettyData = try? JSONSerialization.data(withJSONObject: jsonObject, options: .prettyPrinted),
              let prettyString = String(data: prettyData, encoding: .utf8) else {
            return nil
        }
        return prettyString
    }



    /// 处理Mock环境下的请求
    ///
    /// 从本地Bundle中读取对应的JSON文件作为响应数据。
    /// 文件命名规则：将API路径中的"/"替换为"_"，并添加".json"后缀。
    ///
    /// - Parameters:
    ///   - api: API请求配置
    ///   - forceDetailLog: 强制显示详细日志
    /// - Returns: 解码后的响应数据
    /// - Throws: NetworkError 相关错误
    private func handleMockRequest<T: Decodable>(_ api: APIRequest, forceDetailLog: Bool) async throws -> T {
        let fileName = api.path
            .trimmingCharacters(in: CharacterSet(charactersIn: "/"))
            .replacingOccurrences(of: "/", with: "_")
            + ".json"

        guard let url = Bundle.main.url(forResource: fileName, withExtension: nil) else {
            Log.error("Mock文件未找到: \(fileName)")
            throw NetworkError.invalidURL
        }

        do {
            let data = try Data(contentsOf: url)



            // 检查是否是分页响应类型
            if T.self is any PaginationResponseProtocol.Type {
                return try handleMockPaginationRequest(api: api, data: data)
            } else {
                return try jsonDecoder.decode(T.self, from: data)
            }
        } catch {
            Log.error("Mock数据解码失败: \(error)")
            throw NetworkError.decodingError(error)
        }
    }

    /// 处理Mock环境下的分页请求
    private func handleMockPaginationRequest<T: Decodable>(api: APIRequest, data: Data) throws -> T {
        // 检查是否包含分页参数
        guard hasMockPaginationParams(api) else {
            // 没有分页参数，直接返回原始数据
            return try jsonDecoder.decode(T.self, from: data)
        }

        // 使用策略模式处理分页
        return try processPaginationWithStrategy(
            api: api,
            data: data,
            decoder: jsonDecoder
        )
    }

    /// 检查Mock API请求是否包含分页参数
    private func hasMockPaginationParams(_ api: APIRequest) -> Bool {
        return api.query.keys.contains { ["page", "pageSize", "limit", "offset"].contains($0) }
    }

    /// 使用策略处理分页请求
    private func processPaginationWithStrategy<T: Decodable>(
        api: APIRequest,
        data: Data,
        decoder: JSONDecoder
    ) throws -> T {
        // 使用Mock分页筛选策略系统
        let processedData = try MockPaginationFilterStrategy.applyStrategy(
            for: api.path,
            to: data,
            with: api.query,
            decoder: decoder
        )

        return try decoder.decode(T.self, from: processedData)
    }

    /// 处理真实网络请求
    ///
    /// 构建URLRequest并执行网络请求，包含完善的错误处理和状态码检查。
    ///
    /// - Parameters:
    ///   - api: API请求配置
    ///   - forceDetailLog: 强制显示详细日志
    /// - Returns: 解码后的响应数据
    /// - Throws: NetworkError 相关错误
    private func handleNetworkRequest<T: Decodable>(_ api: APIRequest, forceDetailLog: Bool) async throws -> T {
        // 构建URL
        let url = try buildURL(from: api)

        // 构建请求
        let request = try buildURLRequest(url: url, api: api)

        // 执行请求
        let (data, response) = try await session.data(for: request)

        // 记录响应信息
        if let httpResponse = response as? HTTPURLResponse {
            Log.networkDetail("📥 [Response] \(httpResponse.statusCode) (\(data.count) bytes)", forceDetail: forceDetailLog) {
                buildResponseDetails(httpResponse, data: data)
            }
        }

        // 验证响应
        try validateResponse(response, data: data)

        // 解码数据
        do {
            let result = try jsonDecoder.decode(T.self, from: data)
            Log.debug("✅ 数据解码成功 (\(data.count) bytes)")
            return result
        } catch {
            Log.error("❌ 响应数据解码失败: \(error)")
            if let responseString = String(data: data, encoding: .utf8) {
                Log.debug("📄 原始响应: \(responseString.prefix(500))")
            }
            throw NetworkError.decodingError(error)
        }
    }

    /// 构建完整的URL
    ///
    /// - Parameter api: API请求配置
    /// - Returns: 构建好的URL对象
    /// - Throws: NetworkError.invalidURL
    private func buildURL(from api: APIRequest) throws -> URL {
        var urlString = NetworkConstants.baseURL + api.path

        // 添加查询参数
        if !api.query.isEmpty {
            let queryItems = api.query.map { "\($0.key)=\($0.value.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? $0.value)" }
            urlString += "?" + queryItems.joined(separator: "&")
        }

        guard let url = URL(string: urlString) else {
            Log.error("❌ 无效的URL: \(urlString)")
            throw NetworkError.invalidURL
        }

        return url
    }

    /// 构建URLRequest对象
    ///
    /// - Parameters:
    ///   - url: 请求URL
    ///   - api: API请求配置
    /// - Returns: 配置好的URLRequest对象
    /// - Throws: NetworkError 相关错误
    private func buildURLRequest(url: URL, api: APIRequest) throws -> URLRequest {
        var request = URLRequest(url: url)
        request.httpMethod = api.method
        request.timeoutInterval = NetworkConstants.timeoutInterval
        request.httpBody = api.body

        // 设置默认请求头
        request.setValue(AppConfig.Network.defaultContentType, forHTTPHeaderField: NetworkConstants.Headers.contentType)
        request.setValue(AppConfig.Network.defaultAccept, forHTTPHeaderField: NetworkConstants.Headers.accept)
        request.setValue(AppConstants.appInfoString, forHTTPHeaderField: NetworkConstants.Headers.userAgent)

        // 设置自定义请求头
        api.headers.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }

        return request
    }

    /// 验证HTTP响应
    ///
    /// - Parameters:
    ///   - response: URL响应对象
    ///   - data: 响应数据
    /// - Throws: NetworkError 相关错误
    private func validateResponse(_ response: URLResponse, data: Data) throws {
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NetworkError.invalidResponse
        }

        Log.debug("📡 响应状态码: \(httpResponse.statusCode)")

        switch httpResponse.statusCode {
        case 200...299:
            // 成功响应
            break
        case 400...499:
            // 客户端错误
            if httpResponse.statusCode == 401 {
                // 可以在这里处理token过期等逻辑
                Log.warning("⚠️ 认证失败，可能需要重新登录")
            }
            throw NetworkError.serverError(httpResponse.statusCode)
        case 500...599:
            // 服务器错误
            throw NetworkError.serverError(httpResponse.statusCode)
        default:
            throw NetworkError.invalidResponse
        }
    }
}
