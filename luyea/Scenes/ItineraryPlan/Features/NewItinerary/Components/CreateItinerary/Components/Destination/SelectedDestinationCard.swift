import SwiftUI

/// 已选择目的地卡片组件
///
/// 功能特性：
/// - 显示已选择的目的地信息
/// - 支持移除操作
/// - 现代化卡片设计
/// - 符合app整体设计风格
struct SelectedDestinationCard: View {
    
    // MARK: - Properties
    
    let destination: Destination
    let onRemove: () -> Void
    
    // MARK: - State

    @State private var isPressed = false
    @State private var isRemoveButtonPressed = false
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 目的地图片
            destinationImageSection
            
            // 目的地信息
            destinationInfoSection
        }
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        .overlay(
            // 移除按钮
            removeButton,
            alignment: .topTrailing
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
    }
    
    // MARK: - Private Views
    
    /// 目的地图片区域
    private var destinationImageSection: some View {
        CachedAsyncImage(
            url: URL(string: destination.imageUrl ?? "")
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            placeholderImage
        } errorView: {
            // 使用改进后的公共组件，标准样式
            ImageErrorView(
                style: .standard,
                showText: true,
                errorText: "加载失败",
                iconName: "exclamationmark.triangle"
            )
        }
        .frame(height: 45)
        .clipped()
    }
    
    /// 占位图片
    private var placeholderImage: some View {
        Rectangle()
            .fill(
                LinearGradient(
                    colors: [
                        Color.blue.opacity(0.1),
                        Color.blue.opacity(0.2)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                VStack(spacing: 2) {
                    Image(systemName: "location.circle")
                        .font(.callout)
                        .foregroundColor(.blue.opacity(0.6))

                    Text("目的地")
                        .font(.caption2.weight(.medium))
                        .foregroundColor(.blue.opacity(0.8))
                }
            )
    }

    
    /// 目的地信息区域
    private var destinationInfoSection: some View {
        VStack(alignment: .leading, spacing: 2) {
            // 目的地名称
            Text(destination.name)
                .font(.caption.weight(.semibold))
                .foregroundColor(.primary)
                .lineLimit(1)

            // 目的地描述或类型
            if let description = destination.description {
                Text(description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            } else {
                Text(destination.type.displayName)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(8)
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    /// 移除按钮
    private var removeButton: some View {
        Button(action: onRemove) {
            Image(systemName: "xmark.circle.fill")
                .font(.caption)
                .foregroundColor(.white)
                .background(
                    Circle()
                        .fill(Color.gray.opacity(0.8))
                        .frame(width: 18, height: 18)
                )
        }
        .offset(x: -5, y: 5)
        .opacity(0.9)
        .scaleEffect(isRemoveButtonPressed ? 0.85 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isRemoveButtonPressed)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isRemoveButtonPressed = pressing
        } perform: {}
    }
}

// MARK: - Preview

#Preview {
    LazyVGrid(columns: [
        GridItem(.flexible(), spacing: 12),
        GridItem(.flexible(), spacing: 12)
    ], spacing: 12) {
        ForEach(Destination.samples.filter { $0.type == .city }, id: \.id) { destination in
            SelectedDestinationCard(
                destination: destination,
                onRemove: {}
            )
        }
    }
    .padding(20)
    .background(Color(.systemGroupedBackground))
}
