import SwiftUI
import MapKit

/// 背景地图层
///
/// 提供沉浸式地图背景体验，作为整个行程创建界面的视觉基础。
/// 地图作为背景层，不参与具体的业务逻辑交互。
///
/// 主要功能：
/// - 显示地图背景
/// - 提供基础的地图交互（缩放、平移）
/// - 显示用户位置
/// - 响应管理器的状态变化
struct BackgroundMapLayer: View {
    
    // MARK: - Properties
    
    @ObservedObject var mapBackgroundManager: MapBackgroundManager
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 地图背景 - 始终显示
            mapGradientBackground

            // 地图内容 - 优化版本，减少不必要的检查
            if mapBackgroundManager.isReady {
                mapContent
                    .transition(.opacity.combined(with: .scale(scale: 0.98)))
            } else {
                // 显示轻量级加载占位符
                mapLoadingPlaceholder
            }

            // 位置标签覆盖 - 仅在需要时显示
            if mapBackgroundManager.showLocationLabel {
                locationLabelOverlay
            }
        }
        .appBackground()
        .onAppear {
            Log.debug("🗺️ 背景地图层出现")
        }
        .onDisappear {
            Log.debug("🗺️ 背景地图层消失")
        }
    }
    
    // MARK: - Private Views
    
    /// 地图渐变背景 - 始终显示
    private var mapGradientBackground: some View {
        Color.clear
            .appBackground()
    }

    /// 地图加载占位符
    private var mapLoadingPlaceholder: some View {
        ZStack {
            // 渐变背景
            LinearGradient(
                colors: [
                    Color(.systemBlue).opacity(0.1),
                    Color(.systemTeal).opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // 加载指示器
            VStack(spacing: 12) {
                Image(systemName: "map")
                    .font(.system(size: 32, weight: .light))
                    .foregroundColor(.secondary.opacity(0.6))

                Text("地图加载中...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .scaleEffect(0.8)
        }
    }


    
    /// 地图内容视图
    private var mapContent: some View {
        Map(
            position: $mapBackgroundManager.cameraPosition,
            interactionModes: mapBackgroundManager.interactionModes
        ) {
            // 用户位置标注
            if mapBackgroundManager.showUserLocation {
                userLocationAnnotation()
            }

            // 未来可以在这里添加其他标注点
            // 例如：选中的目的地、景点等
        }
        .mapStyle(mapBackgroundManager.mapStyle)
        .mapControls {
            MapCompass()
            MapScaleView()
        }
        .mapControlVisibility(.automatic)
        .onMapCameraChange(frequency: .onEnd) { context in
            handleMapCameraChange(context)
        }
    }
    
    /// 用户位置标注
    private func userLocationAnnotation() -> some MapContent {
        Group {
            if let location = LocationManager.shared.location {
                Annotation("我的位置", coordinate: location.coordinate) {
                    UserLocationAnnotationView()
                }
                .annotationTitles(.hidden)
            }
        }
    }
    
    /// 位置标签覆盖层
    private var locationLabelOverlay: some View {
        VStack {
            Spacer()
            
            HStack {
                Spacer()
                
                Text("当前位置")
                    .font(.caption.weight(.medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(.blue.gradient, in: Capsule())
                    .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                
                Spacer()
            }
            
            Spacer()
                .frame(height: 120) // 避免与底部弹窗重叠
        }
        .transition(.opacity.combined(with: .move(edge: .bottom)))
    }
    
    // MARK: - Private Methods
    
    /// 处理地图相机变化
    private func handleMapCameraChange(_ context: MapCameraUpdateContext) {
        // 这里可以添加地图相机变化的处理逻辑
        // 例如：更新可见区域、触发相关事件等
        
        Log.debug("🗺️ 地图相机位置变化")
    }
}



// MARK: - Preview

#Preview("地图背景") {
    BackgroundMapLayer(
        mapBackgroundManager: MapBackgroundManager()
    )
}

#Preview("地图占位") {
    LinearGradient(
        colors: [
            Color(.systemBlue).opacity(0.1),
            Color(.systemTeal).opacity(0.05)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    .overlay(
        VStack(spacing: 16) {
            Image(systemName: "map")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.secondary.opacity(0.6))
            
            Text("地图加载中...")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .scaleEffect(0.8)
    )
}
