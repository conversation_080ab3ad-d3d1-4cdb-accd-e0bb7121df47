import SwiftUI

/// 应用主要标签页枚举
///
/// 定义应用的主要功能模块标签页，包括标题、图标和对应的视图。
/// 统一管理标签页的配置，便于维护和扩展。
enum AppTab: String, CaseIterable, Identifiable {
    case itineraryPlan = "itineraryPlan"
    case exploreJourney = "exploreJourney"
    case profileCenter = "profileCenter"

    var id: String { rawValue }

    /// 标签页显示标题
    var title: String {
        switch self {
        case .itineraryPlan: return "行程计划"
        case .exploreJourney: return "探索旅途"
        case .profileCenter: return "个人中心"
        }
    }

    /// 标签页图标名称
    var iconName: String {
        switch self {
        case .itineraryPlan: return "map.fill"
        case .exploreJourney: return "globe.americas.fill"
        case .profileCenter: return "person.fill"
        }
    }

    /// 标签页未选中时的图标名称
    var unselectedIconName: String {
        switch self {
        case .itineraryPlan: return "map"
        case .exploreJourney: return "globe.americas"
        case .profileCenter: return "person"
        }
    }

    /// 标签页对应的内容视图
    @ViewBuilder
    var contentView: some View {
        switch self {
        case .itineraryPlan:
            ItineraryPlanView()
        case .exploreJourney:
            ExploreJourneyView()
        case .profileCenter:
            ProfileCenterView()
        }
    }
}