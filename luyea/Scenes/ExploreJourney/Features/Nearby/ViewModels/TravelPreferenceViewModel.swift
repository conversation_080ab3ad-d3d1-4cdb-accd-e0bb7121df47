import Foundation
import Combine

class TravelPreferenceViewModel: ObservableObject {
    @Published var preferences: [TravelPreference] = TravelPreference.samples
    @Published var selectedPreference: TravelPreference? // Changed to single selection as per image
    
    func selectPreference(_ preference: TravelPreference) {
        if selectedPreference == preference {
            selectedPreference = nil // Deselect if already selected
        } else {
            selectedPreference = preference
        }
    }
}
