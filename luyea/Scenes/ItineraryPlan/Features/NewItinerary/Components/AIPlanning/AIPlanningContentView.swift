import Foundation
import SwiftUI

/// AI智能规划Tab内容视图
///
/// AI智能规划功能的主界面，当前为占位实现。
/// 预留完整架构，为未来的AI规划功能做准备。
struct AIPlanningContentView: View {

    // MARK: - Dependencies

    /// 内容管理器，负责UI状态管理
    @ObservedObject var contentManager: ContentManager

    // MARK: - Body

    var body: some View {
        VStack(spacing: 24) {
            Spacer()

            // 简单的占位内容
            VStack(spacing: 16) {
                Image(systemName: "wand.and.stars")
                    .font(.system(size: 48, weight: .light))
                    .foregroundColor(.secondary.opacity(0.6))

                Text("AI规划")
                    .font(.title2.weight(.medium))
                    .foregroundColor(.primary)

                Text("功能开发中...")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    AIPlanningContentView(
        contentManager: ContentManager()
    )
    .padding()
}