import SwiftUI

/// FootprintsView
/// 展示用户足迹统计与城市卡片。
///
/// 功能特性：
/// - 显示用户去过的城市数量统计
/// - 水平滚动展示城市卡片（最多6个）
/// - 支持异步图片加载和占位符
/// - 响应式布局设计
struct FootprintsView: View {

    // MARK: - Properties

    /// 足迹数据列表
    let footprints: [FootprintModel]

    // MARK: - Body

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            footprintStatisticsView

            if !footprints.isEmpty {
                footprintCardsScrollView
            }
        }
        .padding(.top, 10)
        .padding(.bottom, 8)
    }

    // MARK: - Private Views

    /// 足迹统计区域
    private var footprintStatisticsView: some View {
        HStack(alignment: .lastTextBaseline, spacing: 6) {
            Text("去过")
                .font(.caption)
                .foregroundColor(.secondary)

            Text("\(footprints.count)")
                .font(.title2.weight(.bold))
                .foregroundColor(.blue)

            Text("个城市")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.leading, 4)
    }

    /// 城市卡片滚动视图
    private var footprintCardsScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(
                    footprints.prefix(6), // 最多显示6个足迹
                    id: \.id
                ) { footprint in
                    FootprintCityCard(footprint: footprint)
                }
            }
            .padding(.horizontal, 4)
        }
        .frame(height: 100)
    }
}

// MARK: - FootprintCityCard

/// 单个城市足迹卡片
private struct FootprintCityCard: View {
    let footprint: FootprintModel

    var body: some View {
        VStack(spacing: 4) {
            cityImageView
            cityNameView
        }
        .frame(width: 70)
    }

    /// 城市图片视图
    @ViewBuilder
    private var cityImageView: some View {
        CachedAsyncImage(
            url: URL(string: footprint.imageUrl)
        ) { image in
            image
                .resizable()
                .scaledToFill()
        } placeholder: {
            placeholderImageView
        }
        .frame(width: 60, height: 60)
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }

    /// 占位图片视图
    private var placeholderImageView: some View {
        Color.gray.opacity(0.2)
            .frame(width: 60, height: 60)
            .clipShape(RoundedRectangle(cornerRadius: 8))
    }

    /// 城市名称视图
    private var cityNameView: some View {
        Text(footprint.city)
            .font(.caption)
            .foregroundColor(.secondary)
            .lineLimit(1)
    }
}
