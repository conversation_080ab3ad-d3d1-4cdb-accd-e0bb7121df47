import SwiftUI
import MapKit
import CoreLocation

/// 行程地图视图组件
struct ItineraryMapView: View {
    @Binding var position: MapCameraPosition
    @ObservedObject var locationManager: LocationManager
    let interactionModes: MapInteractionModes
    let showUserLocation: Bo<PERSON>
    @Binding var showLocationLabel: Bool

    @State private var isMapReady = false


    var body: some View {
        ZStack {
            if isMapReady {
                mapContent
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            } else {
                mapPlaceholder
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }

            if showLocationLabel {
                locationLabelOverlay
            }
        }
        .onAppear(perform: setupMap)
        .onChange(of: locationManager.location, handleLocationUpdate)
    }

    private var mapPlaceholder: some View {
        ZStack {
            Color(.systemBlue)
                .opacity(0.08)

            VStack(spacing: 16) {
                Image(systemName: "map")
                    .font(.title2)
                    .foregroundStyle(.blue)

                Text("加载中...")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
        }
        .ignoresSafeArea(.all, edges: .all)
    }

    private var mapContent: some View {
        Map(position: $position, interactionModes: interactionModes) {
            if showUserLocation, let location = locationManager.location {
                Annotation("我的位置", coordinate: location.coordinate) {
                    UserLocationAnnotation()
                }
                .annotationTitles(.hidden)
                .annotationSubtitles(.hidden)
            }
        }
        .mapStyle(.standard(elevation: .flat, pointsOfInterest: .excludingAll))
        .mapControls {
            MapCompass()
            MapScaleView()
        }
        .mapControlVisibility(.automatic)
        .ignoresSafeArea(.all, edges: .all)
        .onMapCameraChange(frequency: .onEnd, handleMapCameraChange)
    }

    private var locationLabelOverlay: some View {
        VStack {
            HStack {
                Spacer()
                Text("当前位置")
                    .font(.caption.weight(.semibold))
                    .foregroundStyle(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(.blue.gradient, in: Capsule())
                    .shadow(color: .black.opacity(0.25), radius: 8, x: 0, y: 4)
                    .symbolEffect(.bounce, value: showLocationLabel)
                Spacer()
            }
            .padding(.top, 100)
            Spacer()
        }
        .transition(.asymmetric(
            insertion: .scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .top)),
            removal: .scale(scale: 0.9).combined(with: .opacity).combined(with: .move(edge: .top))
        ))
    }

    private func setupMap() {
        Task {
            // 减少延迟，更快显示地图
            try? await Task.sleep(for: .milliseconds(50))
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.4)) {
                    isMapReady = true
                }
            }
        }
    }

    private func handleLocationUpdate(_ oldValue: CLLocation?, _ newLocation: CLLocation?) {
        guard let location = newLocation else { return }

        withAnimation(.easeInOut(duration: 0.8)) {
            if case .automatic = position {
                position = .camera(MapCamera(
                    centerCoordinate: location.coordinate,
                    distance: 2000,
                    heading: 0,
                    pitch: 0
                ))
            }
        }
    }

    private func handleMapCameraChange(_ context: MapCameraUpdateContext) {
        // 性能优化逻辑可以在这里添加
    }


}

struct UserLocationAnnotation: View {
    @State private var pulseScale: CGFloat = 1.0
    @State private var rotationAngle: Double = 0
    @State private var glowOpacity: Double = 0.6

    var body: some View {
        ZStack {
            Circle()
                .stroke(.blue.opacity(0.4), lineWidth: 2)
                .frame(width: 32, height: 32)
                .scaleEffect(pulseScale)
                .opacity(2.0 - pulseScale)

            Circle()
                .fill(.blue.opacity(0.15))
                .frame(width: 20, height: 20)
                .blur(radius: 1)
                .opacity(glowOpacity)

            Circle()
                .fill(.blue.gradient)
                .frame(width: 12, height: 12)
                .overlay(
                    Circle()
                        .stroke(.white, lineWidth: 2)
                        .frame(width: 12, height: 12)
                )
                .shadow(color: .blue.opacity(0.3), radius: 2, x: 0, y: 1)

            Image(systemName: "location.north.line.fill")
                .font(.system(size: 6, weight: .bold))
                .foregroundStyle(.white)
                .rotationEffect(.degrees(rotationAngle))
                .symbolEffect(.pulse.byLayer, options: .repeating)
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: false)) {
                pulseScale = 2.0
            }

            withAnimation(.linear(duration: 3.0).repeatForever(autoreverses: false)) {
                rotationAngle = 360
            }

            withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                glowOpacity = 0.2
            }
        }
    }
}

struct UserLocationAnnotationView: View {
    var body: some View {
        UserLocationAnnotation()
    }
}

#Preview {
    @Previewable @State var position = MapCameraPosition.automatic
    @Previewable @State var showLabel = false

    ItineraryMapView(
        position: $position,
        locationManager: LocationManager.shared,
        interactionModes: .all,
        showUserLocation: true,
        showLocationLabel: $showLabel
    )
}