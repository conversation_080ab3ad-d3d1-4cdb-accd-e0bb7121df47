import SwiftUI
import AudioToolbox

/// 个人中心模块主入口视图
///
/// 作为ProfileCenter模块的统一入口和协调者，负责整合各功能模块，提供统一接口。
/// 遵循四层架构规范，协调Core层数据、Features层功能和Shared层组件。
///
/// 功能特性:
/// - 用户资料展示和编辑
/// - 统计数据可视化展示
/// - 功能导航和路由管理
/// - 响应式布局和深色模式支持
///
/// 架构职责:
/// - 🚪 统一入口：模块的主入口和协调者
/// - 🔗 功能整合：整合各功能模块，提供统一接口
/// - 📱 对外接口：作为模块对外的唯一接口
/// - 🎯 路由管理：处理模块内部的导航和路由
///
/// - Note: 遵循项目四层架构规范，依赖Core层服务和Features层功能
/// - Warning: 大量数据加载时注意性能优化，使用懒加载策略
struct ProfileCenterView: View {
    // MARK: - Properties

    @StateObject private var viewModel = ProfileViewModel()

    // MARK: - State

    @State private var showSettings = false
    @State private var showAbout = false
    @State private var showUserProfile = false
    @State private var animateOnAppear = false
    @State private var navigateToMyWorks = false

    @Environment(\.authenticationManager) private var authManager

    // 添加状态监听，确保UI响应认证状态变化
    @State private var authenticationState: AuthenticationModels.AuthState = .unauthenticated

    // MARK: - Body

    var body: some View {
        NavigationStack {
            content
                .navigationTitle("")
                .navigationBarTitleDisplayMode(.inline)
                .showTabBar()
                .navigationDestination(isPresented: $navigateToMyWorks) {
                    myWorksDestination
                }
        }
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1)) {
                animateOnAppear = true
            }
            // 同步认证状态
            authenticationState = authManager.authenticationState
        }
        .onReceive(authManager.$authenticationState) { newState in
            // 监听认证状态变化，确保UI及时更新
            Log.debug("🔐 [ProfileCenterView] Auth state changed: \(newState)")
            authenticationState = newState
        }
    }

    /// 主内容区域
    private var content: some View {
        VStack(spacing: 0) {
            // 顶部工具栏
            topToolbar
                .padding(.top, 8)
                .padding(.horizontal)

            // 用户信息区域
            userInfoSection
                .padding(.top, 20)
            // 主要内容
            mainContentCard
        }
        .padding(.horizontal)
        .appBackground() // 使用App统一背景
        .sheet(isPresented: $showSettings) {
            settingsSheet
        }
        .sheet(isPresented: $showAbout) {
            aboutSheet
        }
        .fullScreenCover(isPresented: $showUserProfile) {
            userProfileSheet
        }
    }

    // MARK: - Components

    /// 顶部工具栏
    private var topToolbar: some View {
        HStack {
            Spacer()

            HStack(spacing: 20) {
                // 消息通知图标
                Button(action: {}) {
                    ZStack {
                        Image(systemName: "bell")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.primary)

                        // 红点提示
                        Circle()
                            .fill(Color.red)
                            .frame(width: 8, height: 8)
                            .offset(x: 8, y: -8)
                    }
                }

                // 设置图标
                Button(action: {
                    showSettings = true
                }) {
                    Image(systemName: "gearshape")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                }
            }
        }
    }

    /// 主要内容区域 - 直接显示在背景上
    private var mainContentCard: some View {
        TabBarAwareScrollView {
            VStack(spacing: 0) {
                // 统计数据和功能入口整合区域
                integratedStatsAndFunctionsSection
                    .padding(.top, 20)

                // 更多功能区域
                moreToolsSection
                    .padding(.top, 24)
                    .padding(.bottom, 40)
            }
        }
        .opacity(animateOnAppear ? 1 : 0)
        .offset(y: animateOnAppear ? 0 : 20)
    }

    /// 用户信息区域 - 使用认证状态响应式组件
    private var userInfoSection: some View {
        VStack(spacing: 0) {
            // 使用认证状态响应式修饰符，整体切换用户信息内容
            Color.clear
                .frame(height: 0)
                .authenticationAware(
                    authenticated: {
                        // 登录后的完整用户信息视图
                        authenticatedUserInfoView
                    },
                    unauthenticated: {
                        // 未登录时的登录提示视图
                        unauthenticatedUserInfoView
                    }
                )

            // 底部分隔线
            Rectangle()
                .fill(Color.gray.opacity(0.15))
                .frame(height: 0.5)
                .padding(.horizontal, 24)
        }
    }

    /// 统计数据和功能入口整合区域
    private var integratedStatsAndFunctionsSection: some View {
        VStack(spacing: 20) {
            // 统计数据部分 - 认证感知显示
            Color.clear
                .frame(height: 0)
                .authenticationAware(
                    authenticated: {
                        // 登录时显示真实统计数据
                        HStack(spacing: 0) {
                            StatItemView(title: "被推荐", value: viewModel.userStats.recommendations.formatCount())
                            StatItemView(title: "被喜欢", value: viewModel.userStats.likes.formatCount())
                            StatItemView(title: "作品数", value: viewModel.userStats.posts.formatCount())
                        }
                    },
                    unauthenticated: {
                        // 未登录时显示占位符
                        HStack(spacing: 0) {
                            StatItemView(title: "被推荐", value: "--")
                            StatItemView(title: "被喜欢", value: "--")
                            StatItemView(title: "作品数", value: "--")
                        }
                    }
                )
            .padding(.horizontal, DesignSystemConstants.Spacing.standard)
            .padding(.top, 20)

            // 功能按钮部分
            HStack(spacing: 12) {
                // 我的作品 - 较小按钮
                myWorksButton
                    .frame(width: 100, height: 82)
                    .background(
                        LinearGradient(
                            colors: [
                                Color.blue.opacity(0.9),
                                Color.blue.opacity(0.85)
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.large))

                // 发布作品 - 主要功能，占据更大空间
                publishWorkButton
                    .frame(height: 82)
                    .background(
                        LinearGradient(
                            colors: [
                                Color.blue.opacity(0.85),
                                Color.purple.opacity(0.8)
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.large))
            }
            .padding(.horizontal, DesignSystemConstants.Spacing.standard)
            .padding(.bottom, 20)
        }
        .background(
            RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.large)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }

    /// 发布作品按钮 - 透明背景版本
    private var publishWorkButton: some View {
        Button(action: {
            // 检查认证状态，未登录时唤起登录界面
            authManager.requireAuthentication {
                // 认证成功后的发布功能
                print("发布作品功能")
            }
        }) {
            HStack(spacing: 16) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 50, height: 50)

                    Image(systemName: "plus")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                }

                // 中间文字 - 修复对齐
                VStack(alignment: .leading, spacing: 4) {
                    Text("发布作品")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)

                    Text("分享精彩旅程")
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.9))
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 20, height: 20)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding(.horizontal, 20)
        }
    }

    /// 我的作品按钮 - 透明背景版本
    private var myWorksButton: some View {
        Button(action: {
            // 检查认证状态，未登录时唤起登录界面
            authManager.requireAuthentication {
                // 认证成功后导航到我的作品页面
                navigateToMyWorks = true
            }
        }) {
            VStack(spacing: 8) {
                // 图标
                ZStack {
                    Circle()
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 36, height: 36)

                    Image(systemName: "square.stack.3d.up")
                        .font(.callout)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }

                // 文字
                Text("我的作品")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 更多功能区域
    private var moreToolsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section 标题
            HStack {
                Text("更多")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            // 功能网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 4), spacing: 20) {
                toolItem(icon: "play.circle.fill", title: "音频测试", color: .blue)
                toolItem(icon: "clock", title: "浏览历史", color: .gray)
                toolItem(icon: "questionmark.circle", title: "帮助中心", color: .gray)
                toolItem(icon: "exclamationmark.triangle", title: "意见反馈", color: .gray)
                toolItem(icon: "info.circle", title: "关于我们", color: .gray)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.large)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }

    /// 功能项组件
    private func toolItem(icon: String, title: String, color: Color) -> some View {
        Button(action: {
            // 根据功能标题执行对应操作
            switch title {
            case "音频测试":
                handleAudioTest()
            case "关于我们":
                showAbout = true
            case "浏览历史":
                ToastManager.shared.show("浏览历史功能开发中，敬请期待", style: .info)
            case "帮助中心":
                ToastManager.shared.show("帮助中心功能开发中，敬请期待", style: .info)
            case "意见反馈":
                ToastManager.shared.show("意见反馈功能开发中，敬请期待", style: .info)
            default:
                break
            }
        }) {
            VStack(spacing: 8) {
                // 图标背景
                ZStack {
                    Circle()
                        .fill(Color(.systemGray6))
                        .frame(width: 44, height: 44)

                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(color)
                }

                // 标题
                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
        }
    }

    // MARK: - Sheet Views

    /// 设置弹窗
    private var settingsSheet: some View {
        NavigationStack {
            List {
                // 账户设置
                Section("账户") {
                    settingRow(
                        title: "个人资料",
                        subtitle: "编辑个人信息",
                        icon: "person.crop.circle",
                        color: .blue
                    )

                    settingRow(
                        title: "账户安全",
                        subtitle: "密码和安全设置",
                        icon: "lock.shield",
                        color: .green
                    )
                }

                // 偏好设置
                Section("偏好") {
                    settingRow(
                        title: "通知设置",
                        subtitle: "管理推送通知",
                        icon: "bell",
                        color: .orange
                    )

                    settingRow(
                        title: "隐私设置",
                        subtitle: "控制信息可见性",
                        icon: "eye.slash",
                        color: .purple
                    )

                    settingRow(
                        title: "旅行偏好",
                        subtitle: "设置旅行类型和兴趣",
                        icon: "heart.text.square",
                        color: .pink
                    )
                }

                // 应用信息
                Section("应用") {
                    settingRow(
                        title: "关于我们",
                        subtitle: "应用信息和版本",
                        icon: "info.circle",
                        color: .blue
                    ) {
                        showAbout = true
                        showSettings = false
                    }
                }

                // 账户操作
                Section("账户操作") {
                    settingRow(
                        title: "退出登录",
                        subtitle: "退出当前账户",
                        icon: "rectangle.portrait.and.arrow.right",
                        color: .red
                    ) {
                        handleLogout()
                    }
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("完成") {
                    showSettings = false
                }
            )
        }
    }

    /// 设置行项目
    private func settingRow(title: String, subtitle: String, icon: String, color: Color, action: (() -> Void)? = nil) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 18))
                .foregroundColor(color)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.gray.opacity(0.6))
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            action?()
        }
    }

    /// 用户个人主页弹窗
    private var userProfileSheet: some View {
        NavigationStack {
            UserProfileView(
                username: "旅行者",
                avatarUrl: "https://picsum.photos/200",
                isCurrentUser: true,
                contentTags: [
                    "旅途规划", "网红打卡", "美食探店",
                    "历史文化", "自然风光", "城市漫步",
                    "特色民宿", "艺术展览", "主题乐园",
                    "购物指南", "交通攻略", "季节限定",
                    "摄影圣地", "亲子游玩", "夜生活"
                ]
            )
        }
    }

    /// 我的作品页面目标
    private var myWorksDestination: some View {
        MyWorksView(
            username: viewModel.userProfile?.displayName ?? "用户"
        )
        .hideTabBar()
    }

    // MARK: - Actions

    /// 处理音频测试
    private func handleAudioTest() {
        // 获取音频文件 URL
        guard let audioURL = Bundle.main.url(forResource: "voice_simple", withExtension: "mp3") else {
            ToastManager.shared.show("❌ 找不到测试音频文件", style: .error)
            Log.error("❌ 找不到音频文件: voice_simple.mp3")
            return
        }

        Log.info("🎵 音频文件路径: \(audioURL.path)")
        Log.info("🎵 音频文件大小: \((try? audioURL.resourceValues(forKeys: [.fileSizeKey]))?.fileSize ?? 0) bytes")

        // 使用 AudioPlayerAPI 播放音频文件
        AudioPlayerAPI.shared.playAudio(
            title: "路亦语音示例",
            audioURL: audioURL
        )

        // 显示提示信息
        ToastManager.shared.show("🎵 正在播放路亦语音示例", style: .success)
        Log.info("🎵 开始播放音频文件: \(audioURL.lastPathComponent)")
    }

    /// 处理退出登录
    private func handleLogout() {
        Task {
            // 使用全局AuthenticationManager进行登出
            await AuthenticationManager.shared.logout()

            // 关闭设置弹窗
            showSettings = false

            // 全局AuthenticationManager会自动处理登录界面显示
            // 不需要手动设置 showLogin = true
        }
    }

    /// 关于应用弹窗
    private var aboutSheet: some View {
        NavigationStack {
            VStack(spacing: 24) {
                // 应用图标
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "map.fill")
                            .font(.system(size: 32))
                            .foregroundColor(.white)
                    )

                VStack(spacing: 8) {
                    Text("路亦")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("版本 1.0.0")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                VStack(spacing: 16) {
                    Text("探索世界，记录美好")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)

                    Text("© 2024 路亦团队")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("关于应用")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("完成") {
                    showAbout = false
                }
            )
        }
    }

    // MARK: - Authentication State Views

    /// 登录后的完整用户信息视图
    private var authenticatedUserInfoView: some View {
        HStack(alignment: .center, spacing: 20) {
            // 左侧用户信息
            VStack(alignment: .leading, spacing: 8) {
                // 用户名和编辑按钮
                HStack(alignment: .center, spacing: 8) {
                    Text(viewModel.displayName)
                        .font(.system(size: 22, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    // 编辑按钮
                    Button(action: {
                        // 编辑用户信息功能
                    }) {
                        Image(systemName: "square.and.pencil")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.blue)
                            .frame(width: 28, height: 28)
                            .background(
                                Circle()
                                    .fill(Color.blue.opacity(0.08))
                            )
                    }

                    Spacer(minLength: 0)
                }

                // ID和归属地信息（同一行）
                HStack(alignment: .center, spacing: 8) {
                    // ID标签
                    HStack(spacing: 4) {
                        Text("ID:")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(.blue.opacity(0.8))

                        Text(viewModel.userProfile?.id ?? "")
                            .font(.system(size: 13, weight: .medium, design: .monospaced))
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.blue.opacity(0.08))
                    )

                    // 归属地标签
                    HStack(spacing: 4) {
                        Image(systemName: "location.fill")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(.green.opacity(0.8))

                        Text(viewModel.userLocation)
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.green.opacity(0.08))
                    )

                    Spacer(minLength: 0)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 右侧用户头像
            authenticatedAvatarView
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
    }

    /// 未登录时的完整用户信息视图
    private var unauthenticatedUserInfoView: some View {
        HStack(alignment: .center, spacing: 20) {
            // 左侧登录提示信息
            VStack(alignment: .leading, spacing: 8) {
                // 登录提示 - 可点击的文本
                HStack(alignment: .center, spacing: 0) {
                    Text("请先")
                        .font(.system(size: 26, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Button(action: {
                        authManager.presentLogin()
                    }) {
                        Text("登录")
                            .font(.system(size: 26, weight: .bold, design: .rounded))
                            .foregroundColor(.blue)
                            .underline()
                    }
                    .buttonStyle(PlainButtonStyle())

                    Spacer(minLength: 0)
                }

                // 提示语/Slogan
                Text("探索旅途，发现更多精彩")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 右侧默认头像
            unauthenticatedAvatarView
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
    }

    /// 登录后的头像视图
    private var authenticatedAvatarView: some View {
        Button(action: { viewModel.triggerAvatarAnimation() }) {
            ZStack {
                // 头像光环效果
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.blue.opacity(0.3),
                                Color.purple.opacity(0.2)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 76, height: 76)

                // 头像
                CachedAsyncImage(
                    url: viewModel.avatarUrl.flatMap { URL(string: $0) }
                ) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.blue.opacity(0.15),
                                    Color.purple.opacity(0.15)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .overlay(
                            Image(systemName: "person.fill")
                                .foregroundColor(.blue.opacity(0.7))
                                .font(.title)
                        )
                } errorView: {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.blue.opacity(0.15),
                                    Color.purple.opacity(0.15)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .overlay(
                            Image(systemName: "person.fill")
                                .foregroundColor(.blue.opacity(0.7))
                                .font(.title)
                        )
                }
                .frame(width: 68, height: 68)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(0.8), lineWidth: 2)
                )
            }
            .scaleEffect(viewModel.avatarScale)
            .offset(y: viewModel.avatarOffset)
        }
    }

    /// 未登录时的头像视图
    private var unauthenticatedAvatarView: some View {
        Button(action: {
            authManager.presentLogin()
        }) {
            ZStack {
                // 默认头像背景
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.gray.opacity(0.2),
                                Color.gray.opacity(0.15)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 76, height: 76)

                // 默认头像
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.gray.opacity(0.15),
                                Color.gray.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 68, height: 68)
                    .overlay(
                        Image(systemName: "person.fill")
                            .foregroundColor(.gray.opacity(0.6))
                            .font(.title)
                    )
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.8), lineWidth: 2)
                    )
            }
        }
    }
}

// MARK: - Internal Components

/// 统计数据项展示组件
private struct StatItemView: View {
    let title: String
    let value: String

    var body: some View {
        VStack(alignment: .center, spacing: 4) {
            Text(value)
                .font(.system(size: 18, weight: .bold, design: .rounded))
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)

            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, alignment: .center)
        .padding(.vertical, 8)
    }
}

// MARK: - Preview

#Preview("个人中心 - 页面跳转") {
    ProfileCenterView()
        .environmentObject(TabBarStateManager.shared)
}

#Preview("With Mock Data") {
    let view = ProfileCenterView()
    return view
        .environmentObject(TabBarStateManager.shared)
        .onAppear {
            // 预览时使用Mock数据
        }
}
