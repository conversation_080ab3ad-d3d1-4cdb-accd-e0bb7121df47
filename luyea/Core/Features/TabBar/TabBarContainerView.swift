import SwiftUI

/// TabBar 容器视图
///
/// 统一的TabBar容器组件，整合TabBar内容视图和TabBar控制栏。
/// 完全依赖TabBarStateManager进行状态管理，不维护内部状态。
///
/// 核心特性：
/// - 无状态设计：完全依赖TabBarStateManager
/// - 响应式布局：自动响应状态变化
/// - 统一管理：集成内容视图和控制栏
/// - 环境传递：向子视图提供状态管理器
///
/// 使用场景：
/// - 作为应用主容器使用
/// - 替代传统的TabView实现
/// - 提供更灵活的TabBar控制
struct TabBarContainerView: View {

    // MARK: - Environment & State

    /// TabBar状态管理器
    /// 通过环境对象获取统一的状态管理器实例
    @EnvironmentObject private var tabBarState: TabBarStateManager

    // MARK: - Body

    var body: some View {
        ZStack {
            // 主内容区域 - 固定布局，不受TabBar影响
            TabBarContentView()
                .ignoresSafeArea(.keyboard, edges: .bottom)
                .frame(maxWidth: .infinity, maxHeight: .infinity)

            // TabBar浮动层 - 独立于主内容布局
            VStack {
                Spacer()
                if tabBarState.isVisible {
                    TabBarView()
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
            .animation(.easeInOut(duration: 0.3), value: tabBarState.isVisible)
        }
        .background(Color.clear)
    }
}
