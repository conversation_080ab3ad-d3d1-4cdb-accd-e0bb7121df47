import SwiftUI

/// 当前位置选择区域组件
///
/// 显示当前位置信息，支持快速选择当前位置作为出发地。
/// 提供清晰的视觉反馈和交互体验。
struct CurrentLocationSection: View {
    
    // MARK: - Properties

    let currentLocation: String
    let onLocationSelected: (String) -> Void

    // MARK: - Computed Properties

    /// 当前位置是否可用
    private var isCurrentLocationAvailable: Bool {
        return !currentLocation.contains("正在获取") &&
               !currentLocation.contains("无法获取") &&
               !currentLocation.contains("未知位置") &&
               currentLocation != "正在获取当前位置..."
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            sectionHeader
            locationButton
        }
    }
    
    // MARK: - Private Views
    
    /// 区域标题
    private var sectionHeader: some View {
        HStack {
            Image(systemName: "location.fill")
                .font(.title3)
                .foregroundColor(isCurrentLocationAvailable ? .blue : .gray)

            Text("当前位置")
                .font(.headline.weight(.medium))
                .foregroundColor(.primary)

            Spacer()
        }
    }
    
    /// 位置选择按钮
    private var locationButton: some View {
        Button(action: {
            if isCurrentLocationAvailable {
                onLocationSelected(currentLocation)
            }
        }) {
            HStack(spacing: 12) {
                Image(systemName: isCurrentLocationAvailable ? "location.circle.fill" : "location.slash.circle")
                    .font(.title2)
                    .foregroundColor(isCurrentLocationAvailable ? .blue : .gray)

                VStack(alignment: .leading, spacing: 2) {
                    Text(currentLocation)
                        .font(.body.weight(.medium))
                        .foregroundColor(isCurrentLocationAvailable ? .primary : .secondary)

                    Text(isCurrentLocationAvailable ? "使用当前位置作为出发地" : "位置信息不可用")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if isCurrentLocationAvailable {
                    Image(systemName: "chevron.right")
                        .font(.caption.weight(.semibold))
                        .foregroundColor(.gray)
                } else {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.caption.weight(.semibold))
                        .foregroundColor(.orange)
                }
            }
            .padding(16)
            .background(isCurrentLocationAvailable ? Color(.systemBackground) : Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(color: .black.opacity(isCurrentLocationAvailable ? 0.05 : 0.02), radius: isCurrentLocationAvailable ? 4 : 2, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!isCurrentLocationAvailable)
    }
}

// MARK: - Preview

#Preview {
    CurrentLocationSection(
        currentLocation: "北京市朝阳区",
        onLocationSelected: { _ in }
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
