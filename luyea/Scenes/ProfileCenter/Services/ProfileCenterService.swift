import Foundation
import Combine

/// 个人中心服务协议
protocol ProfileCenterServiceProtocol {
    /// 获取用户资料
    func fetchUserProfile() async throws -> ProfileModels.UserProfile
    
    /// 更新用户资料
    func updateUserProfile(_ profile: ProfileModels.UserProfile) async throws -> ProfileModels.UserProfile
    
    /// 上传头像
    func uploadAvatar(_ imageData: Data) async throws -> String
    
    /// 获取用户统计信息
    func fetchUserStats() async throws -> ProfileModels.UserStats
}

/// 个人中心服务实现
final class ProfileCenterService: ProfileCenterServiceProtocol {
    
    // MARK: - Dependencies
    
    /// 网络服务
    private let networkService: NetworkServiceProtocol
    
    // MARK: - Cache Properties
    
    /// 用户资料缓存
    private var userProfileCache: ProfileModels.UserProfile?
    
    /// 缓存时间戳
    private var cacheTimestamp: Date?
    
    /// 缓存有效期（秒）
    private let cacheValidityDuration: TimeInterval = 300 // 5分钟
    
    // MARK: - Initialization
    
    init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }
    
    // MARK: - Public Methods
    
    /// 获取用户资料
    func fetchUserProfile() async throws -> ProfileModels.UserProfile {
        // 检查缓存是否有效
        if let cachedProfile = getCachedUserProfile() {
            return cachedProfile
        }
        
        // 从网络获取数据
        let profile: ProfileModels.UserProfile = try await networkService.request(
            .get(APIPaths.profileCurrent)
        )
        
        // 更新缓存
        updateUserProfileCache(profile)
        
        return profile
    }
    
    /// 更新用户资料
    func updateUserProfile(_ profile: ProfileModels.UserProfile) async throws -> ProfileModels.UserProfile {
        let profileData = try JSONEncoder().encode(profile)
        let request = APIRequest
            .put(APIPaths.profileCurrent)
            .body(profileData)

        let updatedProfile: ProfileModels.UserProfile = try await networkService.request(request)

        // 更新缓存
        updateUserProfileCache(updatedProfile)

        return updatedProfile
    }

    /// 上传头像
    func uploadAvatar(_ imageData: Data) async throws -> String {
        let request = APIRequest
            .post(APIPaths.profileCurrent + "/avatar")
            .body(imageData)

        let response: ProfileModels.AvatarUploadResponse = try await networkService.request(request)

        // 清除缓存，强制重新获取用户资料
        clearCache()

        return response.avatarUrl
    }
    
    /// 获取用户统计信息
    func fetchUserStats() async throws -> ProfileModels.UserStats {
        let stats: ProfileModels.UserStats = try await networkService.request(
            .get(APIPaths.profileStats)
        )
        
        return stats
    }
    
    // MARK: - Private Methods - Cache Management
    
    /// 获取缓存的用户资料
    private func getCachedUserProfile() -> ProfileModels.UserProfile? {
        guard isCacheValid() else { return nil }
        return userProfileCache
    }
    
    /// 检查缓存是否有效
    private func isCacheValid() -> Bool {
        guard let timestamp = cacheTimestamp else { return false }
        return Date().timeIntervalSince(timestamp) < cacheValidityDuration
    }
    
    /// 更新用户资料缓存
    private func updateUserProfileCache(_ profile: ProfileModels.UserProfile) {
        userProfileCache = profile
        cacheTimestamp = Date()
    }
    
    /// 清除缓存
    private func clearCache() {
        userProfileCache = nil
        cacheTimestamp = nil
    }
}

// MARK: - Business Logic Extensions

extension ProfileCenterService {
    
    /// 验证用户资料数据
    func validateUserProfile(_ profile: ProfileModels.UserProfile) -> ProfileValidationResult {
        var errors: [ProfileValidationError] = []

        // 验证显示名称
        if profile.displayName.isEmpty {
            errors.append(.emptyDisplayName)
        } else if profile.displayName.count < 2 {
            errors.append(.displayNameTooShort)
        } else if profile.displayName.count > 20 {
            errors.append(.displayNameTooLong)
        }

        // 验证归属地（可选）
        if let location = profile.location, location.count > 50 {
            errors.append(.locationTooLong)
        }

        return ProfileValidationResult(isValid: errors.isEmpty, errors: errors)
    }

}

// MARK: - Supporting Types

/// 用户资料验证结果
struct ProfileValidationResult {
    let isValid: Bool
    let errors: [ProfileValidationError]
}

/// 用户资料验证错误
enum ProfileValidationError: Error, LocalizedError {
    case emptyDisplayName
    case displayNameTooShort
    case displayNameTooLong
    case locationTooLong

    var errorDescription: String? {
        switch self {
        case .emptyDisplayName:
            return "显示名称不能为空"
        case .displayNameTooShort:
            return "显示名称至少需要2个字符"
        case .displayNameTooLong:
            return "显示名称不能超过20个字符"
        case .locationTooLong:
            return "归属地不能超过50个字符"
        }
    }
}
