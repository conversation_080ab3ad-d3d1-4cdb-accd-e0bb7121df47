import SwiftUI

struct AdContentView: View {
    let adId: String
    @State private var isAdLoaded = false
    @State private var error: Error?
    @State private var adContent: AdContent?
    
    var body: some View {
        GeometryReader { geometry in
            Group {
                if let error = error {
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 40))
                            .foregroundColor(.orange)
                        Text("广告加载失败")
                            .font(.headline)
                        Text(error.localizedDescription)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                } else if let adContent = adContent {
                    VStack {
                        CachedAsyncImage(
                            url: URL(string: adContent.imageUrl)
                        ) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                        } placeholder: {
                            ProgressView()
                                .frame(maxWidth: .infinity, maxHeight: .infinity)
                        } errorView: {
                            Image(systemName: "photo")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                                .frame(maxWidth: .infinity, maxHeight: .infinity)
                        }
                        
                        if let title = adContent.title {
                            Text(title)
                                .font(.headline)
                                .padding(.top)
                        }
                        
                        if let description = adContent.description {
                            Text(description)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.top, 4)
                        }
                    }
                    .padding()
                } else {
                    ProgressView()
                }
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
        .onAppear {
            loadAd()
        }
    }
    
    private func loadAd() {
        Task {
            do {
                let content = try await AdService.shared.loadAd(id: adId)
                await MainActor.run {
                    self.adContent = content
                    self.isAdLoaded = true
                }
            } catch {
                await MainActor.run {
                    self.error = error
                }
            }
        }
    }
}

struct AdContent {
    let imageUrl: String
    let title: String?
    let description: String?
    let actionURL: String?
}

class AdService {
    static let shared = AdService()
    
    private init() {}
    
    func loadAd(id: String) async throws -> AdContent {
        // 这里应该实现真实的广告加载逻辑
        // 目前返回模拟数据
        try await Task.sleep(nanoseconds: 1_000_000_000)
        return AdContent(
            imageUrl: "https://example.com/ad.jpg",
            title: "广告标题",
            description: "广告描述",
            actionURL: "https://example.com"
        )
    }
} 