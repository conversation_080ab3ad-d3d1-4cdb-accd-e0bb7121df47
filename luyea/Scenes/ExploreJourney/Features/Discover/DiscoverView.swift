import SwiftUI

/// 发现功能主视图
///
/// 负责管理发现页面的所有UI组件和交互逻辑。
/// 这是发现功能的主要容器，包含筛选栏、内容展示和话题抽屉。
struct DiscoverView: View {
    
    // MARK: - ViewModels

    /// 发现页面视图模型（现在也管理话题数据）
    @ObservedObject var discoverViewModel: DiscoverViewModel

    /// 探索旅途主视图模型（用于话题抽屉状态）
    @ObservedObject var exploreViewModel: ExploreJourneyViewModel
    
    // MARK: - Bindings
    
    /// 选中的发现内容项（用于导航）
    @Binding var selectedDiscoverItem: DiscoverItem?

    /// 话题按钮位置
    @State private var topicButtonPosition: CGRect = .zero
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 主要内容区域
            mainContent
                .coordinateSpace(name: "DiscoverView")
                .onPreferenceChange(TopicButtonPositionKey.self) { position in
                    topicButtonPosition = position
                }

            // 话题抽屉悬浮层
            if exploreViewModel.isTopicDrawerShowing {
                topicDrawerOverlay
            }
        }
    }
    
    // MARK: - Content Components
    
    /// 主要内容区域
    private var mainContent: some View {
        DiscoverContentView(
            viewModel: discoverViewModel,
            isTopicDrawerShowing: $exploreViewModel.isTopicDrawerShowing,
            selectedTopicIds: $discoverViewModel.selectedTopicIds,
            onCardTapped: { item in
                selectedDiscoverItem = item
            }
        )
    }

    /// 话题抽屉悬浮层
    private var topicDrawerOverlay: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 顶部占位区域，确保抽屉从话题按钮下方开始
                Rectangle()
                    .fill(Color.clear)
                    .frame(height: max(topicButtonPosition.maxY, 0))

                // 话题抽屉内容
                TopicDrawerView(
                    isShowing: $exploreViewModel.isTopicDrawerShowing,
                    discoverViewModel: discoverViewModel
                )

                Spacer()
            }
        }
        .zIndex(100)
        .opacity(exploreViewModel.isTopicDrawerShowing ? 1 : 0)
        .animation(.easeOut(duration: 0.3), value: exploreViewModel.isTopicDrawerShowing)
    }
}

// MARK: - Preview

#Preview {
    DiscoverView(
        discoverViewModel: DiscoverViewModel(),
        exploreViewModel: ExploreJourneyViewModel(),
        selectedDiscoverItem: .constant(nil)
    )
}
