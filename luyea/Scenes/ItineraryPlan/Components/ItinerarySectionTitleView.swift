import SwiftUI

/// ItinerarySectionTitleView
/// 用于分区主标题展示，风格统一。
struct ItinerarySectionTitleView: View {
    let title: String
    var body: some View {
        Text(title)
            .font(.system(size: 17, weight: .bold, design: .rounded))
            .foregroundColor(.primary)
            .padding(.leading, 4)
            .padding(.top, 8)
            .padding(.bottom, 2)
            .frame(maxWidth: .infinity, alignment: .leading)
    }
} 