import SwiftUI

/// 动画胶囊按钮组件
///
/// 提供可展开和收起的胶囊形状按钮，支持图标和文本显示。
/// 采用流畅的动画效果，提供优秀的用户体验。
struct AnimatedPillButton: View {
    
    // MARK: - Properties
    
    @Binding var isExpanded: Bool
    let icon: String
    let iconColor: Color
    let label: String
    let expandedWidth: CGFloat
    let action: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        Button(action: action) {
            content
        }
        .buttonStyle(.plain)
    }
    
    // MARK: - Private Views
    
    @ViewBuilder
    private var content: some View {
        if isExpanded {
            expandedView
        } else {
            collapsedView
        }
    }
    
    /// 展开状态视图
    private var expandedView: some View {
        ZStack(alignment: .trailing) {
            Capsule().fill(.regularMaterial)
            HStack(spacing: 2) {
                Text(label)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(.primary)
                    .padding(.leading, 12)

                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(iconColor)
                    .frame(width: 44, height: 44)
                    .background(.thinMaterial, in: Circle())
            }
            .padding(.trailing, 4)
        }
        .frame(width: expandedWidth, height: 44)
        .shadow(
            color: .black.opacity(0.15),
            radius: 8,
            y: 4
        )
        .animation(.spring(
            response: DesignSystemConstants.Animation.Spring.response,
            dampingFraction: DesignSystemConstants.Animation.Spring.damping
        ), value: isExpanded)
    }
    
    /// 收起状态视图
    private var collapsedView: some View {
        ZStack {
            Circle()
                .fill(.regularMaterial)
                .frame(width: 44, height: 44)
                .shadow(
                    color: .black.opacity(0.15),
                    radius: 8,
                    y: 4
                )

            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(iconColor)
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        AnimatedPillButton(
            isExpanded: .constant(false),
            icon: "location.fill",
            iconColor: .blue,
            label: "当前位置",
            expandedWidth: 100,
            action: {}
        )
        
        AnimatedPillButton(
            isExpanded: .constant(true),
            icon: "mappin.circle.fill",
            iconColor: .green,
            label: "显示收藏",
            expandedWidth: 100,
            action: {}
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
