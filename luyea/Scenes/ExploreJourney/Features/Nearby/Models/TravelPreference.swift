import Foundation

struct TravelPreference: Identifiable, Hashable {
    let id = UUID()
    let emoji: String
    let name: String
}

extension TravelPreference {
    static let classicRoute = TravelPreference(emoji: "🗺️", name: "网红打卡")
    static let eatAndDrink = TravelPreference(emoji: "🍽️", name: "吃吃喝喝")
    static let nicheExploration = TravelPreference(emoji: "🕵️", name: "小众探索")
    static let photoTaking = TravelPreference(emoji: "📸", name: "拍照出片")
    static let cityFireworks = TravelPreference(emoji: "🏙️", name: "城市烟火")
    static let artsAndHistory = TravelPreference(emoji: "📜", name: "文艺历史")

    static let samples: [TravelPreference] = [
        .classicRoute,
        .eatAndDrink,
        .nicheExploration,
        .photoTaking,
        .cityFireworks,
        .artsAndHistory
    ]
}
