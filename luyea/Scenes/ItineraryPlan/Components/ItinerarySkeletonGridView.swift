import SwiftUI

/// ItinerarySkeletonGridView
/// 行程卡片骨架屏加载视图。
struct ItinerarySkeletonGridView: View {
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 10), count: 2), spacing: 10) {
            ForEach(0..<6, id: \ .self) { _ in
                RoundedRectangle(cornerRadius: 16, style: .continuous)
                    .fill(Color.gray.opacity(0.08))
                    .frame(height: 90)
                    .shimmer()
            }
        }
        .padding(.horizontal, 10)
        .padding(.top, 16)
    }
} 