import Combine
import Foundation

/// MyWorks模块ViewModel
///
/// 负责管理"我的作品"页面的数据状态和业务逻辑。
/// 提供统计数据、分页加载、搜索筛选等功能。
@MainActor
final class MyWorksViewModel: ObservableObject {

    // MARK: - Types

    /// 加载状态枚举
    enum LoadingState {
        case idle           // 空闲状态
        case loading        // 数据加载中（筛选、搜索、排序等）
        case loadingMore    // 分页加载更多
        case refreshing     // 下拉刷新中
    }

    // MARK: - Published Properties

    /// 作品列表数据
    @Published var works: [MyWorksModels.WorkItem] = []

    /// 统计数据
    @Published var stats: MyWorksModels.WorksStats = .empty

    /// 可用话题列表
    @Published var availableTopics: [MyWorksModels.TopicStats] = []

    /// 当前选中的话题
    @Published var selectedTopic: MyWorksModels.TopicStats?

    /// 搜索关键词
    @Published var searchKeyword: String = ""

    /// 当前排序类型
    @Published var currentSortType: MyWorksModels.WorksSortType = .createTime

    /// 加载状态
    @Published var loadingState: LoadingState = .idle

    /// 是否已经进行过初始加载
    @Published private var hasInitialLoaded: Bool = false

    /// 错误信息
    @Published var error: NetworkError?

    // MARK: - Pagination Properties

    /// 当前页码
    @Published var currentPage: Int = 1

    /// 是否还有更多数据
    @Published var hasMore: Bool = false

    // MARK: - Computed Properties

    // MARK: - Computed Properties

    /// 是否可以加载更多数据
    var canLoadMore: Bool {
        loadingState == .idle && hasMore
    }

    /// 是否为空状态（已加载完成但无数据）
    var isEmpty: Bool {
        works.isEmpty && loadingState == .idle && hasInitialLoaded
    }

    /// 是否正在加载（显示覆盖层动画）
    var isLoading: Bool {
        loadingState == .loading || loadingState == .refreshing
    }

    /// 是否正在加载更多（显示底部指示器）
    var isLoadingMore: Bool {
        loadingState == .loadingMore
    }

    /// 是否正在刷新
    var isRefreshing: Bool {
        loadingState == .refreshing
    }

    /// 是否有错误
    var hasError: Bool {
        error != nil
    }

    /// 所有话题的作品总数
    var totalWorksCount: Int {
        availableTopics.reduce(0) { $0 + $1.worksCount }
    }

    // MARK: - Private Properties

    /// 我的作品服务
    private let myWorksService: MyWorksServiceProtocol

    /// Combine订阅集合
    private var cancellables = Set<AnyCancellable>()

    /// 分页大小
    private let pageSize = 20

    // MARK: - State Management Properties

    /// 上次搜索关键词（用于防抖和去重）
    private var lastSearchKeyword: String = ""

    /// 上次选中的话题ID（用于去重）
    private var lastSelectedTopicId: String?

    /// 上次排序类型（用于去重）
    private var lastSortType: MyWorksModels.WorksSortType = .createTime

    /// 搜索防抖器
    private let searchDebouncer = Debouncer(delay: 0.5)

    /// 加载更多触发记录（防止重复触发）
    private var loadMoreTriggeredItems = Set<String>()

    // MARK: - Initialization

    /// 初始化视图模型
    /// - Parameter myWorksService: 我的作品服务，默认使用默认实例
    init(myWorksService: MyWorksServiceProtocol = MyWorksService()) {
        self.myWorksService = myWorksService
    }



    // MARK: - Public Methods

    /// 加载初始数据
    ///
    /// 在页面首次加载时并行加载所有必要数据：
    /// - 统计数据：用户作品的统计信息
    /// - 话题列表：可用的话题标签
    /// - 作品列表：用户的作品数据
    ///
    /// - Note: 话题和统计数据相对稳定，只在初始化时加载
    func loadInitialData() {
        Task {
            await withTaskGroup(of: Void.self) { group in
                group.addTask { await self.loadStats() }
                group.addTask { await self.loadTopics() }
                group.addTask { await self.loadWorks(reset: true) }
            }
        }
    }

    /// 下拉刷新作品列表
    ///
    /// 用于用户主动下拉刷新操作，只刷新作品列表内容。
    /// 话题和统计数据相对稳定，不需要频繁更新。
    func refreshWorksList() {
        Task {
            // 设置刷新状态
            loadingState = .refreshing

            // 加载数据
            await loadWorks(reset: true)
        }
    }

    /// 刷新统计数据
    ///
    /// 适用场景：
    /// - 仅需要更新统计信息时（如点赞数变化）
    /// - 不涉及作品内容或话题变更的操作
    func refreshStats() {
        Task {
            await loadStats(refresh: true)
        }
    }

    /// 刷新话题数据
    ///
    /// 适用场景：
    /// - 管理员更新话题配置后
    /// - 系统话题列表发生变化时
    /// - 独立的话题数据更新需求
    func refreshTopics() {
        Task {
            await loadTopics()
        }
    }

    /// 刷新所有数据
    /// 用于用户发布、删除、编辑作品后需要重新加载所有数据的场景
    ///
    /// 适用场景：
    /// - 用户发布新作品（可能新增话题标签）
    /// - 用户删除作品（可能移除话题标签）
    /// - 用户编辑作品话题（可能影响话题统计）
    /// - 其他可能影响数据完整性的操作
    func refreshAllData() {
        Task {
            await withTaskGroup(of: Void.self) { group in
                group.addTask { await self.loadStats(refresh: true) }
                group.addTask { await self.loadTopics() }
                group.addTask { await self.loadWorks(reset: true) }
            }
        }
    }

    /// 加载更多作品
    /// 用于分页加载功能
    /// - Parameter triggeredByItemId: 触发加载的item ID，用于防重复触发
    func loadMoreWorks(triggeredByItemId: String? = nil) {
        guard canLoadMore else { return }

        // ✅ 防重复触发机制
        if let itemId = triggeredByItemId {
            guard !loadMoreTriggeredItems.contains(itemId) else { return }
            loadMoreTriggeredItems.insert(itemId)
        }

        Task {
            await loadWorks(reset: false)
        }
    }

    /// 搜索作品
    /// - Parameter keyword: 搜索关键词
    func searchWorks(_ keyword: String) {
        let trimmedKeyword = keyword.trimmingCharacters(in: .whitespacesAndNewlines)

        // 避免重复搜索
        guard trimmedKeyword != lastSearchKeyword else { return }

        // 使用新的通用防抖工具
        searchDebouncer.call {
            Task { @MainActor in
                self.performSearch(trimmedKeyword)
            }
        }
    }

    /// 执行搜索操作
    /// - Parameter keyword: 搜索关键词
    private func performSearch(_ keyword: String) {
        lastSearchKeyword = keyword
        searchKeyword = keyword
        reloadWorksWithNewParams()
    }

    func filterByTopic(_ topic: MyWorksModels.TopicStats?) {
        // 状态管理优化：避免重复筛选
        let newTopicId = topic?.id
        guard newTopicId != lastSelectedTopicId else { return }

        lastSelectedTopicId = newTopicId
        selectedTopic = topic
        reloadWorksWithNewParams()
    }

    /// 更改排序方式
    /// - Parameter sortType: 排序类型
    func changeSortType(_ sortType: MyWorksModels.WorksSortType) {
        // 状态管理优化：避免重复排序
        guard sortType != lastSortType else { return }

        lastSortType = sortType
        currentSortType = sortType
        reloadWorksWithNewParams()
    }

    /// 重新加载作品数据（用于筛选、搜索、排序等操作）
    ///
    /// 此方法用于所有会改变查询参数的操作：
    /// - 切换话题标签
    /// - 更改排序方式
    /// - 执行搜索
    /// - 清除搜索
    private func reloadWorksWithNewParams() {
        // 重置分页状态
        currentPage = 1
        hasMore = true

        // 清理加载更多触发记录
        loadMoreTriggeredItems.removeAll()

        // 立即设置加载状态，确保UI立即响应
        loadingState = .loading
        Task {
            await loadWorks(reset: true)
        }
    }
    
    /// 清除搜索
    func clearSearch() {
        // 取消正在进行的搜索防抖
        searchDebouncer.cancel()

        // 重置搜索相关状态
        searchKeyword = ""
        lastSearchKeyword = ""

        // 重新加载数据
        reloadWorksWithNewParams()
    }

    /// 内存优化：在内存警告时释放部分资源
    func optimizeMemoryUsage() {
        // 取消搜索防抖
        searchDebouncer.cancel()

        // 清理错误状态
        error = nil

        // 重置加载状态
        if loadingState == .loadingMore {
            loadingState = .idle
        }

        // ✅ 清理加载更多触发记录
        loadMoreTriggeredItems.removeAll()

        // 只清理图片缓存，不截断数据
        // 避免用户看到数据突然减少的问题
        ImageCacheService.shared.clearMemoryCache()
    }

    /// ✅ 添加deinit确保资源清理
    deinit {
        // Debouncer 会自动清理，无需手动处理
    }


    
    // MARK: - Private Methods


    
    private func loadStats(refresh: Bool = false) async {
        let startTime = CFAbsoluteTimeGetCurrent()

        if !refresh && loadingState == .idle {
            loadingState = .loading
        }

        // 临时存储结果
        var newStats: MyWorksModels.WorksStats = stats
        var requestError: Error?

        do {
            newStats = try await myWorksService.fetchWorksStats()
        } catch {
            requestError = error
        }

        // 智能动画时长
        if !refresh {
            let elapsedTime = CFAbsoluteTimeGetCurrent() - startTime
            let minimumVisibleTime: TimeInterval = 0.2

            if elapsedTime < minimumVisibleTime {
                let remainingTime = minimumVisibleTime - elapsedTime
                try? await Task.sleep(nanoseconds: UInt64(remainingTime * 1_000_000_000))
            }
        }

        // 动画结束后更新数据
        if let error = requestError {
            handleError(error, preserveData: true)
        } else {
            stats = newStats
        }

        if !refresh {
            loadingState = .idle
        }
    }

    private func loadTopics() async {
        let startTime = CFAbsoluteTimeGetCurrent()

        // 临时存储结果
        var newTopics: [MyWorksModels.TopicStats] = availableTopics
        var requestError: Error?

        do {
            newTopics = try await myWorksService.fetchTopicStats()
        } catch {
            requestError = error
        }

        // 智能动画时长
        let elapsedTime = CFAbsoluteTimeGetCurrent() - startTime
        let minimumVisibleTime: TimeInterval = 0.15

        if elapsedTime < minimumVisibleTime {
            let remainingTime = minimumVisibleTime - elapsedTime
            try? await Task.sleep(nanoseconds: UInt64(remainingTime * 1_000_000_000))
        }

        // 动画结束后更新数据
        if let error = requestError {
            handleError(error, preserveData: true)
        } else {
            availableTopics = newTopics
        }
    }
    
    /// 加载作品数据的核心方法
    ///
    /// - Parameter reset: 是否重置数据（true: 重新加载，false: 分页加载更多）
    private func loadWorks(reset: Bool) async {
        let startTime = CFAbsoluteTimeGetCurrent()

        // 设置加载状态（如果还没有设置的话）
        if reset {
            // 重置加载：保持已设置的状态（.loading 或 .refreshing）
            if loadingState == .idle {
                loadingState = .loading
            }
        } else {
            // 分页加载：设置为加载更多状态
            loadingState = .loadingMore
        }

        let page = reset ? 1 : currentPage + 1
        let params = MyWorksModels.WorksQueryParams(
            page: page,
            pageSize: pageSize,
            keyword: searchKeyword.isEmpty ? nil : searchKeyword,
            topicId: selectedTopic?.id,
            sortBy: currentSortType.rawValue
        )
        
        // 临时存储网络请求结果，不立即更新UI
        var newWorks: [MyWorksModels.WorkItem] = []
        var newCurrentPage: Int = currentPage
        var newHasMore: Bool = hasMore
        var requestError: Error?

        do {
            let response = try await myWorksService.fetchWorks(
                page: params.page,
                pageSize: params.pageSize,
                status: params.status,
                topic: params.topicId,
                keyword: params.keyword,
                sortBy: params.sortBy,
                order: "desc"  // 默认降序
            )

            // 使用Service合并数据
            newWorks = myWorksService.mergeWorksData(
                existing: works,
                newPage: response,
                isRefresh: reset
            )

            newCurrentPage = response.currentPage
            newHasMore = response.hasMore

        } catch {
            requestError = error
        }

        // 确保最小可见时间
        let elapsedTime = CFAbsoluteTimeGetCurrent() - startTime
        let minimumVisibleTime: TimeInterval = 0.3

        if elapsedTime < minimumVisibleTime {
            let remainingTime = minimumVisibleTime - elapsedTime
            try? await Task.sleep(nanoseconds: UInt64(remainingTime * 1_000_000_000))
        }

        // 动画时间结束后，同时更新数据和状态
        if let error = requestError {
            handleError(error, preserveData: !reset)
        } else {
            // 同时更新所有数据
            works = newWorks
            currentPage = newCurrentPage
            hasMore = newHasMore
        }

        // 标记已完成初始加载
        if reset {
            hasInitialLoaded = true
        }

        // 重置加载状态
        loadingState = .idle
    }

    /// 处理网络请求错误
    /// - Parameters:
    ///   - error: 错误对象
    ///   - preserveData: 是否保留现有数据，默认true
    private func handleError(_ error: Error, preserveData: Bool = true) {
        self.error = error as? NetworkError ?? NetworkError.requestFailed(error)

        // 根据情况决定是否保留数据
        if !preserveData {
            works.removeAll()
            currentPage = 1
            hasMore = false
        }

        loadingState = .idle
    }

}