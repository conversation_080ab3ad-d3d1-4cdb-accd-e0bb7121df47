import Foundation

// MARK: - Date Extensions

extension Date {
    // MARK: - Formatting
    
    /// 格式化为字符串
    /// 
    /// - Parameter format: 日期格式，默认为"yyyy-MM-dd"
    /// - Returns: 格式化后的字符串
    func formatted(_ format: String = "yyyy-MM-dd") -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = format
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: self)
    }
    
    /// 相对时间描述（如：刚刚、5分钟前、昨天等）
    var relativeDescription: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateTimeStyle = .named
        return formatter.localizedString(for: self, relativeTo: Date())
    }
    
    /// 友好的时间描述
    var friendlyDescription: String {
        let now = Date()
        let calendar = Calendar.current
        
        // 今天
        if calendar.isDateInToday(self) {
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm"
            return "今天 \(formatter.string(from: self))"
        }
        
        // 昨天
        if calendar.isDateInYesterday(self) {
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm"
            return "昨天 \(formatter.string(from: self))"
        }
        
        // 本周
        if let weekOfYear = calendar.dateInterval(of: .weekOfYear, for: now),
           weekOfYear.contains(self) {
            let formatter = DateFormatter()
            formatter.dateFormat = "EEEE HH:mm"
            formatter.locale = Locale(identifier: "zh_CN")
            return formatter.string(from: self)
        }
        
        // 本年
        if calendar.component(.year, from: self) == calendar.component(.year, from: now) {
            let formatter = DateFormatter()
            formatter.dateFormat = "MM月dd日 HH:mm"
            return formatter.string(from: self)
        }
        
        // 其他年份
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        return formatter.string(from: self)
    }
    
    // MARK: - Date Components
    
    /// 年份
    var year: Int {
        Calendar.current.component(.year, from: self)
    }
    
    /// 月份
    var month: Int {
        Calendar.current.component(.month, from: self)
    }
    
    /// 日期
    var day: Int {
        Calendar.current.component(.day, from: self)
    }
    
    /// 小时
    var hour: Int {
        Calendar.current.component(.hour, from: self)
    }
    
    /// 分钟
    var minute: Int {
        Calendar.current.component(.minute, from: self)
    }
    
    /// 秒
    var second: Int {
        Calendar.current.component(.second, from: self)
    }
    
    /// 星期几（1-7，周日为1）
    var weekday: Int {
        Calendar.current.component(.weekday, from: self)
    }
    
    /// 星期几的中文名称
    var weekdayName: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "EEEE"
        return formatter.string(from: self)
    }
    
    /// 星期几的简短中文名称
    var shortWeekdayName: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "EEE"
        return formatter.string(from: self)
    }
    
    // MARK: - Date Calculations
    
    /// 添加天数
    /// 
    /// - Parameter days: 天数
    /// - Returns: 新的日期
    func addingDays(_ days: Int) -> Date {
        Calendar.current.date(byAdding: .day, value: days, to: self) ?? self
    }
    
    /// 添加月份
    /// 
    /// - Parameter months: 月数
    /// - Returns: 新的日期
    func addingMonths(_ months: Int) -> Date {
        Calendar.current.date(byAdding: .month, value: months, to: self) ?? self
    }
    
    /// 添加年份
    /// 
    /// - Parameter years: 年数
    /// - Returns: 新的日期
    func addingYears(_ years: Int) -> Date {
        Calendar.current.date(byAdding: .year, value: years, to: self) ?? self
    }
    
    /// 添加小时
    /// 
    /// - Parameter hours: 小时数
    /// - Returns: 新的日期
    func addingHours(_ hours: Int) -> Date {
        Calendar.current.date(byAdding: .hour, value: hours, to: self) ?? self
    }
    
    /// 添加分钟
    /// 
    /// - Parameter minutes: 分钟数
    /// - Returns: 新的日期
    func addingMinutes(_ minutes: Int) -> Date {
        Calendar.current.date(byAdding: .minute, value: minutes, to: self) ?? self
    }
    
    /// 获取当天的开始时间（00:00:00）
    var startOfDay: Date {
        Calendar.current.startOfDay(for: self)
    }
    
    /// 获取当天的结束时间（23:59:59）
    var endOfDay: Date {
        let calendar = Calendar.current
        let startOfNextDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? self
        return calendar.date(byAdding: .second, value: -1, to: startOfNextDay) ?? self
    }
    
    /// 获取当月的开始时间
    var startOfMonth: Date {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month], from: self)
        return calendar.date(from: components) ?? self
    }
    
    /// 获取当月的结束时间
    var endOfMonth: Date {
        let calendar = Calendar.current
        let startOfNextMonth = calendar.date(byAdding: .month, value: 1, to: startOfMonth) ?? self
        return calendar.date(byAdding: .second, value: -1, to: startOfNextMonth) ?? self
    }
    
    // MARK: - Date Comparisons
    
    /// 是否是今天
    var isToday: Bool {
        Calendar.current.isDateInToday(self)
    }
    
    /// 是否是昨天
    var isYesterday: Bool {
        Calendar.current.isDateInYesterday(self)
    }
    
    /// 是否是明天
    var isTomorrow: Bool {
        Calendar.current.isDateInTomorrow(self)
    }
    
    /// 是否是本周
    var isThisWeek: Bool {
        Calendar.current.isDate(self, equalTo: Date(), toGranularity: .weekOfYear)
    }
    
    /// 是否是本月
    var isThisMonth: Bool {
        Calendar.current.isDate(self, equalTo: Date(), toGranularity: .month)
    }
    
    /// 是否是本年
    var isThisYear: Bool {
        Calendar.current.isDate(self, equalTo: Date(), toGranularity: .year)
    }
    
    /// 是否是过去的日期
    var isPast: Bool {
        self < Date()
    }
    
    /// 是否是未来的日期
    var isFuture: Bool {
        self > Date()
    }
    
    /// 是否是工作日
    var isWeekday: Bool {
        let weekday = Calendar.current.component(.weekday, from: self)
        return weekday >= 2 && weekday <= 6 // 周一到周五
    }
    
    /// 是否是周末
    var isWeekend: Bool {
        Calendar.current.isDateInWeekend(self)
    }
    
    // MARK: - Time Intervals
    
    /// 距离现在的天数
    var daysFromNow: Int {
        Calendar.current.dateComponents([.day], from: Date(), to: self).day ?? 0
    }
    
    /// 距离现在的小时数
    var hoursFromNow: Int {
        Calendar.current.dateComponents([.hour], from: Date(), to: self).hour ?? 0
    }
    
    /// 距离现在的分钟数
    var minutesFromNow: Int {
        Calendar.current.dateComponents([.minute], from: Date(), to: self).minute ?? 0
    }
    
    /// 距离指定日期的天数
    /// 
    /// - Parameter date: 目标日期
    /// - Returns: 天数差
    func daysFrom(_ date: Date) -> Int {
        Calendar.current.dateComponents([.day], from: date, to: self).day ?? 0
    }
    
    // MARK: - Age Calculation
    
    /// 计算年龄
    var age: Int {
        Calendar.current.dateComponents([.year], from: self, to: Date()).year ?? 0
    }
    
    // MARK: - Zodiac
    
    /// 星座
    var zodiacSign: String {
        let month = self.month
        let day = self.day
        
        switch (month, day) {
        case (3, 21...31), (4, 1...19):
            return "白羊座"
        case (4, 20...30), (5, 1...20):
            return "金牛座"
        case (5, 21...31), (6, 1...20):
            return "双子座"
        case (6, 21...30), (7, 1...22):
            return "巨蟹座"
        case (7, 23...31), (8, 1...22):
            return "狮子座"
        case (8, 23...31), (9, 1...22):
            return "处女座"
        case (9, 23...30), (10, 1...22):
            return "天秤座"
        case (10, 23...31), (11, 1...21):
            return "天蝎座"
        case (11, 22...30), (12, 1...21):
            return "射手座"
        case (12, 22...31), (1, 1...19):
            return "摩羯座"
        case (1, 20...31), (2, 1...18):
            return "水瓶座"
        case (2, 19...29), (3, 1...20):
            return "双鱼座"
        default:
            return "未知"
        }
    }
    
    // MARK: - Static Helpers
    
    /// 从时间戳创建日期
    /// 
    /// - Parameter timestamp: 时间戳（秒）
    /// - Returns: 日期对象
    static func from(timestamp: TimeInterval) -> Date {
        Date(timeIntervalSince1970: timestamp)
    }
    
    /// 从毫秒时间戳创建日期
    /// 
    /// - Parameter milliseconds: 毫秒时间戳
    /// - Returns: 日期对象
    static func from(milliseconds: Int64) -> Date {
        Date(timeIntervalSince1970: TimeInterval(milliseconds) / 1000.0)
    }
    
    /// 创建指定日期
    /// 
    /// - Parameters:
    ///   - year: 年
    ///   - month: 月
    ///   - day: 日
    ///   - hour: 时
    ///   - minute: 分
    ///   - second: 秒
    /// - Returns: 日期对象
    static func create(
        year: Int,
        month: Int,
        day: Int,
        hour: Int = 0,
        minute: Int = 0,
        second: Int = 0
    ) -> Date? {
        var components = DateComponents()
        components.year = year
        components.month = month
        components.day = day
        components.hour = hour
        components.minute = minute
        components.second = second
        
        return Calendar.current.date(from: components)
    }
}
