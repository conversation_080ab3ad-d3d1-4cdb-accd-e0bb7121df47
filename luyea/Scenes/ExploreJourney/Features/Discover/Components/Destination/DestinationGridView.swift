import SwiftUI

struct DestinationGridView: View {
    let destinations: [(name: String, imageName: String)]

    let columns = Array(repeating: GridItem(.flexible(), spacing: 10), count: 3)

    var body: some View {
        LazyVGrid(columns: columns, spacing: 12) {
            ForEach(destinations, id: \.name) { dest in
                VStack(spacing: 6) {
                    // 占位图片，可替换为真实图片
                    ZStack {
                        RoundedRectangle(cornerRadius: 14)
                            .fill(Color(.systemGray6))
                            .frame(height: 74)
                        Text("图")
                            .foregroundColor(.white)
                            .font(.system(size: 18, weight: .bold))
                    }
                    Text(dest.name)
                        .font(.system(size: 13, weight: .semibold))
                        .foregroundColor(Color(.label))
                        .lineLimit(1)
                        .padding(.top, 2)
                }
                .padding(8)
                .background(Color(.systemBackground))
                .cornerRadius(14)
                .shadow(color: Color.primary.opacity(0.04), radius: 4, x: 0, y: 2)
            }
        }
    }
} 