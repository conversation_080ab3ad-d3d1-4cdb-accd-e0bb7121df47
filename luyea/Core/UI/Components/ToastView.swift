import SwiftUI

struct ToastView: View {
    let message: String

    // 清理消息，移除emoji前缀
    private var cleanMessage: String {
        if message.hasPrefix("✅") {
            return String(message.dropFirst(2).trimmingCharacters(in: .whitespaces))
        } else if message.hasPrefix("❌") {
            return String(message.dropFirst(2).trimmingCharacters(in: .whitespaces))
        } else if message.hasPrefix("⚠️") {
            return String(message.dropFirst(2).trimmingCharacters(in: .whitespaces))
        } else if message.hasPrefix("ℹ️") {
            return String(message.dropFirst(2).trimmingCharacters(in: .whitespaces))
        } else {
            return message
        }
    }

    var body: some View {
        Text(cleanMessage)
            .font(.system(size: 15))
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(Color.black.opacity(0.85))
            .cornerRadius(12)
            .padding(.bottom, 40)
    }
}