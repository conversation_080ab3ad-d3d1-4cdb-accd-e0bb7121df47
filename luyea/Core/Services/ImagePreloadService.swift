import SwiftUI
import Foundation

/// 图片预加载服务
///
/// 提供智能的图片预加载功能，在用户滚动时预加载即将出现的图片，
/// 提升用户体验和滚动流畅度。
///
/// 使用 actor 确保线程安全，避免在异步上下文中使用 NSLock
actor ImagePreloadService: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = ImagePreloadService()
    
    // MARK: - Private Properties
    
    /// 图片缓存服务
    private let imageCache = ImageCacheService.shared
    
    /// 预加载队列
    private let preloadQueue = DispatchQueue(label: "com.luyea.imagepreload", qos: .utility)
    
    /// 正在预加载的URL集合（actor 自动保证线程安全）
    private var preloadingURLs: Set<URL> = []

    /// 预加载任务字典（actor 自动保证线程安全）
    private var preloadTasks: [URL: Task<Void, Never>] = [:]
    
    /// 最大预加载数量
    private let maxPreloadCount = 10
    
    // MARK: - Initialization
    
    private init() {
        Log.debug("🚀 [ImagePreload] 图片预加载服务初始化完成")
    }
    
    // MARK: - Public Methods
    
    /// 预加载图片列表（高性能优化版本 - 不阻塞UI）
    /// - Parameter urls: 要预加载的图片URL列表
    /// - Parameter priority: 预加载优先级，默认为 .background（最低优先级）
    func preloadImages(urls: [String], priority: TaskPriority = .background) async {
        let validURLs = urls.compactMap { URL(string: $0) }

        // 限制预加载数量，避免过度预加载
        let urlsToPreload = Array(validURLs.prefix(maxPreloadCount))

        // 在后台执行预加载，使用 Task.detached 避免阻塞调用者
        Task.detached(priority: priority) { [weak self] in
            guard let self = self else { return }

            for url in urlsToPreload {
                // 检查任务是否被取消
                guard !Task.isCancelled else { break }

                // 检查是否已在内存缓存中
                if self.imageCache.getUIImageFromMemory(for: url) == nil {
                    await self.preloadImage(url: url)
                }

                // 让出执行权，确保不阻塞其他任务
                await Task.yield()

                // 添加小延迟，进一步降低对UI的影响
                try? await Task.sleep(nanoseconds: 10_000_000) // 10ms
            }
        }
    }
    
    /// 预加载单个图片（异步版本）
    /// - Parameter url: 图片URL
    private func preloadImage(url: URL) async {
        // actor 自动保证线程安全
        guard !preloadingURLs.contains(url) else { return }

        // 检查是否已在缓存中
        if imageCache.getUIImageFromMemory(for: url) != nil {
            return
        }

        // actor 自动保证线程安全
        preloadingURLs.insert(url)

        await performPreload(url: url)
    }

    /// 预加载单个图片（后台执行版本 - 不阻塞UI）
    /// - Parameter url: 图片URL
    func preloadImage(url: URL) {
        // 使用 Task 来调用 actor 方法
        Task {
            await preloadImageAsync(url: url)
        }
    }

    /// 异步预加载图片的内部方法
    /// - Parameter url: 图片URL
    private func preloadImageAsync(url: URL) async {
        // actor 自动保证线程安全
        guard !preloadingURLs.contains(url) else { return }

        // 检查是否已在缓存中
        if imageCache.getUIImageFromMemory(for: url) != nil {
            return
        }

        // actor 自动保证线程安全
        preloadingURLs.insert(url)

        // 使用detached task在后台执行，不阻塞主线程
        let preloadTask = Task.detached(priority: .background) { [weak self] in
            await self?.performPreload(url: url)
            return ()
        }

        // actor 自动保证线程安全
        preloadTasks[url] = preloadTask
    }
    
    /// 取消预加载任务
    /// - Parameter urls: 要取消的图片URL列表
    func cancelPreload(urls: [String]) async {
        let validURLs = urls.compactMap { URL(string: $0) }
        
        for url in validURLs {
            await cancelPreloadAsync(url: url)
        }
    }
    
    /// 取消单个预加载任务
    /// - Parameter url: 图片URL
    private func cancelPreloadAsync(url: URL) async {
        // actor 自动保证线程安全
        let task = preloadTasks[url]
        preloadTasks.removeValue(forKey: url)
        preloadingURLs.remove(url)

        task?.cancel()
    }
    
    /// 清理所有预加载任务
    func cancelAllPreloads() {
        Task {
            await cancelAllPreloadsAsync()
        }
    }

    /// 异步清理所有预加载任务的内部方法
    private func cancelAllPreloadsAsync() async {
        // actor 自动保证线程安全
        let allTasks = Array(preloadTasks.values)
        preloadTasks.removeAll()
        preloadingURLs.removeAll()

        // 取消所有任务
        for task in allTasks {
            task.cancel()
        }

        Log.debug("🧹 [ImagePreload] 已清理所有预加载任务")
    }
    
    // MARK: - Private Methods
    
    /// 执行预加载（后台执行，性能优化）
    /// - Parameter url: 图片URL
    private func performPreload(url: URL) async {
        defer {
            // 使用 Task 来调用 actor 方法进行清理
            Task {
                await self.cleanupPreloadState(url: url)
            }
        }

        // 检查任务是否被取消
        guard !Task.isCancelled else { return }

        // 预加载开始，不记录日志（减少日志噪音）

        // 添加延迟，确保UI操作优先执行
        try? await Task.sleep(nanoseconds: 50_000_000) // 50ms延迟

        // 再次检查任务是否被取消
        guard !Task.isCancelled else { return }

        // 使用图片缓存服务进行预加载
        let image = await imageCache.getImage(for: url)

        // 预加载完成，不记录日志（减少日志噪音）
        // 只在失败时记录警告
        if image == nil {
            Log.warning("⚠️ [ImagePreload] 预加载失败: \(url.lastPathComponent)")
        }
    }

    /// 清理预加载状态的辅助方法
    /// - Parameter url: 图片URL
    private func cleanupPreloadState(url: URL) async {
        preloadingURLs.remove(url)
        preloadTasks.removeValue(forKey: url)
    }
}

// MARK: - SwiftUI Integration

extension View {
    /// 为视图添加图片预加载功能
    /// - Parameter urls: 要预加载的图片URL列表
    /// - Returns: 修饰后的视图
    func preloadImages(_ urls: [String]) -> some View {
        self.onAppear {
            Task {
                await ImagePreloadService.shared.preloadImages(urls: urls)
            }
        }
        .onDisappear {
            Task {
                await ImagePreloadService.shared.cancelPreload(urls: urls)
            }
        }
    }
}
