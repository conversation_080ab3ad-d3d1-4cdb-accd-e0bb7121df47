import SwiftUI

/// 用户主页中的发现卡片视图
struct UserDiscoverCardView: View {
    let item: ProfileModels.UserDiscoverItem
    
    var body: some View {
        VStack(alignment: .leading, spacing: ProfileCenterConstants.cardSpacing) {
            coverImageView
            contentView
        }
        .background(Color(.systemBackground))
        .cornerRadius(ProfileCenterConstants.cardCornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 子视图
    @ViewBuilder
    private var coverImageView: some View {
        if let coverImage = item.coverImage {
            CachedAsyncImage(
                url: URL(string: coverImage)
            ) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.1))
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title2)
                            .foregroundColor(.gray.opacity(0.6))
                    )
            } errorView: {
                // 使用自定义蓝色主题的错误视图
                ImageErrorView(
                    style: .custom(
                        backgroundColor: Color.blue.opacity(0.06),
                        iconColor: Color.blue.opacity(0.5)
                    ),
                    showText: true,
                    errorText: "图片加载失败",
                    iconName: "photo"
                )
            }
            .frame(height: ProfileCenterConstants.cardImageHeight)
            .clipped()
            .cornerRadius(ProfileCenterConstants.cardImageCornerRadius)
        }
    }
    
    private var contentView: some View {
        VStack(alignment: .leading, spacing: ProfileCenterConstants.cardContentSpacing) {
            titleView
            descriptionView
            footerView
        }
        .padding(.horizontal, ProfileCenterConstants.cardContentPadding)
        .padding(.bottom, ProfileCenterConstants.cardContentPadding)
    }
    
    private var titleView: some View {
        Text(item.title)
            .font(.system(size: ProfileCenterConstants.cardTitleFontSize, weight: .semibold))
            .foregroundColor(.primary)
            .lineLimit(2)
    }
    
    @ViewBuilder
    private var descriptionView: some View {
        if !item.description.isEmpty {
            Text(item.description)
                .font(.system(size: ProfileCenterConstants.cardDescriptionFontSize))
                .foregroundColor(.secondary)
                .lineLimit(3)
        }
    }
    
    private var footerView: some View {
        HStack(spacing: ProfileCenterConstants.cardFooterSpacing) {
            if let tag = item.tag {
                ProfileTagView(
                    text: tag,
                    isSelected: false,
                    action: {}
                )
            }
            
            Spacer()
            
            statsView
        }
    }
    
    private var statsView: some View {
        HStack(spacing: ProfileCenterConstants.cardStatsSpacing) {
            statItem(icon: "heart", count: item.likes, isSelected: item.isLiked)
            statItem(icon: "message", count: item.comments)
        }
    }
    
    @ViewBuilder
    private func statItem(icon: String, count: Int, isSelected: Bool = false) -> some View {
        HStack(spacing: ProfileCenterConstants.cardStatItemSpacing) {
            Image(systemName: icon)
                .font(.system(size: ProfileCenterConstants.cardStatIconSize))
                .foregroundColor(isSelected ? .red : .secondary)
            Text("\(count)")
                .font(.system(size: ProfileCenterConstants.cardStatFontSize))
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - 预览
#Preview {
    UserDiscoverCardView(
        item: ProfileModels.UserDiscoverItem(
            id: "1",
            title: "探索城市中的隐秘角落",
            description: "今天发现了一个隐藏在闹市中的安静咖啡馆，环境优雅，咖啡香醇...",
            coverImage: "https://picsum.photos/400/300?random=1",
            tag: "城市探索",
            likes: 128,
            comments: 32,
            createdAt: Date().addingTimeInterval(-3600 * 2),
            location: "上海市",
            isLiked: true,
            isCollected: false
        )
    )
    .padding()
} 