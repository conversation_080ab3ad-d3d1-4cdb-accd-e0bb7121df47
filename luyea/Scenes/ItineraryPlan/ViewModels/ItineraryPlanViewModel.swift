import Foundation
import SwiftUI
import Combine

/// 行程计划页面视图模型
///
/// 负责管理行程计划页面的所有业务逻辑，包括数据加载、状态管理、用户交互等。
/// 采用MVVM架构模式，确保视图与业务逻辑的分离。
@MainActor
final class ItineraryPlanViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 当前所有行程的列表
    @Published var itineraries: [ItineraryModel] = []

    /// 页面是否处于加载中状态
    @Published var isLoading: Bool = false

    /// 是否正在刷新数据
    @Published var isRefreshing: Bool = false

    /// 当前所有足迹的列表
    @Published var footprints: [FootprintModel] = []

    /// 加载过程中发生的错误
    @Published var error: ItineraryError?

    /// 是否显示错误提示
    @Published var showErrorAlert: Bool = false

    /// 搜索关键词
    @Published var searchText: String = ""

    /// 当前选择的筛选条件
    @Published var selectedFilter: ItineraryFilter = .all

    // MARK: - Private Properties

    /// 行程计划服务
    private let itineraryPlanService: ItineraryPlanServiceProtocol

    /// 取消令牌集合
    private var cancellables = Set<AnyCancellable>()

    /// 数据加载任务
    private var loadingTask: Task<Void, Never>?

    /// 搜索防抖器
    private let searchDebouncer = Debouncer(delay: DesignSystemConstants.Interaction.debounceDelay)

    // MARK: - Computed Properties

    /// 经过排序和筛选的行程列表
    var filteredItineraries: [ItineraryModel] {
        var filtered = itineraries

        // 使用Service进行搜索筛选
        filtered = itineraryPlanService.filterItineraries(filtered, by: searchText)

        // 使用Service进行状态筛选
        filtered = itineraryPlanService.filterItineraries(filtered, by: selectedFilter)

        // 使用Service进行排序
        return itineraryPlanService.sortItineraries(filtered)
    }

    /// 经过排序的行程列表（兼容性保持）
    var sortedItineraries: [ItineraryModel] {
        filteredItineraries
    }

    /// 是否有数据
    var hasData: Bool {
        !itineraries.isEmpty || !footprints.isEmpty
    }

    /// 是否显示空状态
    var showEmptyState: Bool {
        !isLoading && !hasData && error == nil
    }

    // MARK: - Initialization

    /// 初始化方法
    ///
    /// - Parameter itineraryPlanService: 行程计划服务，默认使用默认实例
    init(itineraryPlanService: ItineraryPlanServiceProtocol = ItineraryPlanService()) {
        self.itineraryPlanService = itineraryPlanService

        setupObservers()
        setupSearchDebounce()

        // 监听认证状态变化，只有在已登录时才加载数据
        setupAuthenticationObserver()
    }

    deinit {
        // 清理资源
        loadingTask?.cancel()
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Public Methods

    /// 刷新所有数据（行程和足迹）
    ///
    /// 并行加载数据以提升性能，包含完善的错误处理和状态管理。
    func refreshData() async {
        // 防止重复刷新
        guard !isLoading && !isRefreshing else {
            return
        }

        isRefreshing = true
        error = nil

        // 如果是首次加载，显示加载状态
        if itineraries.isEmpty && footprints.isEmpty {
            isLoading = true
        }

        // 取消之前的加载任务
        loadingTask?.cancel()

        // 创建新的加载任务
        loadingTask = Task {
            do {
                // 使用Service并行加载数据
                let result = try await itineraryPlanService.refreshAllData()

                await MainActor.run {
                    self.itineraries = result.itineraries
                    self.footprints = result.footprints
                }

            } catch {
                if !Task.isCancelled {
                    await handleError(error)
                }
            }

            // 更新UI状态
            isLoading = false
            isRefreshing = false
        }

        await loadingTask?.value
    }

    /// 手动刷新数据（用于下拉刷新）
    func pullToRefresh() async {
        await refreshData()
    }

    /// 搜索行程
    ///
    /// - Parameter text: 搜索关键词
    func searchItineraries(with text: String) {
        searchText = text

        // 使用新的通用防抖工具
        searchDebouncer.call {
            Task { @MainActor in
                // 这里可以添加远程搜索逻辑
                // 实现行程搜索功能
                self.performSearch(text)
            }
        }
    }

    /// 应用筛选条件
    ///
    /// - Parameter filter: 筛选条件
    func applyFilter(_ filter: ItineraryFilter) {
        selectedFilter = filter
    }

    /// 清除错误状态
    func clearError() {
        error = nil
        showErrorAlert = false
    }

    /// 重试加载数据
    func retryLoading() {
        clearError()
        Task {
            await refreshData()
        }
    }

    // MARK: - Private Methods

    /// 设置观察者
    private func setupObservers() {
        // 监听交通状况反馈通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleTrafficFeedback(_:)),
            name: .trafficFeedbackSubmitted,
            object: nil
        )

        // 监听应用进入前台通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    /// 设置搜索防抖
    private func setupSearchDebounce() {
        // 使用新的通用防抖工具，在 searchText 的 didSet 中处理
        // 或者通过 searchItineraries 方法调用
    }

    /// 执行搜索
    private func performSearch(_ text: String) {
        // 这里可以添加远程搜索API调用
    }



    /// 处理错误
    private func handleError(_ error: Error) async {
        let itineraryError: ItineraryError

        if let networkError = error as? NetworkError {
            switch networkError {
            case .noInternetConnection:
                itineraryError = .networkUnavailable
            case .timeout:
                itineraryError = .timeout
            case .serverError:
                itineraryError = .serverError
            default:
                itineraryError = .loadFailed(error)
            }
        } else {
            itineraryError = .loadFailed(error)
        }

        await MainActor.run {
            self.error = itineraryError
            self.showErrorAlert = true
        }
    }

    /// 处理交通状况反馈的通知
    @objc private func handleTrafficFeedback(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let id = userInfo["id"] as? String,
              let level = userInfo["level"] as? String else {
            return
        }

        if let index = itineraries.firstIndex(where: { $0.id == id }) {
            itineraries[index].trafficFeedback = level
        }
    }

    /// 处理应用进入前台
    @objc private func handleAppDidBecomeActive() {
        // 应用进入前台时，检查是否需要刷新数据
        Task {
            // 如果数据超过配置时间没有更新，则自动刷新
            let shouldRefresh = itineraries.isEmpty ||
                               Date().timeIntervalSince(lastRefreshTime) > ItineraryPlanConstants.Config.cacheRefreshInterval

            if shouldRefresh {
                await refreshData()
            }
        }
    }



    private var lastRefreshTime: Date = Date()

    private func setupAuthenticationObserver() {
        NotificationCenter.default.publisher(for: Notification.Name("luyea.auth.user_did_login"))
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.refreshData()
                }
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: Notification.Name("luyea.auth.user_did_logout"))
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.clearData()
                }
            }
            .store(in: &cancellables)

        if AuthenticationManager.shared.isAuthenticated {
            Task {
                await refreshData()
            }
        }
    }

    private func clearData() {
        itineraries.removeAll()
        footprints.removeAll()
        error = nil
        isLoading = false
        isRefreshing = false
    }


}

// MARK: - 支持类型定义

/// 行程筛选条件
enum ItineraryFilter: String, CaseIterable {
    case all = "all"
    case inProgress = "inProgress"
    case upcoming = "upcoming"
    case completed = "completed"

    var displayName: String {
        switch self {
        case .all:
            return "全部"
        case .inProgress:
            return "在途中"
        case .upcoming:
            return "待出行"
        case .completed:
            return "已结束"
        }
    }
}

/// 行程相关错误类型
enum ItineraryError: Error, LocalizedError {
    case loadFailed(Error)
    case networkUnavailable
    case timeout
    case serverError
    case dataCorrupted
    case unauthorized

    var errorDescription: String? {
        switch self {
        case .loadFailed(let error):
            return "数据加载失败: \(error.localizedDescription)"
        case .networkUnavailable:
            return "网络连接不可用，请检查网络设置"
        case .timeout:
            return "请求超时，请稍后重试"
        case .serverError:
            return "服务器暂时不可用，请稍后重试"
        case .dataCorrupted:
            return "数据格式错误，请联系客服"
        case .unauthorized:
            return "登录已过期，请重新登录"
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case .networkUnavailable:
            return "请检查网络连接后重试"
        case .timeout, .serverError:
            return "请稍后重试"
        case .dataCorrupted, .unauthorized:
            return "请联系客服或重新登录"
        case .loadFailed:
            return "请重试或联系客服"
        }
    }
}

// MARK: - 通知名称扩展

extension Notification.Name {
    /// 交通状况反馈提交通知
    static let trafficFeedbackSubmitted = Notification.Name("trafficFeedbackSubmitted")
}
 
