import SwiftUI

/// 缓存异步图片组件
///
/// 使用 ImageCacheService 提供高性能的图片缓存功能，支持内存和磁盘缓存。
/// 提供加载状态、错误状态和占位符的完整支持。
struct CachedAsyncImage<Content: View, Placeholder: View, ErrorView: View>: View {
    
    // MARK: - Properties
    
    /// 图片URL
    let url: URL?
    
    /// 成功加载时的内容构建器
    let content: (Image) -> Content

    /// 占位符构建器（加载中状态）
    let placeholder: () -> Placeholder

    /// 错误视图构建器
    let errorView: () -> ErrorView
    
    // MARK: - State
    
    /// 图片缓存服务
    private let imageCache = ImageCacheService.shared
    
    /// 加载的图片
    @State private var loadedImage: Image?
    
    /// 加载状态
    @State private var isLoading = false
    
    /// 错误状态
    @State private var hasError = false

    /// 当前加载任务
    @State private var loadingTask: Task<Void, Never>?
    
    // MARK: - Initialization
    
    /// 初始化缓存异步图片
    /// - Parameters:
    ///   - url: 图片URL
    ///   - content: 成功加载时的内容构建器
    ///   - placeholder: 占位符构建器（加载中状态）
    ///   - errorView: 错误视图构建器
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder,
        @ViewBuilder errorView: @escaping () -> ErrorView
    ) {
        self.url = url
        self.content = content
        self.placeholder = placeholder
        self.errorView = errorView
    }
    
    // MARK: - Body
    
    var body: some View {
        Group {
            if let image = loadedImage {
                // 优先显示已有图片，即使有错误状态
                content(image)
            } else if hasError {
                errorView()
            } else {
                placeholder()
            }
        }
        .task(id: url) {
            // 取消之前的加载任务
            loadingTask?.cancel()

            // 创建完全独立的后台加载任务，不阻塞UI
            loadingTask = Task.detached(priority: .utility) {
                await self.loadImageInBackground()
            }

            // 不等待任务完成，让加载在后台进行，完全无感知
        }
        .onDisappear {
            // 视图消失时静默取消加载任务，避免不必要的网络请求和内存占用
            loadingTask?.cancel()
            loadingTask = nil
        }
        .onAppear {
            // 视图出现时进行预检查，如果有缓存立即显示
            if let url = url, loadedImage == nil {
                if let cachedImage = imageCache.getSwiftUIImageFromMemoryOnly(for: url) {
                    loadedImage = cachedImage
                    hasError = false
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    /// 完全后台的图片加载 - 无感知版本，不阻塞UI
    private func loadImageInBackground() async {
        guard let url = url else {
            // URL为空时，静默处理，不影响UI
            await MainActor.run {
                if self.loadedImage == nil {
                    self.hasError = true
                    self.isLoading = false
                }
            }
            return
        }

        // 检查任务是否被取消
        guard !Task.isCancelled else { return }

        // 智能状态管理：只有在没有图片时才显示loading状态
        let shouldShowLoading = await MainActor.run { self.loadedImage == nil }

        if shouldShowLoading {
            await MainActor.run {
                self.isLoading = true
                self.hasError = false
            }
        }

        // 再次检查任务是否被取消
        guard !Task.isCancelled else {
            await MainActor.run {
                self.isLoading = false
            }
            return
        }

        // 完全后台处理，不阻塞主线程
        await withTaskCancellationHandler {
            // 1. 优先检查内存缓存，立即返回
            if let cachedImage = imageCache.getSwiftUIImageFromMemoryOnly(for: url) {
                await MainActor.run {
                    self.updateImageSilently(image: cachedImage, isLoading: false, hasError: false)
                }
                return
            }

            // 2. 检查任务是否被取消
            guard !Task.isCancelled else { return }

            // 3. 后台异步获取图片（磁盘缓存 -> 网络下载）
            let swiftUIImage = await withTimeout(seconds: 15) {
                await self.imageCache.getSwiftUIImage(for: url)
            }

            // 4. 检查任务是否被取消
            guard !Task.isCancelled else { return }

            // 5. 静默更新UI，不干扰用户交互
            await MainActor.run {
                self.updateImageSilently(
                    image: swiftUIImage,
                    isLoading: false,
                    hasError: swiftUIImage == nil
                )
            }
        } onCancel: {
            // 任务被取消时的静默清理，不影响UI
            Task { @MainActor in
                // 只有在没有图片时才清除loading状态
                if self.loadedImage == nil {
                    self.isLoading = false
                }
            }
        }
    }

    /// 统一的状态更新方法 - 确保状态更新的一致性
    @MainActor
    private func updateImageState(image: Image?, isLoading: Bool, hasError: Bool) {
        guard !Task.isCancelled else { return }

        // 更新图片状态
        if let image = image {
            // 有新图片时，只有当前没有图片或者有错误时才更新
            if self.loadedImage == nil || self.hasError {
                self.loadedImage = image
            }
        } else if hasError && self.loadedImage == nil {
            // 错误状态且没有已有图片时，清空图片
            self.loadedImage = nil
        }
        // 如果有已有图片且不是错误状态，保留已有图片

        self.isLoading = isLoading
        self.hasError = hasError
    }

    /// 静默更新图片状态 - 完全无感知的更新，不影响UI交互
    @MainActor
    private func updateImageSilently(image: Image?, isLoading: Bool, hasError: Bool) {
        guard !Task.isCancelled else { return }

        // 静默更新策略：最小化UI变化
        if let image = image {
            // 总是更新到最新图片
            self.loadedImage = image
            self.hasError = false
        } else if hasError {
            // 加载失败时，只有没有图片时才设置错误状态
            if self.loadedImage == nil {
                self.hasError = true
            }
            // 如果已有图片，保持当前状态，不显示错误
        }

        // 只有在没有图片时才显示loading状态
        if self.loadedImage == nil {
            self.isLoading = isLoading
        } else {
            // 有图片时，静默加载，不显示loading状态
            self.isLoading = false
        }
    }

    /// 带超时控制的异步操作 - 改进版本，支持取消传播
    private func withTimeout<T>(
        seconds: TimeInterval,
        operation: @escaping () async -> T?
    ) async -> T? {
        return await withTaskGroup(of: T?.self) { group in
            // 添加主要操作任务
            group.addTask {
                await operation()
            }

            // 添加超时任务
            group.addTask {
                do {
                    try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                    return nil
                } catch {
                    // 任务被取消时返回nil
                    return nil
                }
            }

            // 等待第一个完成的任务
            defer { group.cancelAll() }
            return await group.next() ?? nil
        }
    }
}

// MARK: - Default Views

/// 默认占位符视图（加载中状态）
struct DefaultPlaceholderView: View {
    var body: some View {
        ZStack {
            Color.gray.opacity(0.08)
            ProgressView()
                .scaleEffect(0.8)
                .tint(.gray)
        }
    }
}

/// 默认错误视图 - 使用统一的 ImageErrorView 组件
struct DefaultErrorView: View {
    var body: some View {
        ImageErrorView(
            style: .minimal,
            showText: true,
            errorText: "图片加载失败",
            iconName: "photo"
        )
    }
}

// MARK: - Convenience Initializers

extension CachedAsyncImage where ErrorView == DefaultErrorView {
    /// 便捷初始化方法（使用默认错误视图）
    /// - Parameters:
    ///   - url: 图片URL
    ///   - content: 成功加载时的内容构建器
    ///   - placeholder: 占位符构建器
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.init(
            url: url,
            content: content,
            placeholder: placeholder,
            errorView: { DefaultErrorView() }
        )
    }
}

extension CachedAsyncImage where Placeholder == DefaultPlaceholderView {
    /// 便捷初始化方法（使用默认占位符）
    /// - Parameters:
    ///   - url: 图片URL
    ///   - content: 成功加载时的内容构建器
    ///   - errorView: 错误视图构建器
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder errorView: @escaping () -> ErrorView
    ) {
        self.init(
            url: url,
            content: content,
            placeholder: { DefaultPlaceholderView() },
            errorView: errorView
        )
    }
}

// MARK: - 便利初始化方法

extension CachedAsyncImage where Placeholder == DefaultPlaceholderView, ErrorView == DefaultErrorView {
    /// 最便捷的初始化方法（使用所有默认视图）
    /// - Parameters:
    ///   - url: 图片URL
    ///   - content: 成功加载时的内容构建器
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content
    ) {
        self.init(
            url: url,
            content: content,
            placeholder: { DefaultPlaceholderView() },
            errorView: { DefaultErrorView() }
        )
    }
}

// MARK: - Additional Convenience Initializers

extension CachedAsyncImage {
    /// 自定义占位符和错误视图的初始化方法
    /// - Parameters:
    ///   - url: 图片URL
    ///   - content: 成功加载时的内容构建器
    ///   - placeholder: 自定义占位符视图
    ///   - errorView: 自定义错误视图
    static func custom<P: View, E: View>(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> P,
        @ViewBuilder errorView: @escaping () -> E
    ) -> CachedAsyncImage<Content, P, E> {
        return CachedAsyncImage<Content, P, E>(
            url: url,
            content: content,
            placeholder: placeholder,
            errorView: errorView
        )
    }

    /// 只自定义占位符的初始化方法（使用默认错误视图）
    /// - Parameters:
    ///   - url: 图片URL
    ///   - content: 成功加载时的内容构建器
    ///   - placeholder: 自定义占位符视图
    static func withPlaceholder<P: View>(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> P
    ) -> CachedAsyncImage<Content, P, DefaultErrorView> {
        return CachedAsyncImage<Content, P, DefaultErrorView>(
            url: url,
            content: content,
            placeholder: placeholder,
            errorView: { DefaultErrorView() }
        )
    }

    /// 只自定义错误视图的初始化方法（使用默认占位符）
    /// - Parameters:
    ///   - url: 图片URL
    ///   - content: 成功加载时的内容构建器
    ///   - errorView: 自定义错误视图
    static func withErrorView<E: View>(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder errorView: @escaping () -> E
    ) -> CachedAsyncImage<Content, DefaultPlaceholderView, E> {
        return CachedAsyncImage<Content, DefaultPlaceholderView, E>(
            url: url,
            content: content,
            placeholder: { DefaultPlaceholderView() },
            errorView: errorView
        )
    }
}



// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // 基本用法
        CachedAsyncImage(
            url: URL(string: "https://images.unsplash.com/photo-1506744038136-46273834b3fb")
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Rectangle()
                .fill(Color.blue.opacity(0.1))
                .overlay(
                    ProgressView()
                        .tint(.blue)
                )
        }
        .frame(width: 200, height: 150)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        
        // 便利方法用法
        CachedAsyncImage(
            url: URL(string: "https://images.unsplash.com/photo-1502602898657-3e91760cbb34")
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Rectangle()
                .fill(Color.gray.opacity(0.1))
                .overlay(
                    ProgressView()
                        .tint(.gray)
                )
        }
        .frame(width: 200, height: 150)
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    .padding()
}
