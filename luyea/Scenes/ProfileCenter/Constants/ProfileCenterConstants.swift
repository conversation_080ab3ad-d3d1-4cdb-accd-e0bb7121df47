import SwiftUI

/// 个人中心模块常量定义
enum ProfileCenterConstants {
    // MARK: - 头像相关
    /// 头像尺寸
    static let avatarSize: CGFloat = 80
    /// 头像边框宽度
    static let avatarBorderWidth: CGFloat = 1
    /// 头像编辑按钮图标尺寸
    static let avatarEditIconSize: CGFloat = 24
    
    // MARK: - 按钮相关
    /// 按钮宽度
    static let buttonWidth: CGFloat = 80
    /// 按钮高度
    static let buttonHeight: CGFloat = 32
    /// 按钮字体大小
    static let buttonFontSize: CGFloat = 16
    /// 按钮圆角半径
    static let buttonCornerRadius: CGFloat = 16
    
    // MARK: - 文本相关
    /// 用户名字体大小
    static let usernameFontSize: CGFloat = 22
    /// 标题字体大小
    static let titleFontSize: CGFloat = 20
    /// 副标题字体大小
    static let subtitleFontSize: CGFloat = 15
    /// 描述字体大小
    static let descriptionFontSize: CGFloat = 14
    
    // MARK: - 间距相关
    ///
    /// 注意：基础的间距常量已迁移到 DesignSystemConstants.Spacing
    /// 这里保留模块专用的间距参数（如果有的话）

    // 当前没有模块专用的间距参数
    // 所有间距常量都已迁移到 DesignSystemConstants.Spacing
    
    // MARK: - 标签相关
    /// 标签高度
    static let tagHeight: CGFloat = 28
    /// 标签间距
    static let tagSpacing: CGFloat = 12
    /// 标签字体大小
    static let tagFontSize: CGFloat = 13
    /// 每行标签数量
    static let tagsPerRow: Int = 4
    /// 标签最大显示行数
    static let maxRows: Int = 2
    
    // MARK: - 发现卡片相关
    /// 卡片间距
    static let cardSpacing: CGFloat = 12
    /// 卡片圆角半径
    static let cardCornerRadius: CGFloat = 16
    /// 卡片图片高度
    static let cardImageHeight: CGFloat = 200
    /// 卡片图片圆角半径
    static let cardImageCornerRadius: CGFloat = 12
    /// 卡片内容区间距
    static let cardContentSpacing: CGFloat = 8
    /// 卡片内容区内边距
    static let cardContentPadding: CGFloat = 12
    /// 卡片标题字体大小
    static let cardTitleFontSize: CGFloat = 17
    /// 卡片描述字体大小
    static let cardDescriptionFontSize: CGFloat = 15
    /// 卡片底部区域间距
    static let cardFooterSpacing: CGFloat = 12
    /// 卡片标签字体大小
    static let cardTagFontSize: CGFloat = 12
    /// 卡片标签内边距
    static let cardTagPadding: CGFloat = 8
    /// 卡片标签圆角半径
    static let cardTagCornerRadius: CGFloat = 4
    /// 卡片统计信息区域间距
    static let cardStatsSpacing: CGFloat = 16
    /// 卡片统计信息单项间距
    static let cardStatItemSpacing: CGFloat = 4
    /// 卡片统计信息图标大小
    static let cardStatIconSize: CGFloat = 12
    /// 卡片统计信息字体大小
    static let cardStatFontSize: CGFloat = 12
} 