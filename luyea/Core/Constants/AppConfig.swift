import Foundation

/// App 全局配置中心
///
/// 统一管理应用的全局配置参数，包括功能开关、缓存策略、网络配置等。
/// 便于维护和团队协作，支持运行时动态调整。
enum AppConfig {
    // MARK: - 环境配置

    /// 应用环境类型枚举（实际切换逻辑在 EnvironmentConfig 中）
    enum Environment: String, CaseIterable {
        case development = "development"
        case staging = "staging"
        case production = "production"

        /// 环境显示名称
        var displayName: String {
            switch self {
            case .development: return "开发环境"
            case .staging: return "测试环境"
            case .production: return "生产环境"
            }
        }
    }

    // MARK: - 功能开关（Feature Flags）

    /// 功能开关配置，用于控制特性的启用/禁用
    enum FeatureFlags {
        /// 是否启用定位服务
        static let enableLocation = true
        /// 是否启用推送通知
        static let enablePushNotification = true
        /// 是否启用数据分析
        static let enableAnalytics = true
        /// 是否启用新首页功能
        static let enableNewHomePage = false
        /// 是否启用调试模式
        static let enableDebugMode = false
        /// 是否启用应用监控
        static let enableAppMonitoring = true
    }

    // MARK: - 缓存配置

    /// 缓存相关配置参数
    enum Cache {
        /// 缓存过期时间（秒）
        static let expirationTime: TimeInterval = 3600
        /// 最大缓存大小（MB）
        static let maxSize: Int = 100
        /// 图片缓存最大数量
        static let maxImageCount: Int = 200
        /// 磁盘缓存清理阈值（MB）
        static let cleanupThreshold: Int = 80

        /// 推荐内容缓存时间（秒）
        static let recommendationsCacheDuration: TimeInterval = 300 // 5分钟

        /// 搜索结果缓存时间（秒）
        static let searchResultsCacheDuration: TimeInterval = 180 // 3分钟

        /// 用户偏好缓存时间（秒）
        static let userPreferencesCacheDuration: TimeInterval = 600 // 10分钟
    }

    // MARK: - 网络配置

    /// 网络请求相关配置（与 NetworkConstants 配合使用）
    enum Network {
        /// 网络请求最大重试次数
        static let maxRetries = 3
        /// 网络请求重试间隔（秒）
        static let retryInterval: TimeInterval = 2
        /// 请求超时时间（秒）
        static let requestTimeout: TimeInterval = 30
        /// 下载超时时间（秒）
        static let downloadTimeout: TimeInterval = 60
        /// 默认 Content-Type
        static let defaultContentType = "application/json"
        /// 默认 Accept
        static let defaultAccept = "application/json"
    }

    // MARK: - 分页配置

    /// 分页相关配置参数
    enum Pagination {
        /// 默认页面大小
        static let defaultPageSize = 20
        /// 最大页面大小
        static let maxPageSize = 100
        /// 预加载阈值（距离底部多少项时开始加载）
        static let preloadThreshold = 5
        /// 搜索建议最大数量
        static let maxSuggestions = 10
        /// 搜索历史最大数量
        static let maxSearchHistory = 20
    }

    // MARK: - UI 配置

    /// UI 相关配置参数
    ///
    /// 注意：基础的设计系统常量已迁移到 DesignSystemConstants
    /// 这里只保留真正的应用级别配置参数
    enum UI {
        // MARK: - 应用级别配置

        // 注意：以下配置已移除，请根据需要添加应用特定的UI配置：
        // - 最大内容宽度: 用于iPad适配，限制内容宽度提供更好阅读体验
        // - 分隔线透明度: 使用系统默认分隔线样式
        // - 禁用状态透明度: 使用系统默认 0.6 或直接硬编码
        // - 加载状态透明度: 根据具体场景使用合适的透明度值

        // 当前没有应用级别的UI配置，所有UI常量都已迁移到 DesignSystemConstants
    }
}