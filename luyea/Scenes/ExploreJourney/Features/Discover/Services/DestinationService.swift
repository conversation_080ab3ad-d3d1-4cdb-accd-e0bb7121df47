import Foundation
import Combine

/// 目的地服务协议
protocol DestinationServiceProtocol {
    /// 获取所有目的地
    func fetchAllDestinations(forceRefresh: Bool) async throws -> [Destination]
    
    /// 搜索目的地
    func searchDestinations(query: String, in destinations: [Destination]) -> [Destination]
    
    /// 按省份分组目的地
    func groupDestinationsByProvince(_ destinations: [Destination]) -> [String: [Destination]]
    

    
    /// 验证目的地缓存是否有效
    var isDestinationsCacheValid: Bool { get }
}

/// 目的地服务实现
final class DestinationService: DestinationServiceProtocol {
    
    // MARK: - Dependencies
    
    private let networkService: NetworkServiceProtocol
    
    // MARK: - Cache Properties
    
    /// 目的地缓存
    private var cachedDestinations: [Destination] = []
    
    /// 最后更新时间
    private var lastDestinationsUpdateTime: Date?
    
    /// 缓存有效期（30分钟）
    private let cacheValidDuration: TimeInterval = 1800
    
    /// 省份分组缓存
    private var provinceGroupsCache: [String: [Destination]] = [:]
    
    // MARK: - Computed Properties
    
    /// 目的地缓存是否有效
    var isDestinationsCacheValid: Bool {
        guard let lastUpdate = lastDestinationsUpdateTime else { return false }
        return Date().timeIntervalSince(lastUpdate) < cacheValidDuration
    }
    
    // MARK: - Initialization
    
    init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }
    
    // MARK: - Public Methods
    
    /// 获取所有目的地
    func fetchAllDestinations(forceRefresh: Bool = false) async throws -> [Destination] {
        // 如果不强制刷新且缓存有效，返回缓存数据
        if !forceRefresh && isDestinationsCacheValid && !cachedDestinations.isEmpty {
            Log.debug("🗺️ [DestinationService] 使用缓存的目的地数据: \(cachedDestinations.count) 个")
            return cachedDestinations
        }
        
        Log.info("🗺️ [DestinationService] 开始加载目的地数据")
        
        do {
            let destinations: [Destination] = try await networkService.request(
                .get(APIPaths.allDestinations)
            )
            
            // 更新缓存
            cachedDestinations = destinations
            lastDestinationsUpdateTime = Date()
            
            // 清除省份分组缓存，下次使用时重新计算
            provinceGroupsCache.removeAll()
            
            Log.success("✅ [DestinationService] 目的地数据加载成功: \(destinations.count) 个")
            return destinations
            
        } catch {
            // 如果网络请求失败且有缓存数据，返回缓存数据
            if !cachedDestinations.isEmpty {
                Log.warning("⚠️ [DestinationService] 网络请求失败，使用缓存数据: \(error.localizedDescription)")
                return cachedDestinations
            }
            
            // 处理具体的网络错误
            let destinationError = mapNetworkError(error)
            Log.error("❌ [DestinationService] 目的地数据加载失败: \(destinationError.localizedDescription)")
            throw destinationError
        }
    }
    
    /// 搜索目的地
    func searchDestinations(query: String, in destinations: [Destination]) -> [Destination] {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 如果搜索词为空，返回所有目的地
        guard !trimmedQuery.isEmpty else {
            return destinations
        }
        
        let lowercasedQuery = trimmedQuery.lowercased()
        
        // 按名称和描述搜索
        let filteredDestinations = destinations.filter { destination in
            destination.name.lowercased().contains(lowercasedQuery) ||
            (destination.description?.lowercased().contains(lowercasedQuery) ?? false)
        }
        
        Log.debug("🔍 [DestinationService] 目的地搜索结果: \(filteredDestinations.count)/\(destinations.count) 个匹配 '\(trimmedQuery)'")
        return filteredDestinations
    }
    
    /// 按省份分组目的地
    func groupDestinationsByProvince(_ destinations: [Destination]) -> [String: [Destination]] {
        // 检查缓存
        if !provinceGroupsCache.isEmpty && destinations.count == cachedDestinations.count {
            return provinceGroupsCache
        }

        // 按类型分组：省份作为键，城市作为值
        let provinces = destinations.filter { $0.type == .province }
        var grouped: [String: [Destination]] = [:]

        for province in provinces {
            let cities = destinations.filter { $0.type == .city && $0.parentId == province.id }
            grouped[province.name] = cities.sorted { $0.name < $1.name }
        }

        // 缓存结果
        provinceGroupsCache = grouped

        Log.debug("🗂️ [DestinationService] 目的地按省份分组: \(grouped.keys.count) 个省份")
        return grouped
    }
    

    
    /// 刷新目的地缓存
    func refreshDestinations() async throws -> [Destination] {
        return try await fetchAllDestinations(forceRefresh: true)
    }
    
    /// 清除目的地缓存
    func clearCache() {
        cachedDestinations.removeAll()
        lastDestinationsUpdateTime = nil
        provinceGroupsCache.removeAll()
        Log.debug("🗑️ [DestinationService] 目的地缓存已清除")
    }
    
    /// 获取缓存的目的地数据（不发起网络请求）
    func getCachedDestinations() -> [Destination] {
        return cachedDestinations
    }
    
    // MARK: - Private Methods
    
    /// 映射网络错误到目的地错误
    private func mapNetworkError(_ error: Error) -> DiscoverError {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .noInternetConnection:
                return .destinationsNetworkUnavailable
            case .timeout:
                return .destinationsTimeout
            case .serverError:
                return .destinationsServerError
            default:
                return .destinationsLoadFailed(error)
            }
        } else {
            return .destinationsLoadFailed(error)
        }
    }
}

// MARK: - Destination Service Extensions
// 扩展功能已移除，保持Service专注于核心功能

// MARK: - Singleton Support (Optional)

extension DestinationService {
    /// 单例实例（可选，根据项目需要）
    static let shared = DestinationService()
}
