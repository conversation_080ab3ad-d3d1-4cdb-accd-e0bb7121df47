import Foundation
import SwiftUI
import Combine

@MainActor
class AuthenticationViewModel: ObservableObject {
    private let authenticationService = AuthenticationService.shared

    @Published var phoneNumber: String = ""
    @Published var verificationCode: String = ""
    @Published var isLoading: Bool = false

    var authState: AuthenticationModels.AuthState {
        authenticationService.authState
    }
    
    var codeSendState: AuthenticationModels.CodeSendState {
        _codeSendState
    }

    var isPhoneNumberValid: Bool {
        if let cached = _cachedPhoneValidation {
            return cached
        }
        let isValid = phoneNumber.isValidPhoneNumber
        _cachedPhoneValidation = isValid
        return isValid
    }

    var isVerificationCodeValid: Bool {
        verificationCode.isValidVerificationCode
    }

    var canSendCode: Bool {
        isPhoneNumberValid && !isLoading && codeSendState.canSend
    }

    var canLogin: Bool {
        isPhoneNumberValid && isVerificationCodeValid && !isLoading
    }
    
    var sendCodeButtonTitle: String {
        switch codeSendState {
        case .idle:
            return AuthenticationConstants.Text.sendCodeButtonTitle
        case .sending:
            return "发送中..."
        case .sent:
            return AuthenticationConstants.Text.resendCodeButtonTitle
        case .failed:
            return AuthenticationConstants.Text.resendCodeButtonTitle
        case .cooldown(let seconds):
            return "\(seconds)s"
        case .canResend:
            return AuthenticationConstants.Text.resendCodeButtonTitle
        }
    }

    @Published private var _codeSendState: AuthenticationModels.CodeSendState = .idle
    private var codeTimerCancellable: AnyCancellable?
    private var failureResetTimerCancellable: AnyCancellable?
    private var cancellables = Set<AnyCancellable>()
    private var _cachedPhoneValidation: Bool?
    
    init() {
        setupBindings()
        setupPhoneNumberObserver()
    }

    deinit {
        codeTimerCancellable?.cancel()
        failureResetTimerCancellable?.cancel()
    }
    func sendVerificationCode() async {
        guard canSendCode else { return }
        
        _codeSendState = .sending
        
        do {
            let response = try await authenticationService.sendVerificationCode(to: phoneNumber)
            
            if response.success {
                let cooldownEnd = Date().addingTimeInterval(AuthenticationConstants.Validation.codeCooldownTime)
                _codeSendState = .sent(expiresAt: cooldownEnd)
                startCodeTimer(expiresAt: cooldownEnd)
                ToastManager.shared.show(AuthenticationConstants.Text.codeSentSuccess, duration: 2.5)
            } else {
                _codeSendState = .failed(.codeSendFailed)
                ToastManager.shared.show(response.message, duration: 3.5)
                startFailureResetTimer()
            }

        } catch {
            _codeSendState = .failed(.codeSendFailed)
            handleError(error)
            startFailureResetTimer()
        }
    }
    
    func loginWithPhoneNumber() async {
        isLoading = true

        do {
            let _ = try await authenticationService.loginWithPhoneNumber(phoneNumber, verificationCode: verificationCode)
            ToastManager.shared.show(AuthenticationConstants.Text.loginSuccess, duration: 2.5)
        } catch {
            handleError(error)
        }

        isLoading = false
    }
    
    func loginWithWeChat() async {
        ToastManager.shared.show("微信登录功能开发中", duration: 2.0)
    }

    func loginWithQQ() async {
        ToastManager.shared.show("QQ登录功能开发中", duration: 2.0)
    }

    func loginWithApple() async {
        ToastManager.shared.show("Apple登录功能开发中", duration: 2.0)
    }
    
    func resetState() {
        phoneNumber = ""
        verificationCode = ""
        isLoading = false
        _codeSendState = .idle
        cancelTimers()
    }

    func clearError() {
        if case .error = authState {
        }
    }
    
    private func setupBindings() {
        authenticationService.$authState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }

    private func setupPhoneNumberObserver() {
        $phoneNumber
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?._cachedPhoneValidation = nil
            }
            .store(in: &cancellables)
    }

    private func handleError(_ error: Error) {
        if let authError = error as? AuthenticationModels.AuthError {
            ToastManager.shared.show(authError.localizedDescription, duration: 3.5)
        } else {
            ToastManager.shared.show(AuthenticationConstants.Text.loginFailedError, duration: 3.5)
        }
    }
    
    private func startCodeTimer(expiresAt: Date) {
        cancelTimers()

        codeTimerCancellable = Timer.publish(every: 1.0, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                guard let self = self else { return }

                let now = Date()
                if now >= expiresAt {
                    self.codeTimerCancellable?.cancel()
                    self.codeTimerCancellable = nil
                    self._codeSendState = .canResend
                } else {
                    let remaining = Int(expiresAt.timeIntervalSince(now))
                    self._codeSendState = .cooldown(remainingSeconds: remaining)
                }
            }
    }

    /// 启动失败重置定时器
    private func startFailureResetTimer() {
        failureResetTimerCancellable?.cancel()

        failureResetTimerCancellable = Timer.publish(every: 3.0, on: .main, in: .common)
            .autoconnect()
            .first() // 只执行一次
            .sink { [weak self] _ in
                guard let self = self else { return }

                if case .failed = self._codeSendState {
                    self._codeSendState = .canResend
                }
                self.failureResetTimerCancellable?.cancel()
                self.failureResetTimerCancellable = nil
            }
    }

    private func cancelTimers() {
        codeTimerCancellable?.cancel()
        codeTimerCancellable = nil
        failureResetTimerCancellable?.cancel()
        failureResetTimerCancellable = nil
    }

}


