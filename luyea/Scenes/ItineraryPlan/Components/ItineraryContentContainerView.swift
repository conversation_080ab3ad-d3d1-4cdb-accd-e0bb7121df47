import SwiftUI

struct ItineraryContentContainer: View {
    @Environment(\.authenticationManager) private var authManager
    @ObservedObject var viewModel: ItineraryPlanViewModel
    @Binding var navigateToItineraryCreation: Bool
    @State private var expandedId: String?
    @State private var showAllItineraries = false

    var body: some View {
        Group {
            if authManager.isAuthenticated {
                authenticatedContent
            } else {
                unauthenticatedContent
            }
        }
        .navigationDestination(isPresented: $showAllItineraries) {
            AllItinerariesView()
        }
    }

    private var authenticatedContent: some View {
        VStack(spacing: 0) {
            ItineraryHeaderView {
                navigateToItineraryCreation = true
            }
            .padding(.bottom, 8)

            TabBarAwareScrollView {
                authenticatedContentSwitcher
                    .frame(maxWidth: .infinity)
            }
            .refreshable {
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
                await viewModel.refreshData()
            }
        }
        .padding(.horizontal)
    }

    private var unauthenticatedContent: some View {
        VStack(spacing: 0) {
            ItineraryHeaderView {
                navigateToItineraryCreation = true
            }
            .padding(.bottom, 8)

            TabBarAwareScrollView {
                UnauthenticatedItineraryView()
                    .frame(maxWidth: .infinity)
                    .padding(.top, 60)
            }
        }
        .padding(.horizontal)
    }

    @ViewBuilder
    private var authenticatedContentSwitcher: some View {
        if viewModel.isLoading {
            ItinerarySkeletonGridView()
                .padding(.top)
        } else if let error = viewModel.error {
            ItineraryErrorView(error: error) {
                Task { await viewModel.refreshData() }
            }
            .padding(.top, 40)
        } else if viewModel.itineraries.isEmpty {
            EmptyItineraryView()
                .padding(.top, 40)
        } else {
            dataDrivenContent
        }
    }

    @ViewBuilder
    private var dataDrivenContent: some View {
        let displayedItineraries = Array(
            viewModel.sortedItineraries.prefix(3) // 最多显示3个行程
        )

        ItineraryListView(
            itineraries: displayedItineraries,
            totalItineraryCount: viewModel.itineraries.count,
            expandedId: $expandedId,
            showAll: $showAllItineraries
        )

        if !viewModel.footprints.isEmpty {
            FootprintsView(footprints: viewModel.footprints)
                .padding(.top)
        }
    }
} 