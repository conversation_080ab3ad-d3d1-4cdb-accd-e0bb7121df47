import SwiftUI

// MARK: - Constants
private enum SelectedTopicsConstants {
    static let tagSpacing: CGFloat = 8
    static let tagHorizontalPadding: CGFloat = 8
    static let tagVerticalPadding: CGFloat = 4
    static let tagCornerRadius: CGFloat = 6
    static let scrollViewHorizontalPadding: CGFloat = 8
    static let deleteButtonSize: CGFloat = 10
    static let animationResponse: Double = 0.3
    static let animationDampingFraction: Double = 0.7
    static let scaleFactor: CGFloat = 0.8
}

// MARK: - SelectedTopicsView
struct SelectedTopicsView: View {
    let topics: [Topic]
    @Binding var selectedTopicIds: Set<String>
    let onTopicRemoved: (String) -> Void
    @State private var scrollProxy: ScrollViewProxy? = nil
    @State private var orderedTopicIds: [String] = []
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            ScrollViewReader { proxy in
                LazyHStack(spacing: SelectedTopicsConstants.tagSpacing) {
                    ForEach(orderedTopicIds, id: \.self) { topicId in
                        if let topic = topics.first(where: { $0.id == topicId }) {
                            TopicTagView(topic: topic, style: .removable {
                                onTopicRemoved(topic.id)
                            })
                            .id(topicId)
                            .transition(.scale.combined(with: .opacity))
                        }
                    }
                }
                .padding(.horizontal, SelectedTopicsConstants.scrollViewHorizontalPadding)
                .padding(.vertical, 8)
                .animation(.spring(
                    response: SelectedTopicsConstants.animationResponse,
                    dampingFraction: SelectedTopicsConstants.animationDampingFraction
                ), value: orderedTopicIds)
                .onAppear {
                    self.scrollProxy = proxy
                }
            }
        }
        .onChange(of: selectedTopicIds) { _, newValue in
            // 更新有序数组：保留现有顺序，添加新的ID到末尾
            let newIds = newValue.filter { !orderedTopicIds.contains($0) }
            orderedTopicIds = orderedTopicIds.filter { newValue.contains($0) } + newIds
            
            // 如果有新添加的标签，滚动到最后一个
            if !newIds.isEmpty {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.spring(
                        response: SelectedTopicsConstants.animationResponse,
                        dampingFraction: SelectedTopicsConstants.animationDampingFraction
                    )) {
                        scrollProxy?.scrollTo(orderedTopicIds.last, anchor: .trailing)
                    }
                }
            }
        }
        .onAppear {
            // 初始化有序数组
            orderedTopicIds = Array(selectedTopicIds)
        }
    }
}

// MARK: - Preview
struct SelectedTopicsView_Previews: PreviewProvider {
    @State static var selectedTopicIds: Set<String> = ["1", "2", "3"]
    
    static var previews: some View {
        SelectedTopicsView(
            topics: [
                Topic(id: "1", name: "自然风光", order: 1),
                Topic(id: "2", name: "美食探店", order: 2),
                Topic(id: "3", name: "城市漫步", order: 3)
            ],
            selectedTopicIds: $selectedTopicIds,
            onTopicRemoved: { _ in }
        )
        .padding()
        .background(Color.gray.opacity(0.1))
    }
} 
 
