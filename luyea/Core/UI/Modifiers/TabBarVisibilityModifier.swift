import SwiftUI

/// TabBar 显示隐藏控制修饰器
///
/// 统一的TabBar显示隐藏控制修饰器，使用TabBarStateManager进行状态管理。
/// 确保所有TabBar状态变化都通过统一的管理器进行，保证状态一致性。
///
/// 核心特性：
/// - 使用统一的TabBarStateManager进行状态管理
/// - 支持动画和非动画两种模式
/// - 自动处理视图生命周期中的状态变化
/// - 确保状态变化不受调用方内容视图影响
///
/// 使用示例：
/// ```swift
/// // 显示 TabBar
/// struct MainView: View {
///     var body: some View {
///         Text("主视图")
///             .showTabBar()  // 明确显示 TabBar
///     }
/// }
///
/// // 隐藏 TabBar
/// struct DetailView: View {
///     var body: some View {
///         Text("详情视图")
///             .hideTabBar()  // 明确隐藏 TabBar
///     }
/// }
///
/// // 条件控制 TabBar
/// struct ConditionalView: View {
///     @State private var shouldShowTabBar = true
///
///     var body: some View {
///         Text("条件视图")
///             .tabBarVisible(shouldShowTabBar)
///     }
/// }
/// ```
struct TabBarVisibilityModifier: ViewModifier {

    // MARK: - Properties

    /// 目标可见性状态
    private let isVisible: Bool

    /// 是否使用动画
    private let animated: Bool

    /// TabBar状态管理器
    @EnvironmentObject private var tabBarState: TabBarStateManager

    // MARK: - Initialization

    /// 初始化TabBar可见性修饰器
    ///
    /// - Parameters:
    ///   - isVisible: 目标可见性状态
    ///   - animated: 是否使用动画，默认为true
    init(isVisible: Bool, animated: Bool = true) {
        self.isVisible = isVisible
        self.animated = animated
    }

    // MARK: - ViewModifier

    func body(content: Content) -> some View {
        content
            .onAppear {
                // 视图出现时设置TabBar状态
                tabBarState.setVisible(isVisible, animated: animated)
            }
            .onDisappear {
                // 视图消失时可以选择恢复TabBar显示状态
                // 这里不自动恢复，让调用方明确控制
            }
    }
}

// MARK: - View Extensions

extension View {

    /// 显示TabBar
    ///
    /// 使视图出现时显示TabBar。使用统一的TabBarStateManager进行状态管理。
    ///
    /// - Parameter animated: 是否使用动画，默认为true
    /// - Returns: 应用了显示TabBar修饰器的视图
    ///
    /// 使用示例：
    /// ```swift
    /// Text("主页内容")
    ///     .showTabBar()
    /// ```
    func showTabBar(animated: Bool = true) -> some View {
        modifier(TabBarVisibilityModifier(isVisible: true, animated: animated))
    }

    /// 隐藏TabBar
    ///
    /// 使视图出现时隐藏TabBar。使用统一的TabBarStateManager进行状态管理。
    ///
    /// - Parameter animated: 是否使用动画，默认为true
    /// - Returns: 应用了隐藏TabBar修饰器的视图
    ///
    /// 使用示例：
    /// ```swift
    /// Text("全屏内容")
    ///     .hideTabBar()
    /// ```
    func hideTabBar(animated: Bool = true) -> some View {
        modifier(TabBarVisibilityModifier(isVisible: false, animated: animated))
    }

    /// 条件控制TabBar可见性
    ///
    /// 根据条件动态控制TabBar的显示隐藏。
    ///
    /// - Parameters:
    ///   - isVisible: 是否可见
    ///   - animated: 是否使用动画，默认为true
    /// - Returns: 应用了TabBar可见性控制修饰器的视图
    ///
    /// 使用示例：
    /// ```swift
    /// Text("条件内容")
    ///     .tabBarVisible(shouldShowTabBar, animated: true)
    /// ```
    func tabBarVisible(_ isVisible: Bool, animated: Bool = true) -> some View {
        modifier(TabBarVisibilityModifier(isVisible: isVisible, animated: animated))
    }
}
