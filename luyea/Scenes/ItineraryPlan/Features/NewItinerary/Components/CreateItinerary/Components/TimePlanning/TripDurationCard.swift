import SwiftUI

/// 行程时间规划卡片
///
/// 功能特性：
/// - 出行天数选择
/// - 返程计划切换
/// - 天数选择器
/// - 自定义天数输入
struct TripDurationCard: View {
    
    // MARK: - Properties
    
    @Binding var tripDays: Int
    @Binding var includeReturn: Bool
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题 - 添加图标保持一致性
            HStack(spacing: 12) {
                Image(systemName: "clock.badge.checkmark")
                    .font(.title3)
                    .foregroundColor(.purple)

                Text("行程时间规划")
                    .font(.headline.weight(.medium))
                    .foregroundColor(.primary)

                Spacer()
            }

            // 出行天数设置
            tripDaysSection

            // 返程计划选项
            returnPlanToggle
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - Private Views

    /// 出行天数设置区域
    private var tripDaysSection: some View {
        HStack(spacing: 16) {
            // 左侧信息区域 - 添加图标保持一致性
            HStack(spacing: 12) {
                // 时间规划图标
                Image(systemName: "calendar.badge.clock")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.orange)
                    .frame(width: 20, height: 20)

                VStack(alignment: .leading, spacing: 4) {
                    Text("出行天数")
                        .font(.subheadline.weight(.medium))
                        .foregroundColor(.primary)

                    Text(tripDurationDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)  // 限制为一行
                }
            }

            Spacer()

            // 右侧天数调节器
            HStack(spacing: 12) {
                // 减少按钮
                Button(action: {
                    if tripDays > 1 {
                        tripDays -= 1
                    }
                }) {
                    Image(systemName: "minus.circle.fill")
                        .font(.title2)
                        .foregroundColor(tripDays > 1 ? .blue : .gray.opacity(0.5))
                }
                .disabled(tripDays <= 1)

                // 天数输入
                TextField("天数", value: $tripDays, format: .number)
                    .font(.title.weight(.bold))
                    .foregroundColor(.blue)
                    .frame(width: 60)  // 恢复原来的宽度
                    .multilineTextAlignment(.center)
                    .keyboardType(.numberPad)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onChange(of: tripDays) { _, newValue in
                        // 限制天数范围
                        if newValue < 1 {
                            tripDays = 1
                        } else if newValue > 366 {
                            tripDays = 366
                        }
                    }

                // 增加按钮
                Button(action: {
                    if tripDays < 366 {
                        tripDays += 1
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(tripDays < 366 ? .blue : .gray.opacity(0.5))
                }
                .disabled(tripDays >= 366)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6).opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }

    /// 返程计划切换
    private var returnPlanToggle: some View {
        HStack(spacing: 12) {
            Image(systemName: includeReturn ? "airplane.arrival" : "airplane")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(includeReturn ? .green : .gray)
                .frame(width: 20, height: 20)

            VStack(alignment: .leading, spacing: 2) {
                Text("包含返程计划")
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.primary)

                Text(includeReturn ? "将为您规划往返行程" : "仅规划单程行程")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Toggle("", isOn: $includeReturn)
                .labelsHidden()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6).opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Computed Properties
    
    /// 行程时长描述
    private var tripDurationDescription: String {
        if includeReturn {
            return "往返 \(tripDays) 天"
        } else {
            return "单程 \(tripDays) 天"
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        TripDurationCard(
            tripDays: .constant(3),
            includeReturn: .constant(true)
        )
        
        TripDurationCard(
            tripDays: .constant(7),
            includeReturn: .constant(false)
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
