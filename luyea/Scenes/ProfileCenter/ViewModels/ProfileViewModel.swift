import Foundation
import Combine
import SwiftUI

/// 个人中心模块视图模型
///
/// 作为ProfileCenter模块的核心业务逻辑层，负责协调数据服务和UI展示。
/// 遵循MVVM架构模式和四层架构规范，提供响应式的数据绑定。
///
/// 功能特性:
/// - 用户资料状态管理和数据绑定
/// - 统计数据的格式化和展示逻辑
/// - 页面导航和路由管理
/// - 错误处理和用户反馈
/// - 数据刷新和同步控制
///
/// 架构职责:
/// - 🎯 业务逻辑：处理模块核心业务逻辑
/// - 📊 状态管理：管理UI状态和数据状态
/// - 🔄 数据协调：协调Service层和View层的数据流
/// - 🎭 用户交互：处理用户操作和页面导航
///
/// 依赖关系:
/// - ProfileCenterService: 个人中心业务服务
/// - ProfileModels: 数据模型定义
/// - ProfileCenterConstants: 常量配置
///
/// - Note: 使用@MainActor确保UI更新在主线程执行
/// - Warning: 长时间操作需要显示加载状态，避免界面卡顿
@MainActor
class ProfileViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前用户资料
    @Published var userProfile: ProfileModels.UserProfile?
    
    /// 加载状态
    @Published var isLoading = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    /// 是否显示错误提示
    @Published var showError = false
    
    /// 头像动画状态
    @Published var avatarOffset: CGFloat = 0.0
    @Published var avatarScale: CGFloat = 1.0
    
    // MARK: - Private Properties

    private let profileCenterService: ProfileCenterServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    /// 初始化视图模型
    /// - Parameter profileCenterService: 个人中心服务，默认使用默认实例
    init(profileCenterService: ProfileCenterServiceProtocol = ProfileCenterService()) {
        self.profileCenterService = profileCenterService
        loadUserProfile()
    }
    
    // MARK: - Public Methods
    
    /// 加载用户资料
    func loadUserProfile() {
        isLoading = true
        errorMessage = nil

        Task {
            do {
                let profile = try await profileCenterService.fetchUserProfile()

                await MainActor.run {
                    self.userProfile = profile
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.handleError(error)
                }
            }
        }
    }

    /// 触发头像跳跃动画
    func triggerAvatarAnimation() {
        // 重置动画状态
        avatarOffset = 0.0
        avatarScale = 1.0
        
        // 第一次跳跃
        withAnimation(.easeOut(duration: 0.3)) {
            avatarOffset = -20
            avatarScale = 1.1
        }
        
        // 第一次落地
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation(.easeIn(duration: 0.2)) {
                self.avatarOffset = 0
                self.avatarScale = 0.95
            }
        }
        
        // 第二次跳跃
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.easeOut(duration: 0.25)) {
                self.avatarOffset = -15
                self.avatarScale = 1.05
            }
        }
        
        // 第二次落地
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.75) {
            withAnimation(.easeIn(duration: 0.15)) {
                self.avatarOffset = 0
                self.avatarScale = 0.98
            }
        }
        
        // 第三次跳跃
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.9) {
            withAnimation(.easeOut(duration: 0.2)) {
                self.avatarOffset = -8
                self.avatarScale = 1.02
            }
        }
        
        // 最终落下并恢复
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.1) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                self.avatarOffset = 0
                self.avatarScale = 1.0
            }
        }
    }
    

    
    // MARK: - Private Methods
    

    
    /// 处理错误
    private func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        showError = true
        
        // 3秒后自动隐藏错误提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.showError = false
        }
    }
}

// MARK: - Computed Properties

extension ProfileViewModel {
    
    /// 是否有用户数据
    var hasUserData: Bool {
        userProfile != nil
    }
    
    /// 用户显示名称
    var displayName: String {
        userProfile?.displayName ?? "用户"
    }
    
    /// 用户ID显示
    var userIdDisplay: String {
        guard let id = userProfile?.id else { return "" }
        return "ID: \(id)"
    }
    
    /// 用户归属地
    var userLocation: String {
        userProfile?.location ?? "未知"
    }
    
    /// 用户头像URL
    var avatarUrl: String? {
        userProfile?.avatarUrl
    }
    
    /// 用户统计数据
    var userStats: ProfileModels.UserStats {
        userProfile?.stats ?? .empty
    }
    
    /// 是否显示加载状态
    var shouldShowLoading: Bool {
        isLoading && userProfile == nil
    }
    
    /// 是否显示内容
    var shouldShowContent: Bool {
        !isLoading && userProfile != nil
    }
}

// MARK: - Mock Data Support

extension ProfileViewModel {
    
    /// 创建用于预览的Mock实例
    static func mock() -> ProfileViewModel {
        let viewModel = ProfileViewModel()
        viewModel.userProfile = ProfileModels.UserProfile.sample
        return viewModel
    }
}
