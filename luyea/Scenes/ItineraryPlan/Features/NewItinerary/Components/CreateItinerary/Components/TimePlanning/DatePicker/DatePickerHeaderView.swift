import SwiftUI

/// 日期选择器标题视图
///
/// 包含拖拽指示器、标题、取消和确定按钮。
/// 提供统一的标题栏样式和交互功能。
struct DatePickerHeaderView: View {
    
    // MARK: - Properties
    
    let onCancel: () -> Void
    let onConfirm: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 16) {
            // 顶部拖拽指示器
            dragIndicator
            
            // 标题和按钮
            headerContent
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - Private Views
    
    /// 拖拽指示器
    private var dragIndicator: some View {
        RoundedRectangle(cornerRadius: 2.5)
            .fill(Color.secondary.opacity(0.3))
            .frame(width: 36, height: 5)
            .padding(.top, 8)
    }
    
    /// 标题内容
    private var headerContent: some View {
        HStack {
            // 取消按钮
            Button("取消") {
                onCancel()
            }
            .font(.body)
            .foregroundColor(.blue)
            
            Spacer()
            
            // 标题区域
            VStack(spacing: 4) {
                Text("选择出发时间")
                    .font(.headline.weight(.semibold))
                    .foregroundColor(.primary)
                
                Text("为您的旅行选择最佳出发日期")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 确定按钮
            Button("确定") {
                onConfirm()
            }
            .font(.body)
            .foregroundColor(.blue)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 8)
    }
}

// MARK: - Preview

#Preview {
    DatePickerHeaderView(
        onCancel: {},
        onConfirm: {}
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
