import SwiftUI

enum DiscoverConstants {
    // MARK: - Layout
    enum Layout {
        /// 卡片水平方向的内边距
        static let horizontalPadding: CGFloat = 12
        /// 卡片垂直方向的内边距
        static let verticalPadding: CGFloat = 8
        /// 卡片之间的间距
        static let itemSpacing: CGFloat = 12
        /// 瀑布流列数
        static let columns: Int = 2
        /// 内容整体的内边距
        static let contentInset = EdgeInsets(top: 16, leading: 12, bottom: 8, trailing: 12)
    }

    // MARK: - Card
    enum Card {
        /// 卡片图片最大高度
        static let maxImageHeight: CGFloat = 280
    }

    // MARK: - Performance
    enum Performance {
        /// 图片预加载数量（可见区域覆盖）
        static let imagePreloadCount: Int = 20

        /// 滚动位置恢复延迟（秒）
        static let scrollRestoreDelay: TimeInterval = 0.1

        /// 视图缓存最大数量
        static let maxCachedViews: Int = 50

        // 注意：以下配置已迁移到全局配置：
        // - 防抖间隔: DesignSystemConstants.Interaction.fastDebounceDelay
        // - Tab切换动画时长: DesignSystemConstants.Animation.tabSwitch
    }
}