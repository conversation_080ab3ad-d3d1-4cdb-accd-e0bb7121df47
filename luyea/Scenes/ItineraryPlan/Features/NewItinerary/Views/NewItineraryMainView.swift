import Combine
import MapKit
import SwiftUI

/// 新建行程主视图 - 分层安全区域架构
///
/// 采用分层安全区域设计，地图背景提供沉浸式体验，内容层保持在安全区域内。
/// 专注核心功能，确保UI元素的可访问性和视觉体验的平衡。
///
/// 架构层次：
/// - 背景层：地图背景视图（延伸到安全区域）
/// - 内容层：底部弹窗容器（保持在安全区域内）
struct NewItineraryMainView: View {

    // MARK: - Environment & Dependencies

    @Environment(\.dismiss) private var dismiss
    @StateObject private var mapBackgroundManager = MapBackgroundManager()
    @StateObject private var contentManager = ContentManager()

    // MARK: - View State

    @State private var isInitialized = false
    @State private var loadingPhase: LoadingPhase = .contentOnly

    // MARK: - Body

    var body: some View {
        ZStack {
            // 背景层：地图背景 - 统一安全区域处理
            Group {
                if loadingPhase.shouldCreateMap {
                    BackgroundMapLayer(mapBackgroundManager: mapBackgroundManager)
                        .opacity(loadingPhase.mapOpacity)
                        .animation(.easeInOut(duration: 0.5), value: loadingPhase)
                        .transition(.opacity)
                } else {
                    // 地图占位符 - 立即显示，与真实地图保持一致的安全区域
                    mapPlaceholderView
                }
            }
            .ignoresSafeArea(.all, edges: .all)

            // 内容层：底部弹窗（包含所有UI元素、覆盖功能和交互处理）- 保持在安全区域内
            NewItineraryContentView(contentManager: contentManager, dismissAction: { dismiss() })
                .opacity(loadingPhase.contentOpacity)
                .animation(.easeOut(duration: 0.3), value: loadingPhase)
        }
        .onAppear(perform: initializeView)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
        .hideTabBar()
    }

    // MARK: - Private Views

    /// 地图占位符视图 - 轻量级，立即显示
    private var mapPlaceholderView: some View {
        ZStack {
            // 渐变背景，模拟地图样式
            LinearGradient(
                colors: [
                    Color(.systemBlue).opacity(0.08),
                    Color(.systemTeal).opacity(0.04),
                    Color(.systemGray6)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // 简洁的加载提示
            VStack(spacing: 8) {
                Image(systemName: "map")
                    .font(.system(size: 24, weight: .light))
                    .foregroundColor(.secondary.opacity(0.4))

                Text("准备地图...")
                    .font(.caption2)
                    .foregroundColor(.secondary.opacity(0.6))
            }
            .scaleEffect(0.9)
            .opacity(0.7)
        }
        .appBackground()
    }

    // MARK: - Private Methods

    /// 初始化视图 - 简化版本
    private func initializeView() {
        // 同步初始化管理器
        mapBackgroundManager.initialize()
        contentManager.initialize()

        // 开始UI阶段加载
        Task {
            await loadUIPhases()
        }

        Log.info("🎯 新建行程管理器初始化完成")
    }

    /// 分阶段加载UI组件 - 全面优化版本，真正的渐进式加载
    private func loadUIPhases() async {
        Log.info("🎯 开始分阶段UI加载")

        // 阶段1：立即显示内容层（已经是 .contentOnly 状态）
        // 用户可以立即看到并操作界面

        // 阶段2：延迟1.2秒开始地图加载准备
        try? await Task.sleep(for: .milliseconds(1200))

        await MainActor.run {
            withAnimation(.easeInOut(duration: 0.5)) {
                loadingPhase = .mapLoading
            }
        }

        Log.info("🗺️ 开始地图组件创建")

        // 阶段3：等待地图组件创建和初始化
        try? await Task.sleep(for: .milliseconds(800))

        await MainActor.run {
            withAnimation(.easeInOut(duration: 0.8)) {
                loadingPhase = .fullyLoaded
            }
        }

        Log.info("🎯 UI加载完全完成")
    }
}

// MARK: - Loading Phase

/// 加载阶段枚举 - 优化版本，支持真正的渐进式加载
enum LoadingPhase {
    case contentOnly    // 仅显示内容层，地图完全隐藏
    case mapLoading     // 地图开始加载，显示占位符
    case fullyLoaded    // 完全加载完成

    /// 地图透明度 - 实现真正的延迟显示
    var mapOpacity: Double {
        switch self {
        case .contentOnly: return 0      // 初始完全隐藏地图
        case .mapLoading: return 0.3     // 加载中半透明显示
        case .fullyLoaded: return 1      // 完全显示
        }
    }

    /// 内容层透明度 - 立即显示
    var contentOpacity: Double {
        switch self {
        case .contentOnly, .mapLoading, .fullyLoaded: return 1
        }
    }

    /// 是否应该创建地图组件
    var shouldCreateMap: Bool {
        switch self {
        case .contentOnly: return false  // 初始阶段不创建地图
        case .mapLoading, .fullyLoaded: return true
        }
    }
}
// MARK: - Preview

#Preview {
    NavigationStack {
        NewItineraryMainView()
            // 全局Toast无需环境对象
    }
}
