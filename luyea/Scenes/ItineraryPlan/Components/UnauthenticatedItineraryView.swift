import SwiftUI

struct UnauthenticatedItineraryView: View {
    @Environment(\.authenticationManager) private var authManager
    @State private var animate = false
    
    var body: some View {
        VStack(spacing: 32) {
            animatedIconView

            VStack(spacing: 20) {
                contentView
                actionButton
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 32)
        .onAppear {
            animate = true
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityDescription)
    }

    private var animatedIconView: some View {
        ZStack {
            backgroundCircle
            mapIcon
        }
    }

    private var backgroundCircle: some View {
        Circle()
            .fill(LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.12),
                    Color.purple.opacity(0.10)
                ]),
                startPoint: .top,
                endPoint: .bottom
            ))
            .frame(width: 120, height: 120)
            .scaleEffect(animate ? 1.08 : 0.95)
            .animation(
                .easeInOut(duration: 1.2).repeatForever(autoreverses: true),
                value: animate
            )
    }

    private var mapIcon: some View {
        Image(systemName: "map")
            .resizable()
            .scaledToFit()
            .frame(width: 54, height: 54)
            .foregroundColor(.blue.opacity(0.7))
            .rotationEffect(.degrees(animate ? 8 : -8))
            .animation(
                .easeInOut(duration: 1.2).repeatForever(autoreverses: true),
                value: animate
            )
    }

    private var contentView: some View {
        VStack(spacing: 8) {
            titleView
            subtitleView
            featuresView
        }
    }

    private var titleView: some View {
        Text("登录后开始规划")
            .font(.system(size: 20, weight: .medium, design: .rounded))
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
    }

    private var subtitleView: some View {
        Text("登录账户，开始创建和管理您的专属旅行计划")
            .font(.system(size: 15))
            .foregroundColor(.gray)
            .multilineTextAlignment(.center)
            .lineLimit(2)
    }

    private var featuresView: some View {
        VStack(spacing: 6) {
            featureRow(icon: "map.fill", text: "创建个性化行程")
            featureRow(icon: "heart.fill", text: "收藏喜爱的景点")
            featureRow(icon: "square.and.arrow.up.fill", text: "分享旅行足迹")
        }
        .padding(.top, 12)
    }

    private func featureRow(icon: String, text: String) -> some View {
        HStack(spacing: 10) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.blue.opacity(0.6))
                .frame(width: 16)

            Text(text)
                .font(.system(size: 13, weight: .regular))
                .foregroundColor(.gray)

            Spacer()
        }
    }

    private var actionButton: some View {
        Button(action: {
            authManager.presentLogin()
        }) {
            HStack(spacing: 8) {
                Image(systemName: "person.crop.circle.badge.plus")
                    .font(.system(size: 16, weight: .semibold))

                Text("立即登录")
                    .font(.system(size: 16, weight: .semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                LinearGradient(
                    colors: [
                        Color.blue.opacity(0.85),
                        Color.purple.opacity(0.8)
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var accessibilityDescription: String {
        "登录后开始规划。登录账户，开始创建和管理您的专属旅行计划。功能包括：创建个性化行程、收藏喜爱的景点、分享旅行足迹。"
    }
}
