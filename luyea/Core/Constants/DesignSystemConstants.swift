import Foundation
import SwiftUI

/// 全局设计系统常量
///
/// 统一管理应用的设计语言，包括动画、间距、圆角等视觉标准。
/// 确保整个应用的视觉一致性，便于维护和扩展。
///
/// 使用原则：
/// - 所有模块应优先使用这里定义的标准常量
/// - 只有在业务特殊需求时才在模块中定义专用常量
/// - 修改设计标准时只需在此处统一修改
enum DesignSystemConstants {
    
    // MARK: - 动画系统

    /// 动画时长和效果标准
    enum Animation {
        /// 标准动画时长 - 用于大多数UI转换
        static let standard: TimeInterval = 0.3

        /// 快速动画时长 - 用于微交互和反馈
        static let fast: TimeInterval = 0.2

        /// 慢速动画时长 - 用于重要的状态变化
        static let slow: TimeInterval = 0.5

        /// 超快动画时长 - 用于即时反馈
        static let ultraFast: TimeInterval = 0.15

        /// 弹簧动画参数
        enum Spring {
            /// 标准弹簧响应时间
            static let response: Double = 0.5
            /// 标准弹簧阻尼
            static let damping: Double = 0.75
            /// 标准弹簧混合时长
            static let blendDuration: Double = 0.3
        }

        /// 淡入淡出动画时长
        static let fadeInOut: TimeInterval = 0.25

        /// Tab切换动画时长
        static let tabSwitch: TimeInterval = 0.3
    }

    // MARK: - 交互系统

    /// 用户交互相关的时间常量
    enum Interaction {
        /// 标准防抖延迟 - 用于搜索等输入操作
        static let debounceDelay: TimeInterval = 0.3

        /// 快速防抖延迟 - 用于实时反馈场景
        static let fastDebounceDelay: TimeInterval = 0.2

        /// 慢速防抖延迟 - 用于复杂搜索操作
        static let slowDebounceDelay: TimeInterval = 0.5
    }
    
    // MARK: - 间距系统

    /// 精简的间距体系 - 基于8pt网格系统，保留4个核心级别
    enum Spacing {
        /// 小间距 - 8pt
        static let small: CGFloat = 8

        /// 标准间距 - 16pt (最常用)
        static let standard: CGFloat = 16

        /// 大间距 - 24pt
        static let large: CGFloat = 24

        /// 超大间距 - 32pt
        static let xl: CGFloat = 32
    }
    
    // MARK: - 圆角系统

    /// 精简的圆角半径体系 - 保留3个核心级别
    enum CornerRadius {
        /// 小圆角 - 8pt (用于小组件)
        static let small: CGFloat = 8

        /// 标准圆角 - 12pt (最常用，按钮、输入框等)
        static let `default`: CGFloat = 12

        /// 大圆角 - 16pt (用于卡片、容器等)
        static let large: CGFloat = 16
    }
    
    // MARK: - 尺寸系统

    /// 业务特定尺寸定义 (移除了与iOS系统重复的标准尺寸)
    enum Size {
        /// 小按钮高度 (非标准尺寸，保留)
        static let smallButtonHeight: CGFloat = 36

        /// 大按钮高度 (非标准尺寸，保留)
        static let largeButtonHeight: CGFloat = 52

        /// 输入框高度 (业务特定尺寸，保留)
        static let inputHeight: CGFloat = 48

        /// 标准图标尺寸 (业务特定，保留)
        static let iconSize: CGFloat = 24

        /// 小图标尺寸 (业务特定，保留)
        static let smallIconSize: CGFloat = 16

        /// 大图标尺寸 (业务特定，保留)
        static let largeIconSize: CGFloat = 32

        // 注意：以下尺寸已移除，请直接使用iOS系统默认值：
        // - buttonHeight (44pt) - 使用系统标准按钮高度
        // - searchBarHeight (44pt) - 使用系统标准搜索栏高度
        // - navigationBarHeight (44pt) - 使用系统标准导航栏高度
    }
    
    // MARK: - 阴影系统
    
    /// 标准阴影效果
    enum Shadow {
        /// 轻微阴影
        static let light = (radius: CGFloat(4), opacity: 0.1, offset: CGSize(width: 0, height: 2))
        
        /// 标准阴影
        static let standard = (radius: CGFloat(8), opacity: 0.15, offset: CGSize(width: 0, height: 4))
        
        /// 重阴影
        static let heavy = (radius: CGFloat(16), opacity: 0.2, offset: CGSize(width: 0, height: 8))
    }
}

// MARK: - SwiftUI 便利扩展

extension DesignSystemConstants {
    
    /// 标准弹簧动画
    static var standardSpringAnimation: SwiftUI.Animation {
        SwiftUI.Animation.interactiveSpring(
            response: Animation.Spring.response,
            dampingFraction: Animation.Spring.damping,
            blendDuration: Animation.Spring.blendDuration
        )
    }
    
    /// 标准缓动动画
    static var standardEaseAnimation: SwiftUI.Animation {
        SwiftUI.Animation.easeInOut(duration: Animation.standard)
    }
    
    /// 快速缓动动画
    static var fastEaseAnimation: SwiftUI.Animation {
        SwiftUI.Animation.easeInOut(duration: Animation.fast)
    }
    
    /// 慢速缓动动画
    static var slowEaseAnimation: SwiftUI.Animation {
        SwiftUI.Animation.easeInOut(duration: Animation.slow)
    }
}

// MARK: - 使用指南注释

/*
 精简设计系统使用指南：

 1. 间距使用 (4个核心级别)：
    .padding(DesignSystemConstants.Spacing.standard)      // 16pt - 最常用
    .padding(.horizontal, DesignSystemConstants.Spacing.large)  // 24pt
    .padding(DesignSystemConstants.Spacing.small)         // 8pt
    .padding(DesignSystemConstants.Spacing.xl)            // 32pt

 2. 圆角使用 (3个核心级别)：
    .cornerRadius(DesignSystemConstants.CornerRadius.default)  // 12pt - 最常用
    .cornerRadius(DesignSystemConstants.CornerRadius.large)    // 16pt - 卡片
    .cornerRadius(DesignSystemConstants.CornerRadius.small)    // 8pt - 小组件

 3. 动画使用：
    withAnimation(DesignSystemConstants.standardEaseAnimation) { ... }
    withAnimation(DesignSystemConstants.fastEaseAnimation) { ... }

 4. 尺寸使用 (仅业务特定尺寸)：
    .frame(height: DesignSystemConstants.Size.inputHeight)     // 48pt
    .frame(width: DesignSystemConstants.Size.iconSize)        // 24pt
    // 注意：标准按钮高度(44pt)请直接使用系统默认值

 5. 阴影使用：
    let shadow = DesignSystemConstants.Shadow.standard
    .shadow(radius: shadow.radius, x: shadow.offset.width, y: shadow.offset.height)

 设计原则：
 - 优先使用语义化名称 (standard, default, large)
 - 避免使用技术性名称 (md, lg, xl)
 - 系统标准尺寸直接使用iOS默认值，无需常量
 */
