import Foundation
import Security

/// Keychain安全存储服务
class KeychainService {
    
    // MARK: - Singleton
    
    static let shared = KeychainService()
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// 保存字符串到Keychain
    /// - Parameters:
    ///   - key: 存储键
    ///   - value: 要存储的字符串值
    /// - Returns: 是否保存成功
    @discardableResult
    func save(key: String, value: String) -> Bool {
        guard let data = value.data(using: .utf8) else {
            Log.error("❌ [KeychainService] 字符串转换为Data失败: \(key)")
            return false
        }
        return save(key: key, data: data)
    }
    
    /// 保存Data到Keychain
    /// - Parameters:
    ///   - key: 存储键
    ///   - data: 要存储的数据
    /// - Returns: 是否保存成功
    @discardableResult
    func save(key: String, data: Data) -> Bool {
        // 先删除已存在的项
        delete(key: key)
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecSuccess {
            Log.debug("✅ [KeychainService] 保存成功: \(key)")
            return true
        } else {
            Log.error("❌ [KeychainService] 保存失败: \(key), 状态码: \(status)")
            return false
        }
    }
    
    /// 从Keychain获取字符串
    /// - Parameter key: 存储键
    /// - Returns: 存储的字符串值，如果不存在则返回nil
    func get(key: String) -> String? {
        guard let data = getData(key: key) else {
            return nil
        }
        return String(data: data, encoding: .utf8)
    }
    
    /// 从Keychain获取Data
    /// - Parameter key: 存储键
    /// - Returns: 存储的数据，如果不存在则返回nil
    func getData(key: String) -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess {
            Log.debug("✅ [KeychainService] 获取成功: \(key)")
            return result as? Data
        } else if status == errSecItemNotFound {
            Log.debug("ℹ️ [KeychainService] 项目不存在: \(key)")
            return nil
        } else {
            Log.error("❌ [KeychainService] 获取失败: \(key), 状态码: \(status)")
            return nil
        }
    }
    
    /// 从Keychain删除项目
    /// - Parameter key: 存储键
    /// - Returns: 是否删除成功
    @discardableResult
    func delete(key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        if status == errSecSuccess || status == errSecItemNotFound {
            Log.debug("✅ [KeychainService] 删除成功: \(key)")
            return true
        } else {
            Log.error("❌ [KeychainService] 删除失败: \(key), 状态码: \(status)")
            return false
        }
    }
    
    /// 检查Keychain中是否存在指定键
    /// - Parameter key: 存储键
    /// - Returns: 是否存在
    func exists(key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: false,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /// 清除所有存储的项目
    /// - Returns: 是否清除成功
    @discardableResult
    func clearAll() -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        if status == errSecSuccess || status == errSecItemNotFound {
            Log.info("✅ [KeychainService] 清除所有项目成功")
            return true
        } else {
            Log.error("❌ [KeychainService] 清除所有项目失败, 状态码: \(status)")
            return false
        }
    }
    
    /// 获取所有存储的键
    /// - Returns: 所有键的数组
    func getAllKeys() -> [String] {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecReturnAttributes as String: true,
            kSecMatchLimit as String: kSecMatchLimitAll
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let items = result as? [[String: Any]] else {
            Log.debug("ℹ️ [KeychainService] 没有找到任何项目")
            return []
        }
        
        let keys = items.compactMap { item in
            item[kSecAttrAccount as String] as? String
        }
        
        Log.debug("✅ [KeychainService] 找到 \(keys.count) 个项目")
        return keys
    }
}

// MARK: - 便利方法

extension KeychainService {
    
    /// 保存用户令牌
    func saveAccessToken(_ token: String) -> Bool {
        return save(key: "access_token", value: token)
    }
    
    /// 获取用户令牌
    func getAccessToken() -> String? {
        return get(key: "access_token")
    }
    
    /// 删除用户令牌
    func deleteAccessToken() -> Bool {
        return delete(key: "access_token")
    }
    
    /// 保存刷新令牌
    func saveRefreshToken(_ token: String) -> Bool {
        return save(key: "refresh_token", value: token)
    }
    
    /// 获取刷新令牌
    func getRefreshToken() -> String? {
        return get(key: "refresh_token")
    }
    
    /// 删除刷新令牌
    func deleteRefreshToken() -> Bool {
        return delete(key: "refresh_token")
    }
    
    /// 保存用户数据
    func saveUserData<T: Codable>(_ user: T) -> Bool {
        guard let data = try? JSONEncoder().encode(user) else {
            Log.error("❌ [KeychainService] 用户数据编码失败")
            return false
        }
        return save(key: "user_data", data: data)
    }
    
    /// 获取用户数据
    func getUserData<T: Codable>(type: T.Type) -> T? {
        guard let data = getData(key: "user_data") else {
            return nil
        }
        return try? JSONDecoder().decode(type, from: data)
    }
    
    /// 删除用户数据
    func deleteUserData() -> Bool {
        return delete(key: "user_data")
    }
}
