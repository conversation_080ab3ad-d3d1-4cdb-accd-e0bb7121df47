import SwiftUI

/// 通用加载状态视图
/// 
/// 提供统一的加载、错误、空状态展示组件，支持自定义样式和交互。
/// 可在整个应用中复用，确保一致的用户体验。
/// 注意：与ExploreJourney中的LoadingStateView区分，此为通用版本。
struct UniversalLoadingStateView: View {
    // MARK: - State Enum
    
    /// 加载状态枚举
    enum State {
        case loading
        case error(Error)
        case empty
        case success
    }
    
    // MARK: - Properties
    
    /// 当前状态
    let state: State
    
    /// 加载文本
    let loadingText: String
    
    /// 错误标题
    let errorTitle: String
    
    /// 空状态标题
    let emptyTitle: String
    
    /// 空状态描述
    let emptyDescription: String
    
    /// 重试回调
    let onRetry: (() -> Void)?

    /// 是否显示重试按钮
    let showRetryButton: Bool

    /// 是否显示背景
    let showBackground: Bool
    
    // MARK: - Initialization
    
    /// 初始化方法
    ///
    /// - Parameters:
    ///   - state: 当前状态
    ///   - loadingText: 加载文本，默认为"加载中..."
    ///   - errorTitle: 错误标题，默认为"加载失败"
    ///   - emptyTitle: 空状态标题，默认为"暂无数据"
    ///   - emptyDescription: 空状态描述，默认为"暂时没有相关内容"
    ///   - showRetryButton: 是否显示重试按钮，默认为true
    ///   - showBackground: 是否显示背景，默认为true
    ///   - onRetry: 重试回调
    init(
        state: State,
        loadingText: String = "加载中...",
        errorTitle: String = "加载失败",
        emptyTitle: String = "暂无数据",
        emptyDescription: String = "暂时没有相关内容",
        showRetryButton: Bool = true,
        showBackground: Bool = true,
        onRetry: (() -> Void)? = nil
    ) {
        self.state = state
        self.loadingText = loadingText
        self.errorTitle = errorTitle
        self.emptyTitle = emptyTitle
        self.emptyDescription = emptyDescription
        self.showRetryButton = showRetryButton
        self.showBackground = showBackground
        self.onRetry = onRetry
    }
    
    // MARK: - Body
    
    var body: some View {
        Group {
            switch state {
            case .loading:
                loadingView
            case .error(let error):
                errorView(error)
            case .empty:
                emptyView
            case .success:
                EmptyView()
            }
        }
    }
    
    // MARK: - View Components
    
    /// 加载视图
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(.accentColor)

            Text(loadingText)
                .font(.body)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(showBackground ? Color(.systemBackground) : Color.clear)
    }
    
    /// 错误视图
    private func errorView(_ error: Error) -> some View {
        VStack(spacing: 20) {
            // 错误图标
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            // 错误信息
            VStack(spacing: 8) {
                Text(errorTitle)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(error.localizedDescription)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            // 重试按钮
            if showRetryButton, let onRetry = onRetry {
                Button(action: onRetry) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.clockwise")
                        Text("重试")
                    }
                    .font(.body.weight(.medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.accentColor)
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
    
    /// 空状态视图
    private var emptyView: some View {
        VStack(spacing: 20) {
            // 空状态图标
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            // 空状态文本
            VStack(spacing: 8) {
                Text(emptyTitle)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(emptyDescription)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            
            // 刷新按钮（可选）
            if let onRetry = onRetry {
                Button(action: onRetry) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.clockwise")
                        Text("刷新")
                    }
                    .font(.body.weight(.medium))
                    .foregroundColor(.accentColor)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.accentColor.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Convenience Extensions

extension UniversalLoadingStateView {
    /// 创建加载状态视图
    static func loading(_ text: String = "加载中...", showBackground: Bool = true) -> UniversalLoadingStateView {
        UniversalLoadingStateView(state: .loading, loadingText: text, showBackground: showBackground)
    }
    
    /// 创建错误状态视图
    static func error(_ error: Error, onRetry: @escaping () -> Void) -> UniversalLoadingStateView {
        UniversalLoadingStateView(state: .error(error), onRetry: onRetry)
    }
    
    /// 创建空状态视图
    static func empty(
        title: String = "暂无数据",
        description: String = "暂时没有相关内容",
        onRefresh: (() -> Void)? = nil
    ) -> UniversalLoadingStateView {
        UniversalLoadingStateView(
            state: .empty,
            emptyTitle: title,
            emptyDescription: description,
            onRetry: onRefresh
        )
    }
}

// MARK: - Preview

#Preview("通用加载状态") {
    VStack(spacing: 20) {
        UniversalLoadingStateView.loading("正在加载数据...")
            .frame(height: 200)
            .border(Color.gray.opacity(0.3))
        
        UniversalLoadingStateView.error(
            NSError(domain: "TestError", code: -1, userInfo: [NSLocalizedDescriptionKey: "网络连接失败，请检查网络设置"]),
            onRetry: { Log.debug("重试") }
        )
        .frame(height: 200)
        .border(Color.gray.opacity(0.3))
        
        UniversalLoadingStateView.empty(
            title: "暂无行程",
            description: "还没有创建任何行程，快去规划你的第一次旅行吧！",
            onRefresh: { Log.debug("刷新") }
        )
        .frame(height: 200)
        .border(Color.gray.opacity(0.3))
    }
    .padding()
}
