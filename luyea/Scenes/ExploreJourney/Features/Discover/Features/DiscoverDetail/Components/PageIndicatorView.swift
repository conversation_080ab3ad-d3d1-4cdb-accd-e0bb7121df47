import SwiftUI

struct PageIndicatorView: View {
    let totalPages: Int
    let currentPage: Int
    
    var body: some View {
        if totalPages > 1 {
            HStack(spacing: 6) {
                ForEach(0..<totalPages, id: \.self) { index in
                    Circle()
                        .fill(index == currentPage ? Color.white : Color.white.opacity(0.4))
                        .frame(width: 8, height: 8)
                        .scaleEffect(index == currentPage ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.3), value: currentPage)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.black.opacity(0.5))
            )
        }
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.blue
            .ignoresSafeArea()
        
        VStack(spacing: 20) {
            PageIndicatorView(totalPages: 3, currentPage: 0)
            PageIndicatorView(totalPages: 3, currentPage: 1)
            PageIndicatorView(totalPages: 3, currentPage: 2)
            PageIndicatorView(totalPages: 1, currentPage: 0) // 单页不显示
        }
    }
}
