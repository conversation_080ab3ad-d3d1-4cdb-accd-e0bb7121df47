import SwiftUI

/// Authentication功能按钮组件
/// 
/// 职责：
/// - 提供统一的按钮样式和交互
/// - 支持主要按钮、次要按钮和第三方登录按钮
/// - 处理加载状态和禁用状态
struct AuthenticationButtonView: View {
    
    // MARK: - Properties
    
    let title: String
    let style: ButtonStyle
    let isEnabled: Bool
    let isLoading: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    // MARK: - Button Styles
    
    enum ButtonStyle {
        case primary
        case secondary
        case wechat
        case apple
        
        var backgroundColor: Color {
            switch self {
            case .primary:
                return Color.accentColor
            case .secondary:
                return Color(.systemBackground)
            case .wechat:
                return AuthenticationConstants.Colors.wechatGreen
            case .apple:
                return AuthenticationConstants.Colors.appleBlack
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary, .wechat, .apple:
                return Color.white
            case .secondary:
                return Color.primary
            }
        }
        
        var disabledBackgroundColor: Color {
            return Color(.systemGray4)
        }

        var disabledForegroundColor: Color {
            return Color(.systemGray2)
        }
        
        var icon: String? {
            switch self {
            case .wechat:
                return AuthenticationConstants.Icons.wechat
            case .apple:
                return AuthenticationConstants.Icons.apple
            default:
                return nil
            }
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                // 图标
                if let iconName = style.icon {
                    Image(systemName: iconName)
                        .font(.system(size: iconSize, weight: .medium))
                        .foregroundColor(currentForegroundColor)
                }

                // 加载指示器或标题
                if isLoading {
                    HStack(spacing: 6) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: currentForegroundColor))
                            .scaleEffect(0.7)

                        Text("加载中...")
                            .font(.system(size: textSize, weight: .semibold))
                    }
                } else {
                    Text(title)
                        .font(.system(size: textSize, weight: .semibold))
                }
            }
            .foregroundColor(currentForegroundColor)
            .frame(maxWidth: .infinity)
            .frame(height: modernButtonHeight)
            .background(
                RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.default)
                    .fill(backgroundGradient)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.default)
                            .stroke(borderGradient, lineWidth: borderWidth)
                    )
            )
            .scaleEffect(buttonScale)
            .animation(.easeInOut(duration: 0.2), value: isPressed)
            .animation(.easeInOut(duration: 0.2), value: isEnabled)
        }
        .disabled(!isEnabled || isLoading)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
    
    // MARK: - Computed Properties
    
    private var currentForegroundColor: Color {
        isEnabled && !isLoading ? style.foregroundColor : style.disabledForegroundColor
    }
    
    private var borderWidth: CGFloat {
        style == .secondary ? 1.0 : 0
    }
    
    private var modernButtonHeight: CGFloat {
        switch style {
        case .wechat, .apple:
            return 44
        default:
            return 52
        }
    }

    private var backgroundGradient: some ShapeStyle {
        if !isEnabled || isLoading {
            return AnyShapeStyle(.gray.opacity(0.3))
        }

        switch style {
        case .primary:
            return AnyShapeStyle(LinearGradient(
                gradient: Gradient(colors: [Color.accentColor, .purple]),
                startPoint: .leading,
                endPoint: .trailing
            ))
        case .wechat:
            return AnyShapeStyle(Color(red: 0.09, green: 0.74, blue: 0.11))
        case .apple:
            return AnyShapeStyle(.primary)
        case .secondary:
            return AnyShapeStyle(.clear)
        }
    }

    private var borderGradient: LinearGradient {
        switch style {
        case .secondary:
            return LinearGradient(
                gradient: Gradient(colors: [
                    .gray.opacity(0.3),
                    .gray.opacity(0.2)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        default:
            return LinearGradient(
                gradient: Gradient(colors: [
                    .clear,
                    .clear
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }



    private var buttonScale: CGFloat {
        isPressed ? 0.98 : 1.0
    }

    private var iconSize: CGFloat {
        switch style {
        case .wechat, .apple: return 14
        default: return 16
        }
    }

    private var textSize: CGFloat {
        switch style {
        case .wechat, .apple: return 14
        default: return 16
        }
    }
}

// MARK: - Static Factory Methods

extension AuthenticationButtonView {
    
    /// 主要按钮
    static func primary(
        title: String,
        isEnabled: Bool = true,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) -> AuthenticationButtonView {
        AuthenticationButtonView(
            title: title,
            style: .primary,
            isEnabled: isEnabled,
            isLoading: isLoading,
            action: action
        )
    }
    
    /// 次要按钮
    static func secondary(
        title: String,
        isEnabled: Bool = true,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) -> AuthenticationButtonView {
        AuthenticationButtonView(
            title: title,
            style: .secondary,
            isEnabled: isEnabled,
            isLoading: isLoading,
            action: action
        )
    }
    
    /// 微信登录按钮
    static func wechat(
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> AuthenticationButtonView {
        AuthenticationButtonView(
            title: AuthenticationConstants.Text.wechatLoginTitle,
            style: .wechat,
            isEnabled: isEnabled,
            isLoading: false,
            action: action
        )
    }
    
    /// Apple登录按钮
    static func apple(
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> AuthenticationButtonView {
        AuthenticationButtonView(
            title: AuthenticationConstants.Text.appleLoginTitle,
            style: .apple,
            isEnabled: isEnabled,
            isLoading: false,
            action: action
        )
    }
}

#Preview("Authentication按钮组件") {
    VStack(spacing: 16) {
        AuthenticationButtonView.primary(title: "登录", isEnabled: true, isLoading: false) {}
        AuthenticationButtonView.primary(title: "登录", isEnabled: true, isLoading: true) {}
        AuthenticationButtonView.primary(title: "登录", isEnabled: false, isLoading: false) {}
        AuthenticationButtonView.secondary(title: "取消", isEnabled: true) {}
        HStack(spacing: 16) {
            AuthenticationButtonView.wechat {}
            AuthenticationButtonView.apple {}
        }
    }
    .padding()
    .appBackground()
}
