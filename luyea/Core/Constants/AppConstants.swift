import Foundation

/// 应用级常量定义
///
/// 统一管理应用的基本信息常量，包括名称、版本、标识符等。
/// 这些常量通常从 Bundle 中动态获取，确保与实际配置保持一致。
enum AppConstants {
    // MARK: - 应用基本信息

    /// 应用名称
    static let appName = "luyea"

    /// 应用显示名称
    static let appDisplayName = Bundle.main.infoDictionary?["CFBundleDisplayName"] as? String ?? "路亦"

    /// 应用版本号
    static let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"

    /// 应用构建版本
    static let buildVersion = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"

    /// 应用包标识符
    static let bundleIdentifier = Bundle.main.bundleIdentifier ?? "com.luyea.app"

    // MARK: - 应用元数据

    /// 应用分类
    static let appCategory = "旅游规划"

    /// 开发者信息
    static let developerName = "路亦团队"

    /// 版权信息
    static let copyright = "© 2024 路亦团队. All rights reserved."

    /// 应用描述
    static let appDescription = "智能旅游规划助手，让每一次旅行都充满惊喜"

    // MARK: - 版本信息

    /// 完整版本信息字符串
    static var fullVersionString: String {
        return "\(appVersion) (\(buildVersion))"
    }

    /// 应用信息字符串
    static var appInfoString: String {
        return "\(appDisplayName) \(fullVersionString)"
    }
}