import SwiftUI

/// 启动界面视图
struct SplashView: View {
    @Binding var isShowingSplash: Bool
    @StateObject private var viewModel = SplashViewModel()
    
    var body: some View {
        splashContent
            .onAppear {
                viewModel.setupSplashConfig()
                viewModel.startCountdown()
            }
            .onChange(of: viewModel.isShowingSplash) {
                if !viewModel.isShowingSplash {
                    isShowingSplash = false
                }
            }
    }
    
    private var splashContent: some View {
        ZStack {
            Group {
                switch viewModel.currentContentType {
                case .image:
                    CachedAsyncImage(
                        url: URL(string: LaunchDataService.shared.splashConfig.imageUrl ?? "")
                    ) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .clipped()
                    } placeholder: {
                        VStack(spacing: 20) {
                            ProgressView()
                                .scaleEffect(1.5)
                            Text("加载中...")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.secondary)
                        }
                    } errorView: {
                        VStack(spacing: 20) {
                            Image("LaunchErrorImage")
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(maxWidth: .infinity, maxHeight: .infinity)
                                .clipped()

                            Text("网络图片加载失败，已切换到本地图片")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.secondary)
                                .padding(.horizontal)
                                .multilineTextAlignment(.center)
                        }
                    }
                case .native:
                    NativeContentView()
                case .ad:
                    if let adId = LaunchDataService.shared.splashConfig.adId {
                        AdContentView(adId: adId)
                    } else {
                        NativeContentView()
                    }
                }
            }
            .transition(.opacity)
            
            if viewModel.currentContentType != .native {
                VStack {
                    HStack {
                        Button(action: {
                            withAnimation {
                                isShowingSplash = false
                            }
                        }) {
                            Text("跳过 \(viewModel.duration)")
                                .font(.system(size: 12))
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.black.opacity(0.5))
                                .clipShape(Capsule())
                        }
                        .padding(.leading, 20)
                        .padding(.top, 60)
                        Spacer()
                    }
                    Spacer()
                }
            }
        }
        .ignoresSafeArea()
    }
}
