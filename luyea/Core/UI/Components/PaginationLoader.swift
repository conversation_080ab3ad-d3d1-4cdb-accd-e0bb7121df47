import SwiftUI

/// 通用分页加载组件
/// 结合现有的PaginationResponse模型，支持自动检测滚动位置并触发分页加载
struct PaginationLoader<Item: Identifiable, Content: View>: View {
    
    // MARK: - Properties
    
    /// 数据源
    let items: [Item]
    
    /// 是否正在加载
    let isLoading: Bool
    
    /// 是否还有更多数据
    let hasMoreData: Bool
    
    /// 触发加载的阈值（距离底部多少个项目时开始加载）
    let loadThreshold: Int
    
    /// 加载更多数据的回调
    let onLoadMore: () -> Void
    
    /// 内容构建器
    let content: (Item) -> Content
    
    // MARK: - Initialization
    
    /// 初始化分页加载器
    /// - Parameters:
    ///   - items: 数据源数组
    ///   - isLoading: 是否正在加载状态
    ///   - hasMoreData: 是否还有更多数据可加载
    ///   - loadThreshold: 触发加载的阈值，默认为6（距离底部6个项目时开始加载）
    ///   - onLoadMore: 加载更多数据的回调函数
    ///   - content: 每个项目的内容构建器
    init(
        items: [Item],
        isLoading: Bool,
        hasMoreData: Bool,
        loadThreshold: Int = 6,
        onLoadMore: @escaping () -> Void,
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = items
        self.isLoading = isLoading
        self.hasMoreData = hasMoreData
        self.loadThreshold = loadThreshold
        self.onLoadMore = onLoadMore
        self.content = content
    }
    
    /// 使用PaginationResponse初始化分页加载器
    /// - Parameters:
    ///   - paginationResponse: 分页响应对象
    ///   - allItems: 所有已加载的数据项（包含之前页面的数据）
    ///   - isLoading: 是否正在加载状态
    ///   - loadThreshold: 触发加载的阈值，默认为6
    ///   - onLoadMore: 加载更多数据的回调函数
    ///   - content: 每个项目的内容构建器
    init<T>(
        paginationResponse: PaginationResponse<T>,
        allItems: [Item],
        isLoading: Bool,
        loadThreshold: Int = 6,
        onLoadMore: @escaping () -> Void,
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = allItems
        self.isLoading = isLoading
        self.hasMoreData = paginationResponse.hasMore
        self.loadThreshold = loadThreshold
        self.onLoadMore = onLoadMore
        self.content = content
    }
    
    // MARK: - Body
    
    var body: some View {
        ForEach(items) { item in
            content(item)
                .onAppear {
                    checkForLoadMore(currentItem: item)
                }
        }
    }
    
    // MARK: - Private Methods
    
    /// 检查是否需要加载更多数据
    /// - Parameter currentItem: 当前显示的项目
    private func checkForLoadMore(currentItem: Item) {
        guard !isLoading && hasMoreData else { return }
        guard items.count >= loadThreshold else { return }
        
        // 计算触发阈值：当滚动到倒数第N个项目时开始加载
        let thresholdIndex = items.index(items.endIndex, offsetBy: -loadThreshold)
        
        if let itemIndex = items.firstIndex(where: { $0.id == currentItem.id }),
           itemIndex >= thresholdIndex {
            onLoadMore()
        }
    }
}

// MARK: - 分页状态管理器

/// 通用分页状态管理器
/// 结合PaginationResponse模型，提供完整的分页状态管理功能
@MainActor
class PaginationManager<T: Codable & Identifiable>: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 所有已加载的数据项
    @Published var allItems: [T] = []
    
    /// 当前页码
    @Published var currentPage: Int = 1
    
    /// 每页数据量
    @Published var pageSize: Int = 20
    
    /// 是否正在加载
    @Published var isLoading: Bool = false
    
    /// 是否还有更多数据
    @Published var hasMoreData: Bool = true
    
    /// 是否已加载过数据
    @Published var hasLoadedData: Bool = false
    
    // MARK: - Private Properties
    
    /// 加载数据的回调函数
    private let loadDataCallback: (PaginationRequest) async throws -> PaginationResponse<T>
    
    // MARK: - Initialization
    
    /// 初始化分页管理器
    /// - Parameters:
    ///   - pageSize: 每页数据量，默认为20
    ///   - loadData: 加载数据的回调函数
    init(
        pageSize: Int = 20,
        loadData: @escaping (PaginationRequest) async throws -> PaginationResponse<T>
    ) {
        self.pageSize = pageSize
        self.loadDataCallback = loadData
    }
    
    // MARK: - Public Methods
    
    /// 加载第一页数据
    func loadFirstPage() async {
        currentPage = 1
        allItems.removeAll()
        hasLoadedData = false
        await loadData()
    }
    
    /// 加载更多数据（下一页）
    func loadMoreData() async {
        guard !isLoading && hasMoreData else { return }
        currentPage += 1
        await loadData()
    }
    
    /// 刷新数据
    func refresh() async {
        await loadFirstPage()
    }
    
    // MARK: - Private Methods
    
    /// 执行数据加载
    private func loadData() async {
        guard !isLoading else { return }
        
        isLoading = true
        
        do {
            let request = PaginationRequest(page: currentPage, pageSize: pageSize)
            let response = try await loadDataCallback(request)
            
            if currentPage == 1 {
                // 第一页：替换所有数据
                allItems = response.items
            } else {
                // 后续页：追加数据
                allItems.append(contentsOf: response.items)
            }
            
            hasMoreData = response.hasMore
            hasLoadedData = true
            
        } catch {
            // 加载失败时回退页码
            if currentPage > 1 {
                currentPage -= 1
            }
            // 这里可以添加错误处理逻辑
            print("分页加载失败: \(error)")
        }
        
        isLoading = false
    }
}

// MARK: - 便捷扩展

extension PaginationLoader {
    
    /// 使用PaginationManager创建分页加载器
    /// - Parameters:
    ///   - manager: 分页管理器
    ///   - loadThreshold: 触发加载的阈值
    ///   - content: 内容构建器
    /// - Returns: 分页加载器视图
    static func withManager(
        _ manager: PaginationManager<Item>,
        loadThreshold: Int = 6,
        @ViewBuilder content: @escaping (Item) -> Content
    ) -> PaginationLoader<Item, Content> where Item: Codable {
        PaginationLoader(
            items: manager.allItems,
            isLoading: manager.isLoading,
            hasMoreData: manager.hasMoreData,
            loadThreshold: loadThreshold,
            onLoadMore: {
                Task {
                    await manager.loadMoreData()
                }
            },
            content: content
        )
    }
}

// MARK: - Preview

#if DEBUG
struct PaginationLoader_Previews: PreviewProvider {
    
    struct MockItem: Identifiable, Codable {
        let id: UUID
        let title: String

        init(title: String) {
            self.id = UUID()
            self.title = title
        }
    }
    
    static var previews: some View {
        Group {
            // 基础用法预览
            ScrollView {
                LazyVStack {
                    PaginationLoader(
                        items: Array(1...20).map { MockItem(title: "Item \($0)") },
                        isLoading: false,
                        hasMoreData: true,
                        onLoadMore: {
                            print("Loading more...")
                        }
                    ) { item in
                        Text(item.title)
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }
                }
                .padding()
            }
            .previewDisplayName("基础用法")
            
            // 使用PaginationManager的预览
            PaginationManagerPreview()
                .previewDisplayName("使用PaginationManager")
        }
    }
}

struct PaginationManagerPreview: View {
    @StateObject private var manager = PaginationManager<PaginationLoader_Previews.MockItem>(
        pageSize: 10
    ) { request in
        // 模拟网络请求
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        
        let startIndex = (request.page - 1) * request.pageSize + 1
        let endIndex = min(startIndex + request.pageSize - 1, 50) // 最多50个项目
        
        let items = Array(startIndex...endIndex).map {
            PaginationLoader_Previews.MockItem(title: "Manager Item \($0)")
        }
        
        return PaginationResponse(
            currentPage: request.page,
            pageSize: request.pageSize,
            hasMore: endIndex < 50,
            items: items
        )
    }
    
    var body: some View {
        ScrollView {
            LazyVStack {
                PaginationLoader.withManager(manager) { item in
                    Text(item.title)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                }
            }
            .padding()
        }
        .task {
            await manager.loadFirstPage()
        }
        .refreshable {
            await manager.refresh()
        }
    }
}
#endif
