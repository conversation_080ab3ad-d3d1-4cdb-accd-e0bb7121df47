import SwiftUI

/// TabBar 主视图组件
///
/// 统一的TabBar视图组件，完全依赖TabBarStateManager进行状态管理。
/// 负责渲染TabBar的UI界面和处理用户交互事件。
///
/// 核心特性：
/// - 使用统一的TabBarStateManager进行状态管理
/// - 自动响应状态变化，无需外部传入绑定
/// - 支持动画效果和用户交互
/// - 遵循Apple Human Interface Guidelines设计规范
/// - 高性能渲染，优化内存使用
///
/// 设计原则：
/// - 无状态组件：不维护内部状态，完全依赖状态管理器
/// - 响应式设计：自动响应状态管理器的变化
/// - 封装性：内部实现细节对外部透明
/// - 可复用性：可在任何需要TabBar的地方使用
struct TabBarView: View {

    // MARK: - Environment & State

    /// TabBar状态管理器
    /// 通过环境对象获取统一的状态管理器实例
    @EnvironmentObject private var tabBarState: TabBarStateManager

    /// 动画命名空间
    /// 用于标签页切换时的匹配几何动画效果
    @Namespace private var indicatorNamespace

    // MARK: - Body

    var body: some View {
        HStack(spacing: 0) {
            ForEach(AppTab.allCases) { tab in
                TabBarItemView(
                    tab: tab,
                    isSelected: tabBarState.selectedTab == tab,
                    indicatorNamespace: indicatorNamespace
                )
                .onTapGesture {
                    // 通过状态管理器处理标签页切换
                    // 确保状态变化的一致性和可追踪性
                    tabBarState.selectTab(tab, animated: true)
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 6)
        .padding(.bottom, 6)
        .background(
            BlurView(style: .systemMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 28, style: .continuous))
                .shadow(color: Color.black.opacity(0.14), radius: 14, x: 0, y: 4)
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 0) // 设置为0，让TabBar最贴近触摸条
    }
}

// 毛玻璃背景封装
struct BlurView: UIViewRepresentable {
    let style: UIBlurEffect.Style
    func makeUIView(context: Context) -> UIVisualEffectView {
        UIVisualEffectView(effect: UIBlurEffect(style: style))
    }
    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {}
} 