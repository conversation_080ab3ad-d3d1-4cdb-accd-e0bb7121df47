import SwiftUI

/// 统一的音频播放器视图
///
/// 根据播放器状态自动切换收缩和展开形态。
/// 合并了原来的AudioPlayerCollapsedView和AudioPlayerExpandedView。
struct AudioPlayerView: View {
    
    // MARK: - Environment
    
    @EnvironmentObject private var playerManager: AudioPlayerManager
    
    // MARK: - Constants
    
    /// 布局配置常量
    private struct LayoutConfig {
        // 收缩状态
        static let collapsedSize: CGFloat = 44
        static let collapsedIconSize: CGFloat = 16
        
        // 展开状态
        static let expandedHeight: CGFloat = 44
        static let expandedMaxWidth: CGFloat = 280
        static let expandedMinWidth: CGFloat = 200
        static let expandedIconSize: CGFloat = 32
        static let expandedButtonSize: CGFloat = 32
        static let titleWidth: CGFloat = 140
        static let horizontalPadding: CGFloat = 16
        static let elementSpacing: CGFloat = 12
        static let cornerRadius: CGFloat = 22
    }
    
    /// 视觉配置常量
    private struct VisualConfig {
        static let shadowRadius: CGFloat = 12
        static let shadowOffset: CGSize = CGSize(width: 0, height: 6)
        static let shadowOpacity: Double = 0.3
        static let borderWidth: CGFloat = 0.5
        static let animationDuration: Double = 0.25
    }
    
    /// 颜色配置
    private struct ColorConfig {
        static let backgroundGradient = LinearGradient(
            colors: [
                Color.black.opacity(0.85),
                Color.black.opacity(0.95)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        static let borderColor = Color.white.opacity(0.2)
        static let shadowColor = Color.black.opacity(0.2)
        static let playingAccentColor = Color.green
        static let pausedAccentColor = Color.gray.opacity(0.3)
        static let iconColor = Color.white
        static let titleColor = Color.white
        static let buttonColor = Color.white
    }
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 收缩状态内容
            if playerManager.playerState == .collapsed {
                collapsedContent
                    .frame(width: LayoutConfig.collapsedSize, height: LayoutConfig.collapsedSize)
                    .opacity(playerManager.playerState == .collapsed ? 1 : 0)
                    .scaleEffect(playerManager.playerState == .collapsed ? 1 : 0.8)
            }

            // 展开状态内容
            if playerManager.playerState == .expanded {
                expandedContent
                    .frame(height: LayoutConfig.expandedHeight)
                    .frame(maxWidth: LayoutConfig.expandedMaxWidth)
                    .opacity(playerManager.playerState == .expanded ? 1 : 0)
                    .scaleEffect(playerManager.playerState == .expanded ? 1 : 0.9)
            }
        }
        .background(ColorConfig.backgroundGradient)
        .clipShape(adaptiveShape)
        .overlay(adaptiveBorder)
        .shadow(
            color: ColorConfig.shadowColor,
            radius: VisualConfig.shadowRadius,
            x: VisualConfig.shadowOffset.width,
            y: VisualConfig.shadowOffset.height
        )
        // 统一的动画参数，与Manager中的动画保持一致
        .animation(.spring(response: 0.45, dampingFraction: 0.8), value: playerManager.playerState)
        .animation(.spring(response: 0.4, dampingFraction: 0.85), value: playerManager.attachmentSide)
        .animation(.easeInOut(duration: 0.15), value: playerManager.isDragging)
        .accessibilityElement(children: .contain)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
    }
    
    // MARK: - Content Views
    
    /// 收缩状态内容
    @ViewBuilder
    private var collapsedContent: some View {
        ZStack {
            // 播放状态指示器
            Circle()
                .fill(currentAccentColor)
                .frame(width: 8, height: 8)
                .opacity(playerManager.isPlaying ? 1.0 : 0.6)
                
            // 音乐图标
            Image(systemName: "music.note")
                .font(.system(size: LayoutConfig.collapsedIconSize, weight: .medium))
                .foregroundColor(ColorConfig.iconColor)
                .opacity(0.9)
        }
        .contentShape(Rectangle())
    }
    
    /// 展开状态内容
    @ViewBuilder
    private var expandedContent: some View {
        HStack(spacing: LayoutConfig.elementSpacing) {
            // 音乐图标
            expandedMusicIcon

            // 标题区域
            titleArea

            // 控制按钮
            controlButtons
        }
    }
    
    // MARK: - Expanded Subviews
    
    /// 展开状态音乐图标
    private var expandedMusicIcon: some View {
        ZStack {
            // 背景圆圈
            Circle()
                .fill(ColorConfig.iconColor.opacity(0.1))
                .frame(width: LayoutConfig.expandedIconSize, height: LayoutConfig.expandedIconSize)
            
            // 播放状态指示环
            if playerManager.isPlaying {
                Circle()
                    .stroke(ColorConfig.playingAccentColor, lineWidth: 2)
                    .frame(width: LayoutConfig.expandedIconSize, height: LayoutConfig.expandedIconSize)
                    .scaleEffect(1.1)
                    .opacity(0.8)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: playerManager.isPlaying)
            }
            
            // 音乐图标
            Image(systemName: "music.note")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(ColorConfig.iconColor)
        }
        .accessibilityElement(children: .ignore)
        .accessibilityLabel("音频播放指示器")
        .accessibilityValue(playerManager.isPlaying ? "正在播放" : "已暂停")
    }
    
    /// 标题区域
    private var titleArea: some View {
        MarqueeTextView(
            text: audioTitle,
            width: LayoutConfig.titleWidth,
            font: .system(size: 14, weight: .medium),
            speed: 30.0
        )
        .foregroundColor(ColorConfig.titleColor)
        .frame(width: LayoutConfig.titleWidth, height: 20, alignment: .leading)
        .accessibilityElement(children: .ignore)
        .accessibilityLabel("正在播放导游语音：\(audioTitle)")
    }
    
    /// 控制按钮
    private var controlButtons: some View {
        HStack(spacing: 8) {
            playPauseButton
            closeButton
        }
    }
    
    /// 播放/暂停按钮
    private var playPauseButton: some View {
        Button(action: togglePlayPause) {
            ZStack {
                // 按钮背景
                Circle()
                    .fill(ColorConfig.buttonColor.opacity(0.1))
                    .frame(width: LayoutConfig.expandedButtonSize, height: LayoutConfig.expandedButtonSize)
                
                // 播放状态指示
                if playerManager.isPlaying {
                    Circle()
                        .stroke(ColorConfig.playingAccentColor, lineWidth: 1.5)
                        .frame(width: LayoutConfig.expandedButtonSize, height: LayoutConfig.expandedButtonSize)
                        .opacity(0.6)
                }
                
                // 播放/暂停图标
                Image(systemName: playPauseIconName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(ColorConfig.buttonColor)
            }
        }
        .buttonStyle(ControlButtonStyle())
        .accessibilityLabel(playerManager.isPlaying ? "暂停" : "播放")
        .accessibilityHint("切换播放或暂停")
    }
    
    /// 关闭按钮
    private var closeButton: some View {
        Button(action: closePlayer) {
            ZStack {
                // 按钮背景
                Circle()
                    .fill(ColorConfig.buttonColor.opacity(0.1))
                    .frame(width: LayoutConfig.expandedButtonSize, height: LayoutConfig.expandedButtonSize)
                
                // 关闭图标
                Image(systemName: "xmark")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(ColorConfig.buttonColor)
            }
        }
        .buttonStyle(ControlButtonStyle())
        .accessibilityLabel("关闭播放器")
        .accessibilityHint("关闭音频播放器")
    }
    
    // MARK: - Adaptive Components
    
    /// 自适应形状
    private var adaptiveShape: some Shape {
        AdaptivePlayerShape(
            playerState: playerManager.playerState,
            attachmentSide: playerManager.attachmentSide,
            isDragging: playerManager.isDragging,
            collapsedSize: LayoutConfig.collapsedSize,
            cornerRadius: LayoutConfig.cornerRadius
        )
    }
    
    /// 自适应边框
    private var adaptiveBorder: some View {
        adaptiveShape
            .stroke(ColorConfig.borderColor, lineWidth: VisualConfig.borderWidth)
    }
    
    // MARK: - Computed Properties
    
    /// 当前强调色
    private var currentAccentColor: Color {
        playerManager.isPlaying ? ColorConfig.playingAccentColor : ColorConfig.pausedAccentColor
    }
    
    /// 音频标题
    private var audioTitle: String {
        playerManager.audioInfo?.title ?? "未知音频"
    }
    
    /// 播放/暂停图标名称
    private var playPauseIconName: String {
        playerManager.isPlaying ? "pause.fill" : "play.fill"
    }
    
    /// 可访问性标签
    private var accessibilityLabel: String {
        if playerManager.playerState == .collapsed {
            return "音频播放器"
        } else {
            return "展开的音频播放器"
        }
    }
    
    /// 可访问性提示
    private var accessibilityHint: String {
        if playerManager.playerState == .collapsed {
            return "点击展开播放器"
        } else {
            return "包含播放控制和音频信息"
        }
    }
    
    // MARK: - Actions
    
    private func togglePlayPause() {
        UIImpactFeedbackGenerator(style: .light).impactOccurred()
        playerManager.togglePlayPause()
    }

    private func closePlayer() {
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
        playerManager.hidePlayer()
    }
}

// MARK: - 自适应播放器形状

/// 根据播放器状态和吸附位置自适应的形状
struct AdaptivePlayerShape: Shape {
    let playerState: PlayerState
    let attachmentSide: AttachmentSide
    let isDragging: Bool
    let collapsedSize: CGFloat
    let cornerRadius: CGFloat

    func path(in rect: CGRect) -> Path {
        var path = Path()

        if playerState == .collapsed {
            // 收缩状态：药丸形状
            let radius = collapsedSize / 2

            if isDragging {
                // 拖拽时显示完整胶囊形状
                path.addRoundedRect(in: rect, cornerSize: CGSize(width: radius, height: radius))
            } else {
                // 非拖拽时根据吸附位置显示半圆
                switch attachmentSide {
                case .left:
                    // 左侧吸附：左边直角，右边圆角
                    path.move(to: CGPoint(x: 0, y: 0))
                    path.addLine(to: CGPoint(x: rect.width - radius, y: 0))
                    path.addArc(
                        center: CGPoint(x: rect.width - radius, y: radius),
                        radius: radius,
                        startAngle: .degrees(-90),
                        endAngle: .degrees(90),
                        clockwise: false
                    )
                    path.addLine(to: CGPoint(x: 0, y: rect.height))
                    path.closeSubpath()

                case .right:
                    // 右侧吸附：右边直角，左边圆角
                    path.move(to: CGPoint(x: radius, y: 0))
                    path.addLine(to: CGPoint(x: rect.width, y: 0))
                    path.addLine(to: CGPoint(x: rect.width, y: rect.height))
                    path.addLine(to: CGPoint(x: radius, y: rect.height))
                    path.addArc(
                        center: CGPoint(x: radius, y: radius),
                        radius: radius,
                        startAngle: .degrees(90),
                        endAngle: .degrees(-90),
                        clockwise: false
                    )
                    path.closeSubpath()
                }
            }
        } else {
            // 展开状态：胶囊形状
            if isDragging {
                // 拖拽时显示完整圆角矩形
                path.addRoundedRect(in: rect, cornerSize: CGSize(width: cornerRadius, height: cornerRadius))
            } else {
                // 非拖拽时根据吸附位置调整形状
                switch attachmentSide {
                case .left:
                    // 左侧吸附：左边直角，右边圆角
                    path.move(to: CGPoint(x: 0, y: 0))
                    path.addLine(to: CGPoint(x: rect.width - cornerRadius, y: 0))
                    path.addArc(
                        center: CGPoint(x: rect.width - cornerRadius, y: cornerRadius),
                        radius: cornerRadius,
                        startAngle: .degrees(-90),
                        endAngle: .degrees(0),
                        clockwise: false
                    )
                    path.addLine(to: CGPoint(x: rect.width, y: rect.height - cornerRadius))
                    path.addArc(
                        center: CGPoint(x: rect.width - cornerRadius, y: rect.height - cornerRadius),
                        radius: cornerRadius,
                        startAngle: .degrees(0),
                        endAngle: .degrees(90),
                        clockwise: false
                    )
                    path.addLine(to: CGPoint(x: 0, y: rect.height))
                    path.closeSubpath()

                case .right:
                    // 右侧吸附：右边直角，左边圆角
                    path.move(to: CGPoint(x: cornerRadius, y: 0))
                    path.addLine(to: CGPoint(x: rect.width, y: 0))
                    path.addLine(to: CGPoint(x: rect.width, y: rect.height))
                    path.addLine(to: CGPoint(x: cornerRadius, y: rect.height))
                    path.addArc(
                        center: CGPoint(x: cornerRadius, y: rect.height - cornerRadius),
                        radius: cornerRadius,
                        startAngle: .degrees(90),
                        endAngle: .degrees(180),
                        clockwise: false
                    )
                    path.addLine(to: CGPoint(x: 0, y: cornerRadius))
                    path.addArc(
                        center: CGPoint(x: cornerRadius, y: cornerRadius),
                        radius: cornerRadius,
                        startAngle: .degrees(180),
                        endAngle: .degrees(270),
                        clockwise: false
                    )
                    path.closeSubpath()
                }
            }
        }

        return path
    }
}

// MARK: - 控制按钮样式

/// 控制按钮样式
struct ControlButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 预览


