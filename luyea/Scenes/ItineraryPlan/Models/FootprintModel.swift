import Foundation

/// FootprintModel（足迹）模型
///
/// 用于表示用户已去过的城市或目的地，支持足迹统计与展示。
/// 支持多种日期格式的解码，包括时间戳和ISO8601字符串。
struct FootprintModel: Identifiable, Equatable, Codable {
    // MARK: - Properties

    /// 足迹唯一标识符
    let id: String

    /// 城市名称
    let city: String

    /// 城市图片URL
    let imageUrl: String

    /// 访问日期（可选）
    var visitDate: Date?

    /// 足迹描述（可选）
    var description: String?

    // MARK: - Initialization

    /// 初始化方法
    init(
        id: String,
        city: String,
        imageUrl: String,
        visitDate: Date? = nil,
        description: String? = nil
    ) {
        self.id = id
        self.city = city
        self.imageUrl = imageUrl
        self.visitDate = visitDate
        self.description = description
    }

    // MARK: - Computed Properties

    /// 格式化的访问日期
    var formattedVisitDate: String? {
        guard let visitDate = visitDate else { return nil }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: visitDate)
    }

    /// 相对时间描述
    var relativeVisitDate: String? {
        guard let visitDate = visitDate else { return nil }
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateTimeStyle = .named
        return formatter.localizedString(for: visitDate, relativeTo: Date())
    }
}

// MARK: - Codable Implementation

extension FootprintModel {
    /// 编码键
    private enum CodingKeys: String, CodingKey {
        case id
        case city
        case imageUrl
        case visitDate
        case description
    }

    /// 自定义解码初始化方法
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        city = try container.decode(String.self, forKey: .city)
        imageUrl = try container.decode(String.self, forKey: .imageUrl)
        description = try container.decodeIfPresent(String.self, forKey: .description)

        // 处理 visitDate 的多种格式
        if let visitDateValue = try? container.decodeIfPresent(Double.self, forKey: .visitDate) {
            // 时间戳格式（秒）
            visitDate = Date(timeIntervalSince1970: visitDateValue)
        } else if let visitDateString = try? container.decodeIfPresent(String.self, forKey: .visitDate) {
            // ISO8601 字符串格式
            let formatter = ISO8601DateFormatter()
            visitDate = formatter.date(from: visitDateString)
        } else {
            visitDate = nil
        }
    }

    /// 自定义编码方法
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(city, forKey: .city)
        try container.encode(imageUrl, forKey: .imageUrl)
        try container.encodeIfPresent(description, forKey: .description)

        // 编码为时间戳
        if let visitDate = visitDate {
            try container.encode(visitDate.timeIntervalSince1970, forKey: .visitDate)
        }
    }
}

// MARK: - Sample Data

extension FootprintModel {
    /// 示例数据
    static let samples: [FootprintModel] = [
        FootprintModel(
            id: "1",
            city: "杭州",
            imageUrl: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80",
            visitDate: Date(timeIntervalSince1970: 1717226400),
            description: "西湖边的悠闲时光"
        ),
        FootprintModel(
            id: "2",
            city: "东京",
            imageUrl: "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80",
            visitDate: Date(timeIntervalSince1970: 1702611600),
            description: "樱花盛开的季节"
        ),
        FootprintModel(
            id: "3",
            city: "巴黎",
            imageUrl: "https://images.unsplash.com/photo-1502602898657-3e91760cbb34?auto=format&fit=crop&w=400&q=80",
            visitDate: Date(timeIntervalSince1970: 1663672800),
            description: "埃菲尔铁塔下的浪漫"
        )
    ]
}