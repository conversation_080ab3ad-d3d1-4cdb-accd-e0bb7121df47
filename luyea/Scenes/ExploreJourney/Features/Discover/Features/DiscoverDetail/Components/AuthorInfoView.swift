import SwiftUI

struct AuthorInfoView: View {
    let avatarUrl: String
    let username: String
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 8) {
                CachedAsyncImage(
                    url: URL(string: avatarUrl)
                ) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 32, height: 32)
                        .clipShape(Circle())
                } placeholder: {
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 32, height: 32)
                        .overlay(
                            Image(systemName: "person.circle.fill")
                                .font(.title3)
                                .foregroundColor(.gray.opacity(0.6))
                        )
                } errorView: {
                    Circle()
                        .fill(Color.red.opacity(0.1))
                        .frame(width: 32, height: 32)
                        .overlay(
                            Image(systemName: "person.circle.fill")
                                .font(.title3)
                                .foregroundColor(.red.opacity(0.6))
                        )
                }
                
                Text(username)
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.5))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.blue
            .ignoresSafeArea()
        
        AuthorInfoView(
            avatarUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e",
            username: "张三",
            onTap: {}
        )
    }
}
