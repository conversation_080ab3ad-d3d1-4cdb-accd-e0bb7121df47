import SwiftUI

/// 排序选项枚举（保持向后兼容）
enum MyWorksSortOption: String, CaseIterable {
    case createTime = "默认"
    case views = "按浏览量"
    case likes = "按喜欢数"
    case forkCount = "按Fork数"

    var displayName: String {
        return self.rawValue
    }

    var iconName: String {
        switch self {
        case .createTime:
            return "clock"
        case .views:
            return "eye"
        case .likes:
            return "heart"
        case .forkCount:
            return "arrow.branch"
        }
    }

    /// 转换为新的排序类型
    var toWorksSortType: MyWorksModels.WorksSortType {
        switch self {
        case .createTime:
            return .createTime // 发布时间映射到创建时间
        case .views:
            return .viewCount
        case .forkCount:
            return .forkCount
        case .likes:
            return .likeCount
        }
    }
}

/// 我的作品管理视图 - MyWorks功能模块的核心视图
///
/// 专门用于管理和展示用户发布的内容作品，支持作品的查看、编辑和管理。
/// 遵循四层架构规范，作为MyWorks功能的主要视图组件。
///
/// 功能特性:
/// - 作品列表展示和管理
/// - 按话题标签筛选作品
/// - 搜索和排序功能
/// - 作品编辑和删除操作
/// - 统计数据可视化展示
/// - 空状态和加载状态处理
///
/// 设计理念:
/// - 基于UserProfileView的设计风格保持一致性
/// - 针对作品管理场景进行专门优化
/// - 提供创作者友好的管理界面
/// - 支持批量操作和快速编辑
///
/// 架构职责:
/// - 🎯 作品管理：提供完整的作品管理功能
/// - 📊 数据展示：展示作品统计和详细信息
/// - 🔍 搜索筛选：支持多维度的内容筛选
/// - ✏️ 编辑操作：支持作品的编辑和管理操作
///
/// - Parameters:
///   - username: 用户显示名称
///
/// - Note: 设计为全屏页面，隐藏TabBar提供沉浸式体验
/// - Warning: 大量作品加载时使用懒加载优化性能
public struct MyWorksView: View {
    // MARK: - Properties

    /// 用户名称
    let username: String

    // MARK: - Environment & State

    /// 页面关闭环境变量
    @Environment(\.dismiss) private var dismiss

    /// 视图模型 - 使用StateObject确保生命周期管理
    @StateObject private var viewModel = MyWorksViewModel()

    /// 标签是否展开
    @State private var isTagsExpanded = false

    /// 搜索是否展开
    @State private var isSearchExpanded = false

    /// 当前选中的排序选项
    @State private var selectedSortOption: MyWorksSortOption = .createTime

    /// 是否显示排序菜单
    @State private var showSortMenu = false

    /// 视图是否已经出现过（用于防止重复加载）
    @State private var hasAppeared = false

    // MARK: - Initialization

    /// 初始化方法
    /// - Parameter username: 用户显示名称
    public init(username: String) {
        self.username = username
    }

    // MARK: - 视图

    public var body: some View {
        ScrollViewReader { scrollProxy in
            ScrollView {
                LazyVStack(spacing: 0, pinnedViews: [.sectionHeaders]) {
                    // 精简的头部区域
                    Section {
                        MyWorksHeaderView(
                            username: self.username
                        )
                    }

                    // ✅ 添加顶部锚点，用于滚动定位
                    Color.clear
                        .frame(height: 1)
                        .id("listTop")
                    // 作品列表区域
                    Section {
                        VStack(spacing: 0) {
                            // 作品列表（当有数据时显示）
                            if !self.viewModel.works.isEmpty {
                                LazyVStack(spacing: 12) {
                                    ForEach(self.viewModel.works) { work in
                                        MyWorksCardView(item: work)
                                            .onAppear {
                                                // 加载更多触发逻辑
                                                let worksCount = self.viewModel.works.count
                                                if worksCount > 2,
                                                   let currentIndex = viewModel.works.firstIndex(where: { $0.id == work.id }),
                                                   currentIndex >= worksCount - 3
                                                {
                                                    self.viewModel.loadMoreWorks(triggeredByItemId: work.id)
                                                }
                                            }

                                            // 优化重新渲染性能
                                            .id(work.id)
                                    }

                                    // 加载更多指示器
                                    if self.viewModel.isLoadingMore {
                                        HStack {
                                            ProgressView()
                                                .scaleEffect(0.8)
                                            Text("加载更多...")
                                                .font(.system(size: 14))
                                                .foregroundColor(.secondary)
                                        }
                                        .padding(.vertical, 20)
                                    }
                                }
                                .padding(.horizontal, DesignSystemConstants.Spacing.standard)
                                .opacity(self.viewModel.isLoading ? 0.6 : 1.0)
                                .blur(radius: self.viewModel.isLoading ? 2 : 0)
                                .animation(.easeInOut(duration: 0.3), value: self.viewModel.isLoading)
                            }

                            // 空视图（当数据为空且不在加载时显示）
                            if self.viewModel.isEmpty && !self.viewModel.isLoading {
                                VStack(spacing: 0) {
                                    // 添加足够的空间确保能触发吸顶
                                    Spacer()

                                    MyWorksEmptyView(selectedTag: self.viewModel.selectedTopic?.name)
                                        .frame(maxWidth: .infinity)

                                    // 底部也添加一些空间
                                    Spacer()
                                }
                            }
                        }
                    } header: {
                        // 筛选栏卡片容器
                        VStack(spacing: 0) {
                            // 标签选择器（如果有话题数据）
                            if !self.viewModel.availableTopics.isEmpty {
                                VStack(spacing: 0) {
                                    // 标签区域标题和操作按钮
                                    VStack(spacing: 0) {
                                        // 顶部控制栏：标题 + 排序 + 搜索按钮
                                        HStack {
                                            Text("作品列表")
                                                .font(.system(size: 16, weight: .medium))
                                                .foregroundColor(.primary)

                                            // 排序选择按钮紧跟标题
                                            MyWorksSortButton(
                                                selectedOption: self.$selectedSortOption,
                                                showMenu: self.$showSortMenu
                                            )

                                            Spacer()

                                            // 搜索按钮在最右侧
                                            MyWorksSearchButton(
                                                isExpanded: self.$isSearchExpanded,
                                                onToggle: {
                                                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                                        if self.isSearchExpanded {
                                                            // 关闭搜索时的处理
                                                            let hadSearchContent = !self.viewModel.searchKeyword.isEmpty
                                                            self.isSearchExpanded = false

                                                            // 如果有搜索内容，清空并重新加载数据
                                                            if hadSearchContent {
                                                                self.viewModel.clearSearch()
                                                            }
                                                        } else {
                                                            // 展开搜索
                                                            self.isSearchExpanded = true
                                                        }
                                                    }
                                                }
                                            )
                                        }
                                        .frame(height: 32) // 顶部控制栏固定高度
                                        .padding(.horizontal, DesignSystemConstants.Spacing.standard)
                                        .padding(.top, 12)

                                        // 搜索输入框（展开时显示）
                                        if self.isSearchExpanded {
                                            MyWorksSearchField(
                                                searchText: self.$viewModel.searchKeyword,
                                                onCancel: {
                                                    self.viewModel.clearSearch()

                                                    // ✅ 清除搜索后滚动到列表顶部
                                                    withAnimation(.easeInOut(duration: 0.5)) {
                                                        scrollProxy.scrollTo("listTop", anchor: .top)
                                                    }
                                                },
                                                onSearch: { keyword in
                                                    // 搜索时自动重置话题选择到"全部"
                                                    self.viewModel.selectedTopic = nil
                                                    self.viewModel.searchWorks(keyword)

                                                    // ✅ 搜索后滚动到列表顶部
                                                    withAnimation(.easeInOut(duration: 0.5)) {
                                                        scrollProxy.scrollTo("listTop", anchor: .top)
                                                    }
                                                }
                                            )
                                            .transition(.asymmetric(
                                                insertion: .move(edge: .top).combined(with: .opacity),
                                                removal: .move(edge: .top).combined(with: .opacity)
                                            ))
                                        }
                                    }

                                    // 标签选择器
                                    MyWorksTagsView(
                                        contentTags: self.viewModel.availableTopics.map { $0.name },
                                        selectedTag: Binding(
                                            get: {
                                                if let topic = viewModel.selectedTopic {
                                                    return topic.name
                                                }
                                                return nil
                                            },
                                            set: { newValue in
                                                if let tagName = newValue {
                                                    let selectedTopicStats = self.viewModel.availableTopics.first { $0.name == tagName }
                                                    self.viewModel.filterByTopic(selectedTopicStats)
                                                } else {
                                                    self.viewModel.filterByTopic(nil)
                                                }

                                                // ✅ 标签切换后滚动到列表顶部
                                                withAnimation(.easeInOut(duration: 0.5)) {
                                                    scrollProxy.scrollTo("listTop", anchor: .top)
                                                }
                                            }
                                        ),
                                        isTagsExpanded: self.$isTagsExpanded,
                                        totalWorksCount: self.viewModel.totalWorksCount,
                                        topicStats: self.viewModel.availableTopics
                                    )
                                    .id("tagsSection")
                                    .padding(.bottom, 4)
                                }
                                .background(Color(.systemBackground))
                                .cornerRadius(DesignSystemConstants.CornerRadius.default)
                                .shadow(
                                    color: Color.black.opacity(0.05),
                                    radius: 8,
                                    x: 0,
                                    y: 2
                                )
                                .padding(.horizontal, DesignSystemConstants.Spacing.standard) // 卡片外边距
                                .padding(.bottom, 12)
                            }
                        }
                    }
                }
            }
            .appBackground()
            .refreshable {
                self.viewModel.refreshWorksList()
            }
            .overlay(
                BeautifulLoadingView.dataLoading(isVisible: self.viewModel.isLoading)
                    .opacity(self.viewModel.isLoading ? 1 : 0)
                    .allowsHitTesting(self.viewModel.isLoading)
            )
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle("我的作品")
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        self.dismiss()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 16, weight: .medium))
                            Text("返回")
                                .font(.system(size: 16))
                        }
                        .foregroundColor(.primary)
                    }
                }
            }
            .onAppear {
                // ✅ 优化：更精确的初始加载条件判断
                if !self.hasAppeared {
                    self.hasAppeared = true
                    self.viewModel.loadInitialData()
                } else if self.viewModel.works.isEmpty && !self.viewModel.isLoading && !self.viewModel.isRefreshing {
                    // 只在确实需要且没有正在加载时重新加载
                    self.viewModel.loadInitialData()
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)) { _ in
                // 收到内存警告时进行优化，而不是在onDisappear时
                self.viewModel.optimizeMemoryUsage()
            }
            .onChange(of: self.selectedSortOption) { _, newOption in
                self.viewModel.changeSortType(newOption.toWorksSortType)

                // ✅ 排序切换后滚动到列表顶部
                withAnimation(.easeInOut(duration: 0.5)) {
                    scrollProxy.scrollTo("listTop", anchor: .top)
                }
            }
        }
    }

    // MARK: - 架构说明

    //
    // 本视图遵循项目四层架构规范：
    // - Views层：当前文件，负责UI展示和用户交互
    // - Components层：MyWorksCardView、MyWorksTagsView等UI组件
    // - ViewModels层：MyWorksViewModel，负责业务逻辑和状态管理
    // - Services层：MyWorksService，负责数据获取和网络请求
    //
    // 性能优化：
    // - 使用LazyVStack实现懒加载，提升大列表性能
    // - 使用UniversalLoadingStateView统一加载状态展示
    // - 集成全局ToastManager和错误处理系统

    // MARK: - 预览

    #Preview("我的作品 - 自适应搜索") {
        MyWorksView(username: "高级知识质子")
    }

    #Preview("空状态") {
        MyWorksView(username: "新用户")
    }
}
