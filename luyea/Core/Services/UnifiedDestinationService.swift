import Foundation

/// 数据源统计信息
struct DataSourceStats {
    let totalCities: Int
    let samplesCount: Int
    let providerCount: Int
    let uniqueCities: Int
}

/// 数据一致性验证结果
struct DataConsistencyResult {
    let isValid: Bool
    let totalCities: Int
    let uniqueNames: Int
    let hasDuplicates: Bool
}

/// 统一的目的地数据服务
///
/// 功能特性：
/// - 统一管理所有目的地数据源
/// - 确保自动识别和手动选择使用相同的数据
/// - 提供城市查找和去重功能
/// - 支持数据源的扩展和更新
class UnifiedDestinationService {
    
    // MARK: - Singleton
    
    static let shared = UnifiedDestinationService()
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// 获取所有可用的城市数据
    /// - Returns: 去重后的城市列表
    func getAllAvailableCities() -> [Destination] {
        // 合并所有可能的城市数据源
        var allCities: [Destination] = []
        
        // 1. 从 Destination.samples 获取
        allCities.append(contentsOf: Destination.samples.filter { $0.type == .city })
        
        // 2. 从 DestinationDataProvider 获取
        allCities.append(contentsOf: DestinationDataProvider.getAllCities())
        
        // 3. 去重（基于城市名称，保留第一个出现的）
        return deduplicateCities(allCities)
    }
    
    /// 根据城市名称查找目的地
    /// - Parameter cityName: 城市名称
    /// - Returns: 匹配的目的地，如果没有找到返回nil
    func findDestination(by cityName: String) -> Destination? {
        let allCities = getAllAvailableCities()
        return allCities.first { $0.name == cityName }
    }
    
    /// 创建新的城市目的地（当数据源中没有找到时）
    /// - Parameters:
    ///   - cityName: 城市名称
    ///   - sourceAttraction: 来源景点（用于获取坐标和图片）
    /// - Returns: 新创建的城市目的地
    func createNewCityDestination(cityName: String, sourceAttraction: AttractionModel) -> Destination {
        // 坐标格式已统一，直接使用
        let coordinate = sourceAttraction.coordinate
        
        // 生成一个基于城市名称的稳定 ID，而不是随机 UUID
        let stableId = generateStableCityId(cityName: cityName)
        
        return Destination(
            id: stableId,
            name: cityName,
            type: .city,
            imageUrl: sourceAttraction.imageUrl,
            description: "从\(sourceAttraction.name)自动识别",
            coordinate: coordinate
        )
    }
    
    /// 智能获取或创建城市目的地
    /// - Parameters:
    ///   - cityName: 城市名称
    ///   - sourceAttraction: 来源景点（用于创建新目的地）
    /// - Returns: 城市目的地
    func getOrCreateCityDestination(cityName: String, sourceAttraction: AttractionModel) -> Destination {
        // 首先尝试从现有数据源中查找
        if let existingDestination = findDestination(by: cityName) {
            return existingDestination
        }

        // 如果没有找到，创建新的目的地
        return createNewCityDestination(cityName: cityName, sourceAttraction: sourceAttraction)
    }

    /// 根据城市名称查找城市（私有方法）
    /// - Parameter cityName: 城市名称
    /// - Returns: 匹配的城市目的地
    private func findCityByName(_ cityName: String) -> Destination? {
        return findDestination(by: cityName)
    }

    /// 智能添加目的地城市到列表
    /// - Parameters:
    ///   - cityName: 城市名称
    ///   - sourceAttraction: 来源景点
    ///   - selectedDestinations: 当前已选择的目的地列表
    /// - Returns: 添加的城市目的地，如果已存在则返回nil
    func addDestinationCityIfNeeded(cityName: String, sourceAttraction: AttractionModel, to selectedDestinations: inout [Destination]) -> Destination? {
        // 检查是否已经存在
        if isCityAlreadySelected(cityName: cityName, in: selectedDestinations) {
            return nil
        }

        // 尝试从数据源中查找城市
        let cityToAdd: Destination
        if let existingCity = findCityByName(cityName) {
            // 找到了预定义的城市，直接使用
            cityToAdd = existingCity
            print("🏙️ 从数据源添加目的地城市: \(cityName) (ID: \(existingCity.id))")
        } else {
            // 没有找到预定义的城市，创建新的城市目的地
            cityToAdd = createNewCityDestination(cityName: cityName, sourceAttraction: sourceAttraction)
            print("🏙️ 创建新的目的地城市: \(cityName) (ID: \(cityToAdd.id))")
        }

        selectedDestinations.append(cityToAdd)
        return cityToAdd
    }

    // MARK: - Private Methods

    /// 去重城市列表
    /// - Parameter cities: 原始城市列表
    /// - Returns: 去重后的城市列表
    private func deduplicateCities(_ cities: [Destination]) -> [Destination] {
        var uniqueCities: [Destination] = []
        var seenCityNames: Set<String> = []

        for city in cities {
            if !seenCityNames.contains(city.name) {
                seenCityNames.insert(city.name)
                uniqueCities.append(city)
            }
        }

        return uniqueCities
    }

    /// 生成基于城市名称的稳定 ID
    /// - Parameter cityName: 城市名称
    /// - Returns: 稳定的城市 ID
    private func generateStableCityId(cityName: String) -> String {
        // 将城市名称转换为稳定的 ID 格式
        let normalizedName = cityName
            .lowercased()
            .replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "市", with: "")
            .replacingOccurrences(of: "省", with: "")

        return "city_\(normalizedName)"
    }
}

// MARK: - 扩展方法

extension UnifiedDestinationService {

    /// 检查城市是否已存在于目的地列表中
    /// - Parameters:
    ///   - cityName: 城市名称
    ///   - destinations: 目的地列表
    /// - Returns: 是否已存在
    func isCityAlreadySelected(cityName: String, in destinations: [Destination]) -> Bool {
        return destinations.contains { destination in
            destination.name == cityName && destination.type == .city
        }
    }

    /// 获取城市的显示信息
    /// - Parameter cityName: 城市名称
    /// - Returns: 城市的显示信息（名称、描述、图片等）
    func getCityDisplayInfo(cityName: String) -> (name: String, description: String?, imageUrl: String?) {
        if let city = findCityByName(cityName) {
            return (city.name, city.description, city.imageUrl)
        } else {
            return (cityName, nil, nil)
        }
    }

    /// 获取推荐的城市列表（基于热门程度）
    /// - Parameter limit: 返回数量限制
    /// - Returns: 推荐的城市列表
    func getRecommendedCities(limit: Int = 10) -> [Destination] {
        let allCities = getAllAvailableCities()

        // 按热门程度排序
        let sortedCities = allCities.sorted { city1, city2 in
            let popularity1 = city1.popularity ?? 0
            let popularity2 = city2.popularity ?? 0
            return popularity1 > popularity2
        }

        return Array(sortedCities.prefix(limit))
    }
}

// MARK: - 调试和日志

extension UnifiedDestinationService {

    /// 获取数据源统计信息
    func getDataSourceStats() -> DataSourceStats {
        let allCities = getAllAvailableCities()
        let samplesCount = Destination.samples.filter { $0.type == .city }.count
        let providerCount = DestinationDataProvider.getAllCities().count

        return DataSourceStats(
            totalCities: allCities.count,
            samplesCount: samplesCount,
            providerCount: providerCount,
            uniqueCities: allCities.count
        )
    }

    /// 验证数据一致性
    func validateDataConsistency() -> DataConsistencyResult {
        let allCities = getAllAvailableCities()
        let cityNames = Set(allCities.map { $0.name })

        // 检查是否有重复的城市名称
        let hasDuplicates = cityNames.count != allCities.count

        return DataConsistencyResult(
            isValid: !hasDuplicates,
            totalCities: allCities.count,
            uniqueNames: cityNames.count,
            hasDuplicates: hasDuplicates
        )
    }
}
