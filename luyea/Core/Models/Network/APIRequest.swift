import Foundation

struct APIRequest {
    var path: String
    var method: String = "GET"
    var query: [String: String] = [:]
    var body: Data? = nil
    var headers: [String: String] = [:]
    
    // MARK: - DSL 构建器
    static func get(_ path: String) -> APIRequest {
        APIRequest(path: path, method: "GET")
    }
    static func post(_ path: String) -> APIRequest {
        APIRequest(path: path, method: "POST")
    }
    static func put(_ path: String) -> APIRequest {
        APIRequest(path: path, method: "PUT")
    }
    static func delete(_ path: String) -> APIRequest {
        APIRequest(path: path, method: "DELETE")
    }
    
    func query(_ params: [String: String]) -> APIRequest {
        var copy = self
        copy.query.merge(params) { $1 }
        return copy
    }
    func query(_ key: String, _ value: String) -> APIRequest {
        var copy = self
        copy.query[key] = value
        return copy
    }
    func header(_ key: String, _ value: String) -> APIRequest {
        var copy = self
        copy.headers[key] = value
        return copy
    }
    func headers(_ dict: [String: String]) -> APIRequest {
        var copy = self
        dict.forEach { copy.headers[$0] = $1 }
        return copy
    }
    func body(_ data: Data) -> APIRequest {
        var copy = self
        copy.body = data
        return copy
    }
}
