import SwiftUI

/// 行程计划区域组件
///
/// 功能特性：
/// - 出发信息设置
/// - 行程时间规划
/// - 预算设置
/// - 返程计划选择
struct TripPlanningSection: View {
    
    // MARK: - Properties

    @Binding var departureLocation: String
    @Binding var departureDate: Date
    @Binding var hasSelectedDate: Bool
    @Binding var tripDays: Int
    @Binding var includeReturn: Bool
    @Binding var estimatedBudget: String
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("行程计划")
                .font(.title2.weight(.bold))
                .foregroundColor(.primary)

            // 计划卡片
            VStack(spacing: 16) {
                // 出发信息
                DepartureInfoCard(
                    departureLocation: $departureLocation,
                    departureDate: $departureDate,
                    hasSelectedDate: $hasSelectedDate
                )

                // 出行天数和返程计划
                TripDurationCard(
                    tripDays: $tripDays,
                    includeReturn: $includeReturn
                )

                // 预计开销（可选）
                BudgetCard(
                    estimatedBudget: $estimatedBudget,
                    tripDays: tripDays
                )
            }
        }
    }
}

/// 出发信息卡片
struct DepartureInfoCard: View {
    @Binding var departureLocation: String
    @Binding var departureDate: Date
    @Binding var hasSelectedDate: Bool
    @State private var showLocationPicker = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            HStack {
                Image(systemName: "airplane.departure")
                    .font(.title3)
                    .foregroundColor(.orange)

                VStack(alignment: .leading, spacing: 2) {
                    HStack(spacing: 8) {
                        Text("出发信息")
                            .font(.headline.weight(.medium))
                            .foregroundColor(.primary)

                        Text("可选")
                            .font(.caption.weight(.medium))
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.secondary.opacity(0.1))
                            .clipShape(Capsule())
                    }

                    Text("出发地点和时间，用于提醒")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            // 出发地点选择
            VStack(spacing: 12) {
                // 出发地点搜索框
                DepartureLocationSelector(
                    selectedLocation: $departureLocation,
                    showLocationPicker: $showLocationPicker
                )

                // 预计出发时间
                DepartureDatePicker(
                    departureDate: $departureDate,
                    hasSelectedDate: $hasSelectedDate
                )
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

/// 预计出发时间选择器
struct DepartureDatePicker: View {
    @Binding var departureDate: Date
    @Binding var hasSelectedDate: Bool
    @State private var showDatePicker = false

    var body: some View {
        Button(action: { showDatePicker = true }) {
            HStack(spacing: 12) {
                Image(systemName: "calendar")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(hasSelectedDate ? .blue : .gray)
                    .frame(width: 20, height: 20)

                VStack(alignment: .leading, spacing: 4) {
                    Text("预计出发时间")
                        .font(.subheadline.weight(.medium))
                        .foregroundColor(.primary)

                    Text(hasSelectedDate ? formattedDepartureDate : "点击选择出发时间")
                        .font(.subheadline)
                        .foregroundColor(hasSelectedDate ? .blue : .secondary)
                        .fontWeight(hasSelectedDate ? .medium : .regular)
                }

                Spacer()

                if hasSelectedDate {
                    Button(action: {
                        hasSelectedDate = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.gray)
                            .frame(width: 20, height: 20)
                    }
                    .onTapGesture {
                        hasSelectedDate = false
                    }
                }

                Image(systemName: "chevron.right")
                    .font(.caption.weight(.semibold))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showDatePicker) {
            DepartureDatePickerSheet(
                selectedDate: $departureDate,
                isPresented: $showDatePicker,
                onDateSelected: {
                    hasSelectedDate = true
                }
            )
        }
    }
    
    /// 格式化的出发日期
    private var formattedDepartureDate: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        let calendar = Calendar.current
        if calendar.isDateInToday(departureDate) {
            return "今天"
        } else if calendar.isDateInTomorrow(departureDate) {
            return "明天"
        } else {
            let daysDiff = calendar.dateComponents([.day], from: Date(), to: departureDate).day ?? 0
            if daysDiff > 0 && daysDiff <= 7 {
                formatter.dateFormat = "EEEE"
                return formatter.string(from: departureDate)
            } else {
                formatter.dateFormat = "M月d日"
                return formatter.string(from: departureDate)
            }
        }
    }
}

/// 预计开销卡片
struct BudgetCard: View {
    @Binding var estimatedBudget: String
    let tripDays: Int
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "yensign.circle")
                    .font(.title3)
                    .foregroundColor(.green)

                VStack(alignment: .leading, spacing: 2) {
                    Text("预计开销")
                        .font(.headline.weight(.medium))
                        .foregroundColor(.primary)

                    Text("可选填，帮助您控制预算")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            // 预算输入
            NumericTextField.currency(
                value: $estimatedBudget,
                placeholder: "如：5000"
            )
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))

            // 预算建议
            if !estimatedBudget.isEmpty, let budget = Double(estimatedBudget) {
                budgetSuggestion(budget: budget)
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    /// 预算建议
    private func budgetSuggestion(budget: Double) -> some View {
        let dailyBudget = budget / Double(tripDays)

        return HStack(spacing: 8) {
            Image(systemName: "lightbulb")
                .font(.caption)
                .foregroundColor(.orange)

            Text("平均每天约 ¥\(Int(dailyBudget))")
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.orange.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

// MARK: - Preview

#Preview {
    TripPlanningSection(
        departureLocation: .constant(""),
        departureDate: .constant(Date()),
        hasSelectedDate: .constant(false),
        tripDays: .constant(3),
        includeReturn: .constant(true),
        estimatedBudget: .constant("")
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
