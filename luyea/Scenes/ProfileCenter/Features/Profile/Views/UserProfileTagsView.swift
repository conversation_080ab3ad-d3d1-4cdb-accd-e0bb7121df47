import SwiftUI

/// 用户主页的标签选择视图，无状态组件（状态由外部Binding传入）
struct UserProfileTagsView: View {
    let contentTags: [String]
    @Binding var selectedTag: String?
    @Binding var isTagsExpanded: Bool
    
    // MARK: - 配置
    private let tagsPerRow = 3
    private let defaultVisibleRows = 2
    
    private var columns: [GridItem] {
        Array(repeating: GridItem(.flexible(), spacing: 12), count: tagsPerRow)
    }
    
    // MARK: - 计算属性
    private var defaultVisibleTagCount: Int {
        tagsPerRow * defaultVisibleRows - 1 // -1 是因为包含"全部"标签
    }
    private var shouldShowExpandButton: Bool {
        contentTags.count > defaultVisibleTagCount
    }
    private var visibleTags: [String] {
        if isTagsExpanded {
            return ["全部"] + contentTags
        } else {
            if let selectedTag = selectedTag,
               let selectedIndex = contentTags.firstIndex(of: selectedTag) {
                let startIndex = max(0, selectedIndex - (defaultVisibleTagCount - 1))
                let endIndex = min(contentTags.count, startIndex + defaultVisibleTagCount)
                let visibleContentTags = Array(contentTags[startIndex..<endIndex])
                return ["全部"] + visibleContentTags
            }
            return ["全部"] + Array(contentTags.prefix(defaultVisibleTagCount))
        }
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 16) {
            // 标签网格
            VStack(spacing: 12) {
                ForEach(0..<(visibleTags.count + tagsPerRow - 1) / tagsPerRow, id: \.self) { row in
                    HStack(spacing: 12) {
                        ForEach(0..<tagsPerRow, id: \.self) { column in
                            let index = row * tagsPerRow + column
                            if index < visibleTags.count {
                                let tag = visibleTags[index]
                                ProfileTagView(
                                    text: tag,
                                    isSelected: tag == "全部" ? selectedTag == nil : selectedTag == tag,
                                    action: {
                                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                            if tag == "全部" {
                                                selectedTag = nil
                                            } else {
                                                selectedTag = tag
                                            }
                                        }
                                    }
                                )
                            } else {
                                Color.clear
                                    .frame(maxWidth: .infinity)
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            
            // 展开/收起按钮
            if shouldShowExpandButton {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isTagsExpanded.toggle()
                    }
                }) {
                    HStack(spacing: 4) {
                        Text(isTagsExpanded ? "收起" : "展开")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                        Image(systemName: isTagsExpanded ? "chevron.up" : "chevron.down")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 8)
                }
            }
        }
        .padding(.vertical, 8)
        .onAppear {
            if selectedTag == nil {
                selectedTag = nil
            }
        }
    }
}

// MARK: - 预览
#Preview("作者模式-默认") {
    UserProfileTagsView(
        contentTags: [
            "美食探店", "城市漫步", "历史文化",
            "自然风光", "艺术展览", "特色民宿",
            "主题乐园", "购物指南"
        ],
        selectedTag: .constant(nil),
        isTagsExpanded: .constant(false)
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("作者模式-展开") {
    UserProfileTagsView(
        contentTags: [
            "美食探店", "城市漫步", "历史文化",
            "自然风光", "艺术展览", "特色民宿",
            "主题乐园", "购物指南"
        ],
        selectedTag: .constant(nil),
        isTagsExpanded: .constant(true)
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("作者模式-选中") {
    UserProfileTagsView(
        contentTags: [
            "美食探店", "城市漫步", "历史文化",
            "自然风光", "艺术展览", "特色民宿",
            "主题乐园", "购物指南"
        ],
        selectedTag: .constant("美食探店"),
        isTagsExpanded: .constant(false)
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("个人模式-默认") {
    UserProfileTagsView(
        contentTags: [
            "美食探店", "城市漫步", "历史文化",
            "自然风光", "艺术展览", "特色民宿",
            "主题乐园", "购物指南", "咖啡探店",
            "摄影", "旅行", "生活"
        ],
        selectedTag: .constant(nil),
        isTagsExpanded: .constant(false)
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("个人模式-展开") {
    UserProfileTagsView(
        contentTags: [
            "美食探店", "城市漫步", "历史文化",
            "自然风光", "艺术展览", "特色民宿",
            "主题乐园", "购物指南", "咖啡探店",
            "摄影", "旅行", "生活"
        ],
        selectedTag: .constant(nil),
        isTagsExpanded: .constant(true)
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("个人模式-选中") {
    UserProfileTagsView(
        contentTags: [
            "美食探店", "城市漫步", "历史文化",
            "自然风光", "艺术展览", "特色民宿",
            "主题乐园", "购物指南", "咖啡探店",
            "摄影", "旅行", "生活"
        ],
        selectedTag: .constant("咖啡探店"),
        isTagsExpanded: .constant(false)
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("新用户模式") {
    UserProfileTagsView(
        contentTags: [],
        selectedTag: .constant(nil),
        isTagsExpanded: .constant(false)
    )
    .padding()
    .background(Color(.systemBackground))
} 