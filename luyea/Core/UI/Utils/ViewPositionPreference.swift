import SwiftUI

/// 视图位置信息结构
struct ViewPositionInfo: Equatable {
    let frame: CGRect
    let height: CGFloat

    static let zero = ViewPositionInfo(frame: .zero, height: 0)
}

/// 用于传递视图位置信息的PreferenceKey
struct ViewPositionPreferenceKey: PreferenceKey {
    static var defaultValue: ViewPositionInfo = .zero
    
    static func reduce(value: inout ViewPositionInfo, nextValue: () -> ViewPositionInfo) {
        let next = nextValue()
        if next.frame != .zero {
            value = next
        }
    }
}

/// 视图扩展：获取位置信息
extension View {
    /// 获取视图在指定坐标空间中的位置信息
    /// - Parameter coordinateSpace: 坐标空间名称
    /// - Returns: 修饰后的视图
    func capturePosition(in coordinateSpace: CoordinateSpace) -> some View {
        background(
            GeometryReader { geometry in
                Color.clear
                    .preference(
                        key: ViewPositionPreferenceKey.self,
                        value: ViewPositionInfo(
                            frame: geometry.frame(in: coordinateSpace),
                            height: geometry.size.height
                        )
                    )
            }
        )
    }
}
