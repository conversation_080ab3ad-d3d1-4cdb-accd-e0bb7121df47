import Foundation
import CoreLocation

/// 探索旅程模块数据模型
enum ExploreJourneyModels {
    
    // MARK: - 基础数据模型
    
    /// 位置信息
    struct Location: Codable, Equatable {
        let latitude: Double
        let longitude: Double
        let name: String?
        let address: String?
        
        init(latitude: Double, longitude: Double, name: String? = nil, address: String? = nil) {
            self.latitude = latitude
            self.longitude = longitude
            self.name = name
            self.address = address
        }
        
        /// 从CLLocation创建
        init(from clLocation: CLLocation, name: String? = nil, address: String? = nil) {
            self.latitude = clLocation.coordinate.latitude
            self.longitude = clLocation.coordinate.longitude
            self.name = name
            self.address = address
        }
    }
    
    /// 目的地信息
    struct Destination: Codable, Identifiable, Equatable {
        let id: String
        let name: String
        let description: String
        let imageUrl: String?
        let location: Location
        let category: String
        let rating: Double
        let reviewCount: Int
        let tags: [String]
        let isPopular: Bool
        
        init(id: String, name: String, description: String, imageUrl: String? = nil, location: Location, category: String, rating: Double, reviewCount: Int, tags: [String] = [], isPopular: Bool = false) {
            self.id = id
            self.name = name
            self.description = description
            self.imageUrl = imageUrl
            self.location = location
            self.category = category
            self.rating = rating
            self.reviewCount = reviewCount
            self.tags = tags
            self.isPopular = isPopular
        }
    }
    
    // MARK: - 推荐相关模型
    
    /// 推荐响应
    struct RecommendationResponse: Codable {
        let featured: [Destination]
        let trending: [Destination]
        let nearYou: [Destination]
        let categories: [Category]
        let totalCount: Int
        
        init(featured: [Destination], trending: [Destination], nearYou: [Destination], categories: [Category], totalCount: Int) {
            self.featured = featured
            self.trending = trending
            self.nearYou = nearYou
            self.categories = categories
            self.totalCount = totalCount
        }
    }
    
    /// 分类信息
    struct Category: Codable, Identifiable, Equatable {
        let id: String
        let name: String
        let iconName: String
        let color: String
        let itemCount: Int
        
        init(id: String, name: String, iconName: String, color: String, itemCount: Int) {
            self.id = id
            self.name = name
            self.iconName = iconName
            self.color = color
            self.itemCount = itemCount
        }
    }
    
    // MARK: - 搜索相关模型
    
    /// 搜索筛选条件
    struct SearchFilters: Codable {
        let category: String?
        let location: String?
        let sortBy: SortOption?
        let priceRange: PriceRange?
        let rating: Double?
        
        init(category: String? = nil, location: String? = nil, sortBy: SortOption? = nil, priceRange: PriceRange? = nil, rating: Double? = nil) {
            self.category = category
            self.location = location
            self.sortBy = sortBy
            self.priceRange = priceRange
            self.rating = rating
        }
    }
    
    /// 排序选项
    enum SortOption: String, Codable, CaseIterable {
        case relevance = "relevance"
        case distance = "distance"
        case rating = "rating"
        case price = "price"
        case newest = "newest"
        
        var displayName: String {
            switch self {
            case .relevance: return "相关性"
            case .distance: return "距离"
            case .rating: return "评分"
            case .price: return "价格"
            case .newest: return "最新"
            }
        }
    }
    
    /// 价格范围
    struct PriceRange: Codable {
        let min: Double
        let max: Double
        
        init(min: Double, max: Double) {
            self.min = min
            self.max = max
        }
    }
    
    /// 搜索响应
    struct SearchResponse: Codable {
        let results: [Destination]
        let totalCount: Int
        let currentPage: Int
        let totalPages: Int
        let suggestions: [String]
        
        init(results: [Destination], totalCount: Int, currentPage: Int, totalPages: Int, suggestions: [String] = []) {
            self.results = results
            self.totalCount = totalCount
            self.currentPage = currentPage
            self.totalPages = totalPages
            self.suggestions = suggestions
        }
    }
    
    // MARK: - 附近内容模型
    
    /// 附近内容响应
    struct NearbyResponse: Codable {
        let items: [NearbyItem]
        let center: Location
        let radius: Double
        let totalCount: Int
        
        init(items: [NearbyItem], center: Location, radius: Double, totalCount: Int) {
            self.items = items
            self.center = center
            self.radius = radius
            self.totalCount = totalCount
        }
    }
    
    /// 附近项目
    struct NearbyItem: Codable, Identifiable, Equatable {
        let id: String
        let destination: Destination
        let location: Location
        let distance: Double // 距离（公里）
        let estimatedTime: String // 预估时间
        
        init(id: String, destination: Destination, location: Location, distance: Double, estimatedTime: String) {
            self.id = id
            self.destination = destination
            self.location = location
            self.distance = distance
            self.estimatedTime = estimatedTime
        }
    }
    
    // MARK: - 用户偏好模型
    
    /// 用户偏好
    struct UserPreferences: Codable {
        let favoriteCategories: [String]
        let preferredLocations: [Location]
        let travelStyle: TravelStyle
        let budget: PriceRange?
        let interests: [String]
        let language: String
        
        init(favoriteCategories: [String] = [], preferredLocations: [Location] = [], travelStyle: TravelStyle = .balanced, budget: PriceRange? = nil, interests: [String] = [], language: String = "zh-CN") {
            self.favoriteCategories = favoriteCategories
            self.preferredLocations = preferredLocations
            self.travelStyle = travelStyle
            self.budget = budget
            self.interests = interests
            self.language = language
        }
    }
    
    /// 旅行风格
    enum TravelStyle: String, Codable, CaseIterable {
        case adventure = "adventure"
        case relaxation = "relaxation"
        case cultural = "cultural"
        case foodie = "foodie"
        case budget = "budget"
        case luxury = "luxury"
        case family = "family"
        case solo = "solo"
        case balanced = "balanced"
        
        var displayName: String {
            switch self {
            case .adventure: return "冒险探索"
            case .relaxation: return "休闲放松"
            case .cultural: return "文化体验"
            case .foodie: return "美食之旅"
            case .budget: return "经济实惠"
            case .luxury: return "奢华享受"
            case .family: return "家庭出游"
            case .solo: return "独自旅行"
            case .balanced: return "均衡体验"
            }
        }
        
        var iconName: String {
            switch self {
            case .adventure: return "mountain.2"
            case .relaxation: return "leaf"
            case .cultural: return "building.columns"
            case .foodie: return "fork.knife"
            case .budget: return "dollarsign.circle"
            case .luxury: return "crown"
            case .family: return "figure.2.and.child.holdinghands"
            case .solo: return "figure.walk"
            case .balanced: return "scale.3d"
            }
        }
    }
}

// MARK: - 示例数据

extension ExploreJourneyModels {
    
    /// 示例位置
    static let sampleLocation = Location(
        latitude: 39.9042,
        longitude: 116.4074,
        name: "北京",
        address: "中国北京市"
    )
    
    /// 示例目的地
    static let sampleDestination = Destination(
        id: "dest_001",
        name: "故宫博物院",
        description: "明清两朝的皇家宫殿，现为世界文化遗产",
        imageUrl: "https://example.com/forbidden-city.jpg",
        location: sampleLocation,
        category: "历史文化",
        rating: 4.8,
        reviewCount: 15420,
        tags: ["历史", "文化", "建筑", "必游"],
        isPopular: true
    )
    
    /// 示例用户偏好
    static let sampleUserPreferences = UserPreferences(
        favoriteCategories: ["历史文化", "自然风光", "美食"],
        preferredLocations: [sampleLocation],
        travelStyle: .cultural,
        budget: PriceRange(min: 100, max: 500),
        interests: ["摄影", "历史", "美食", "建筑"],
        language: "zh-CN"
    )
}
