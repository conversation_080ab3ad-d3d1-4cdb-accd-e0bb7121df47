import SwiftUI

/// 我的作品卡片视图
///
/// 功能特性：
/// - 展示作品基本信息（标题、封面、统计数据）
/// - 支持作品状态显示和操作菜单
/// - 集成项目全局组件（CachedAsyncImage、格式化工具）
/// - 优化的性能表现（懒加载、内存管理）
struct MyWorksCardView: View {
    let item: MyWorksModels.WorkItem
    @State private var showActionSheet = false
    @State private var showEditHistory = false

    // 性能优化：缓存计算属性
    private let cardHeight: CGFloat = 120
    private let imageWidth: CGFloat = 100

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 封面图片 - 让它自然匹配右侧高度
            coverImageView

            // 内容区域
            VStack(alignment: .leading, spacing: 8) {
                // 标题和状态
                titleAndStatusView

                // 基本信息
                basicInfoView

                Spacer()

                // 操作按钮
                actionButtonsView
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(12)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .confirmationDialog("作品操作", isPresented: $showActionSheet) {
            Button("查看数据") { /* TODO: 查看详细数据 */ }
            Button("修改记录") { showEditHistory = true }
            Button("分享作品") { /* TODO: 分享作品 */ }
            Button("删除", role: .destructive) { /* TODO: 删除作品 */ }
            Button("取消", role: .cancel) { }
        }
        .sheet(isPresented: $showEditHistory) {
            MyWorksEditHistoryView(workId: item.id)
        }
    }
    
    // MARK: - 子视图
    @ViewBuilder
    private var coverImageView: some View {
        ZStack {
            // 背景图片 - 性能优化版本
            if let coverImage = item.coverImageUrl {
                CachedAsyncImage(
                    url: URL(string: coverImage)
                ) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .clipped() // 性能优化：明确裁剪边界
                } placeholder: {
                    placeholderView
                } errorView: {
                    placeholderView
                }
            } else {
                defaultImageView
            }
        }
        .frame(width: 120)
        .frame(maxHeight: .infinity)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .overlay(
            // 浮层标签组 - 放在overlay中避免被clipShape裁剪
            VStack {
                // 话题标签 - 右上角
                HStack {
                    Spacer()
                    if let topic = item.topic {
                        tagOverlay(topic.name)
                    }
                }

                Spacer()

                // 位置信息 - 左下角，占满可用宽度
                if let location = item.location {
                    HStack {
                        locationOverlay(location)
                        Spacer(minLength: 0)
                    }
                }
            }
            .padding(DesignSystemConstants.Spacing.small)
            .allowsHitTesting(false)
        )
    }
    
    private var titleAndStatusView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(item.title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                    .lineLimit(2)

                // 状态标签
                statusBadge
            }

            Spacer()
        }
    }

    private var basicInfoView: some View {
        VStack(alignment: .leading, spacing: 6) {
            // 发布时间
            HStack(spacing: 4) {
                Image(systemName: "clock")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                Text(item.createdAt.relativeDescription)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)

                Spacer()
            }

            // 数据指标
            HStack(spacing: 16) {
                // 浏览量
                HStack(spacing: 4) {
                    Image(systemName: "eye")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                    Text(item.viewCount.formatCount())
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }

                // 喜欢数
                HStack(spacing: 4) {
                    Image(systemName: "heart")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                    Text(item.likeCount.formatCount())
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }

                // Fork数量（仅允许Fork的内容显示）
                if item.allowFork {
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.triangle.branch")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                        Text(item.forkCount.formatCount())
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()
            }


        }
    }
    
    private var actionButtonsView: some View {
        HStack(spacing: 12) {
            // 编辑按钮
            Button(action: { /* TODO: 编辑作品 */ }) {
                HStack(spacing: 4) {
                    Image(systemName: "pencil")
                        .font(.system(size: 12))
                    Text("编辑")
                        .font(.system(size: 12))
                }
                .foregroundColor(.blue)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(6)
            }

            // 私密/公开切换按钮
            Button(action: { /* 切换私密状态 */ }) {
                HStack(spacing: 4) {
                    if item.status == .hidden {
                        Image(systemName: "eye")
                            .font(.system(size: 12))
                        Text("公开")
                            .font(.system(size: 12))
                    } else {
                        Image(systemName: "eye.slash")
                            .font(.system(size: 12))
                        Text("私密")
                            .font(.system(size: 12))
                    }
                }
                .foregroundColor(item.status == .hidden ? .blue : .orange)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background((item.status == .hidden ? Color.blue : Color.orange).opacity(0.1))
                .cornerRadius(6)
            }

            Spacer()

            // 更多操作按钮
            Button(action: { showActionSheet = true }) {
                Image(systemName: "ellipsis")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .frame(width: 28, height: 28)
                    .background(Color.gray.opacity(0.1))
                    .clipShape(Circle())
            }
        }
    }

    // 状态标签
    private var statusBadge: some View {
        Group {
            switch item.status {
            case .published:
                Text("已发布")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.green)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(4)
            case .hidden:
                Text("私密")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.orange)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(4)
            case .draft:
                Text("草稿")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.gray)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(4)
            case .deleted:
                Text("已删除")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.red)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(4)
            }
        }
    }



    // 图片上的话题标签浮层
    private func tagOverlay(_ tag: String) -> some View {
        Text(tag)
            .font(.system(size: 11, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, DesignSystemConstants.Spacing.small)
            .padding(.vertical, 4)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [Color.primary.opacity(0.7), Color.primary.opacity(0.5)]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(DesignSystemConstants.CornerRadius.small)
            .shadow(color: Color.primary.opacity(0.3), radius: 1, x: 0, y: 1)
    }

    // 图片上的位置信息浮层
    private func locationOverlay(_ location: String) -> some View {
        HStack(spacing: 4) {
            Image(systemName: "mappin.and.ellipse")
                .font(.system(size: 11))
                .foregroundColor(.white)

            Text(location)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.white)
                .lineLimit(1)
                .truncationMode(.tail)
        }
        .padding(.horizontal, DesignSystemConstants.Spacing.small)
        .padding(.vertical, 4)
        .background(
            Color.black.opacity(0.5)
                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 8))
        )
        .cornerRadius(DesignSystemConstants.CornerRadius.small)
    }







}

// MARK: - 预览
#Preview("优化字体大小的浮层") {
    MyWorksCardView(
        item: MyWorksModels.WorkItem(
            id: "1",
            title: "🌸春日樱花季｜无锡鼋头渚的粉色浪漫",
            description: "三月的鼋头渚，樱花盛开如云霞。湖光山色间，粉色花瓣飞舞，每一帧都是诗意的画面。最佳观赏时间是早上8-10点，人少景美，绝对值得早起！",
            coverImageUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            topic: Topic(id: "1", name: "自然风光", order: 1),
            location: "无锡市滨湖区鼋头渚",
            status: .published,
            createdAt: Date().addingTimeInterval(-3600 * 24 * 3), // 3天前
            viewCount: 12500,
            likeCount: 2847,
            commentCount: 156,
            forkCount: 23,
            isFeatured: true,
            allowFork: true
        )
    )
    .padding()
}

// MARK: - 性能优化的子视图
extension MyWorksCardView {
    /// 优化的占位符视图 - 避免重复创建
    private var placeholderView: some View {
        Rectangle()
            .fill(Color.gray.opacity(0.1))
            .overlay(
                Image(systemName: "photo")
                    .font(.system(size: 20))
                    .foregroundColor(.gray.opacity(0.6))
            )
    }

    /// 默认图片视图 - 避免重复创建
    private var defaultImageView: some View {
        Rectangle()
            .fill(Color.gray.opacity(0.1))
            .overlay(
                Image(systemName: "doc.text")
                    .font(.system(size: 20))
                    .foregroundColor(.gray.opacity(0.6))
            )
    }
}
