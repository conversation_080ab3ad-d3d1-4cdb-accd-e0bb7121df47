import SwiftUI

/// 多选目的地行组件
///
/// 显示单个目的地信息，支持选择状态切换。
/// 包含目的地图片、名称、描述和选择指示器。
struct MultiDestinationRow: View {
    
    // MARK: - Properties
    
    let destination: Destination
    let isSelected: Bool
    let onTap: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        Button(action: {
            // 使用异步操作，确保点击响应不被阻塞
            Task { @MainActor in
                onTap()
            }
        }) {
            HStack(spacing: 16) {
                // 目的地图片
                destinationImage
                
                // 目的地信息
                destinationInfo
                
                Spacer()
                
                // 选择指示器
                selectionIndicator
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Private Views
    
    /// 目的地图片 - 优化版本，支持快速交互
    private var destinationImage: some View {
        CachedAsyncImage(
            url: URL(string: destination.imageUrl ?? "")
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            // 使用更轻量的占位符，减少渲染压力
            Rectangle()
                .fill(Color.gray.opacity(0.15))
                .overlay(
                    Image(systemName: "location.circle")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.gray.opacity(0.6))
                )
        } errorView: {
            // 使用自动尺寸的通用图片错误视图组件
            ImageErrorView()
        }
        .frame(width: 44, height: 44)
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .compositingGroup() // 优化渲染性能
    }
    
    /// 目的地信息
    private var destinationInfo: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(destination.name)
                .font(.headline.weight(.medium))
                .foregroundColor(.primary)
            
            if let description = destination.description {
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
        }
    }
    
    /// 选择指示器
    private var selectionIndicator: some View {
        Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
            .font(.title3)
            .foregroundColor(isSelected ? .blue : .gray.opacity(0.5))
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 0) {
        MultiDestinationRow(
            destination: Destination.samples[0],
            isSelected: false,
            onTap: {}
        )
        
        Divider()
            .padding(.leading, 60)
        
        MultiDestinationRow(
            destination: Destination.samples[1],
            isSelected: true,
            onTap: {}
        )
    }
    .background(Color(.systemBackground))
    .padding()
}
