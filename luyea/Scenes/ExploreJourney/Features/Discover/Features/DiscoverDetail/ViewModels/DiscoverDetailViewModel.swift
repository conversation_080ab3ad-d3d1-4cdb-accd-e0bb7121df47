import SwiftUI
import Combine

/// 发现详情页视图模型
/// 
/// 负责管理详情页的业务逻辑，包括点赞、评论、分享等功能。
/// 遵循MVVM架构，将业务逻辑从View中分离。
@MainActor
class DiscoverDetailViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前页码（图片轮播）
    @Published var currentPage: Int = 0
    
    /// 是否已点赞
    @Published var isLiked: Bool = false
    
    /// 评论输入文本
    @Published var commentText: String = ""
    
    /// 是否显示评论输入框
    @Published var showCommentInput: Bool = false
    
    /// 是否显示作者个人资料
    @Published var isAuthorProfilePresented: Bool = false
    
    /// 是否自动滚动
    @Published var isAutoScrolling: Bool = true
    
    /// 是否显示全屏图片查看器
    @Published var showFullScreenViewer: Bool = false
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 错误信息
    @Published var error: String?
    
    // MARK: - Private Properties
    
    /// 发现项目数据
    private let item: DiscoverItem
    
    /// 网络服务
    private let networkService: NetworkServiceProtocol
    
    /// 取消订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    
    /// 是否有多张图片
    var hasMultipleImages: Bool {
        item.imageUrls.count > 1
    }
    
    /// 当前点赞数
    var likeCount: Int {
        item.likes + (isLiked ? 1 : 0)
    }
    
    /// 评论数量
    var commentCount: Int {
        item.comments.count
    }
    
    // MARK: - Initialization
    
    /// 初始化视图模型
    /// - Parameters:
    ///   - item: 发现项目数据
    ///   - networkService: 网络服务实例
    init(
        item: DiscoverItem,
        networkService: NetworkServiceProtocol = NetworkService.shared
    ) {
        self.item = item
        self.networkService = networkService
        
        // 初始化点赞状态（从本地缓存获取）
        self.isLiked = false
        
        setupBindings()
    }
    
    // MARK: - Public Methods
    
    /// 处理点赞操作
    func handleLike() {
        Task {
            await toggleLike()
        }
    }
    
    /// 处理评论提交
    func handleCommentSubmission() {
        guard !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        Task {
            await submitComment()
        }
    }
    
    /// 处理分享操作
    func handleShare() {
        // 显示分享功能开发中提示
        ToastManager.shared.show("分享功能开发中，敬请期待", style: .info)
    }
    
    /// 显示作者个人资料
    func showAuthorProfile() {
        isAuthorProfilePresented = true
    }
    
    /// 显示全屏图片查看器
    func showFullScreenImageViewer() {
        // 使用高性能的弹性动画，模拟从图片展开的效果
        // 自动滚动的停止由showFullScreenViewer的监听器处理
        withAnimation(.spring(response: 0.55, dampingFraction: 0.85, blendDuration: 0.05)) {
            showFullScreenViewer = true
        }
    }
    
    /// 处理缩放状态变化
    func handleZoomChanged(_ isZoomed: Bool) {
        // 只有在非全屏模式下才根据缩放状态控制自动滚动
        if !showFullScreenViewer {
            isAutoScrolling = !isZoomed
        }
    }
    
    // MARK: - Private Methods
    
    /// 设置数据绑定
    private func setupBindings() {
        // 监听页码变化，确保在有效范围内
        $currentPage
            .sink { [weak self] newPage in
                guard let self = self else { return }
                let validPage = max(0, min(newPage, self.item.imageUrls.count - 1))
                if validPage != newPage {
                    self.currentPage = validPage
                }
            }
            .store(in: &cancellables)

        // 监听全屏查看器状态变化
        $showFullScreenViewer
            .sink { [weak self] isShowing in
                guard let self = self else { return }
                if isShowing {
                    // 显示全屏查看器时，停止自动滚动
                    self.isAutoScrolling = false
                } else if self.hasMultipleImages {
                    // 关闭全屏查看器时，如果有多张图片则恢复自动滚动
                    self.isAutoScrolling = true
                }
            }
            .store(in: &cancellables)
    }
    
    /// 切换点赞状态
    private func toggleLike() async {
        isLoading = true
        error = nil
        
        do {
            // 模拟网络请求
            try await Task.sleep(nanoseconds: 500_000_000)

            // 更新本地状态
            isLiked.toggle()

        } catch {
            self.error = "点赞失败，请重试"
            print("点赞失败: \(error)")
        }
        
        isLoading = false
    }
    
    /// 提交评论
    private func submitComment() async {
        let comment = commentText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !comment.isEmpty else { return }
        
        isLoading = true
        error = nil
        
        do {
            // 模拟网络请求提交评论
            // 实际实现时应调用: let response = try await networkService.request(...)
            try await Task.sleep(nanoseconds: 1_000_000_000)
            
            // 清空输入框并隐藏
            commentText = ""
            showCommentInput = false
            
        } catch {
            self.error = "评论提交失败，请重试"
            print("评论提交失败: \(error)")
        }
        
        isLoading = false
    }
}

// MARK: - Preview Support

extension DiscoverDetailViewModel {
    /// 创建预览用的视图模型
    static func preview() -> DiscoverDetailViewModel {
        let mockItem = DiscoverItem(
            imageUrls: [
                "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
                "https://images.unsplash.com/photo-1469474968028-56623f02e42e"
            ],
            title: "预览标题",
            username: "预览用户",
            userAvatarUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e",
            likes: 128,
            description: "这是预览描述",
            comments: []
        )
        return DiscoverDetailViewModel(item: mockItem)
    }
}
