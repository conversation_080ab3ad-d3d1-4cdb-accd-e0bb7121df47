import SwiftUI

/// 应用全局背景样式系统
///
/// 提供统一的背景样式，确保整个应用的视觉一致性。
/// 所有背景样式集中管理，便于维护和扩展。
///
/// 使用示例：
/// ```swift
/// VStack {
///     // 内容
/// }
/// .appBackground() // 应用默认背景
/// ```
struct AppBackground {

    // MARK: - 预定义背景样式

    /// 默认应用背景（蓝紫渐变）
    ///
    /// 从行程计划页面提取的主题色渐变，作为应用的标准背景。
    /// 渐变从顶部的淡蓝色过渡到底部的淡紫色，营造温和的视觉效果。
    static let defaultGradient = LinearGradient(
        gradient: Gradient(colors: [
            Color.blue.opacity(0.08),
            Color.purple.opacity(0.06)
        ]),
        startPoint: .top,
        endPoint: .bottom
    )

    /// 系统默认背景
    ///
    /// 使用系统原生背景色，自动适配明暗模式。
    /// 适用于需要与系统风格保持一致的页面。
    static let systemBackground = Color(.systemBackground)

    /// 系统分组背景
    ///
    /// 使用系统分组背景色，适用于列表和分组内容。
    /// 在明暗模式下自动调整颜色。
    static let systemGroupedBackground = Color(.systemGroupedBackground)
}

// MARK: - View Extensions

extension View {

    /// 应用默认背景样式（蓝紫渐变）
    ///
    /// 为视图添加应用标准的蓝紫渐变背景。
    /// 背景会自动忽略安全区域，实现全屏覆盖效果。
    ///
    /// - Returns: 带有默认背景的视图
    func appBackground() -> some View {
        ZStack {
            AppBackground.defaultGradient
                .ignoresSafeArea()
            self
        }
    }

    /// 应用系统背景
    ///
    /// 为视图添加系统原生背景色，自动适配明暗模式。
    /// 适用于需要与系统风格保持一致的页面。
    ///
    /// - Returns: 带有系统背景的视图
    func systemBackground() -> some View {
        ZStack {
            AppBackground.systemBackground
                .ignoresSafeArea()
            self
        }
    }

    /// 应用分组系统背景
    ///
    /// 为视图添加系统分组背景色，适用于列表和分组内容。
    /// 在明暗模式下自动调整颜色。
    ///
    /// - Returns: 带有分组背景的视图
    func systemGroupedBackground() -> some View {
        ZStack {
            AppBackground.systemGroupedBackground
                .ignoresSafeArea()
            self
        }
    }
}

// MARK: - Preview

#Preview("默认背景样式") {
    VStack(spacing: 20) {
        Text("默认背景样式")
            .font(.title)
            .fontWeight(.medium)

        Text("蓝紫渐变背景效果预览")
            .font(.body)
            .foregroundColor(.secondary)

        Spacer()

        Button("示例按钮") {
            // 示例操作
        }
        .buttonStyle(.borderedProminent)
        .padding()
    }
    .padding()
    .appBackground()
}

#Preview("系统背景样式") {
    VStack(spacing: 20) {
        Text("系统背景样式")
            .font(.title)
            .fontWeight(.medium)

        Text("系统原生背景色")
            .font(.body)
            .foregroundColor(.secondary)

        Spacer()

        Button("示例按钮") {
            // 示例操作
        }
        .buttonStyle(.bordered)
        .padding()
    }
    .padding()
    .systemBackground()
}
