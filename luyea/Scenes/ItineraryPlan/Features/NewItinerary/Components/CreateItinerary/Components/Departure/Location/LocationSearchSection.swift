import SwiftUI

/// 位置搜索区域组件
///
/// 提供地点搜索功能，包括搜索框和清除按钮。
/// 支持实时搜索和搜索状态管理。
struct LocationSearchSection: View {
    
    // MARK: - Properties
    
    @Binding var searchText: String
    let onSearchChanged: (String) -> Void
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: "magnifyingglass")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                
                TextField("搜索城市名称", text: $searchText)
                    .font(.body)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onChange(of: searchText) { _, newValue in
                        onSearchChanged(newValue)
                    }
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                        onSearchChanged("")
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .background(Color(.systemBackground))
    }
}

// MARK: - Preview

#Preview {
    LocationSearchSection(
        searchText: .constant(""),
        onSearchChanged: { _ in }
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
