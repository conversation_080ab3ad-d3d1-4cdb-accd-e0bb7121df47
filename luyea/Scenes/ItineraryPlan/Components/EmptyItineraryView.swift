import SwiftUI

/// EmptyItineraryView
/// 空状态视图，当用户没有行程计划时显示
///
/// 功能特性：
/// - 动画效果的图标和背景
/// - 友好的提示文案
/// - 响应式布局设计
/// - 支持无障碍访问
struct EmptyItineraryView: View {

    // MARK: - State

    @State private var animate = false

    // MARK: - Body

    var body: some View {
        VStack(spacing: 18) {
            animatedIconView
            titleView
            subtitleView
        }
        .frame(maxWidth: .infinity)
        .onAppear {
            animate = true
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityDescription)
    }

    // MARK: - Private Views

    /// 动画图标视图
    private var animatedIconView: some View {
        ZStack {
            backgroundCircle
            mapIcon
        }
    }

    /// 背景圆圈
    private var backgroundCircle: some View {
        Circle()
            .fill(LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.12),
                    Color.purple.opacity(0.10)
                ]),
                startPoint: .top,
                endPoint: .bottom
            ))
            .frame(width: 120, height: 120)
            .scaleEffect(animate ? 1.08 : 0.95)
            .animation(
                .easeInOut(duration: 1.2).repeatForever(autoreverses: true),
                value: animate
            )
    }

    /// 地图图标
    private var mapIcon: some View {
        Image(systemName: "map")
            .resizable()
            .scaledToFit()
            .frame(width: 54, height: 54)
            .foregroundColor(.blue.opacity(0.7))
            .rotationEffect(.degrees(animate ? 8 : -8))
            .animation(
                .easeInOut(duration: 1.2).repeatForever(autoreverses: true),
                value: animate
            )
    }

    /// 标题视图
    private var titleView: some View {
        Text(ItineraryPlanConstants.Text.noItinerariesTitle)
            .font(.system(size: 20, weight: .medium, design: .rounded))
            .foregroundColor(.secondary)
    }

    /// 副标题视图
    private var subtitleView: some View {
        Text(ItineraryPlanConstants.Text.noItinerariesSubtitle)
            .font(.system(size: 15))
            .foregroundColor(.gray)
            .multilineTextAlignment(.center)
    }

    // MARK: - Computed Properties

    /// 无障碍描述
    private var accessibilityDescription: String {
        "\(ItineraryPlanConstants.Text.noItinerariesTitle)。\(ItineraryPlanConstants.Text.noItinerariesSubtitle)"
    }
}
