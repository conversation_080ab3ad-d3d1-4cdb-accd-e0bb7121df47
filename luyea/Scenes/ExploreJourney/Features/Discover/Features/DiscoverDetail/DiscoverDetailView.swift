import SwiftUI

/// 发现详情功能主入口视图
///
/// 作为Discover的子功能模块，负责展示发现内容的详细信息。
/// 这是DiscoverDetail功能的主入口，封装了所有相关的UI和业务逻辑。
///
/// 功能特性：
/// - 图片轮播展示
/// - 内容详情显示
/// - 用户互动（点赞、评论、分享）
/// - 全屏图片预览
/// - 作者信息展示
struct DiscoverDetailView: View {
    
    // MARK: - Properties
    
    /// 发现项目数据
    let item: DiscoverItem
    
    /// 环境变量：页面关闭
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Body
    
    var body: some View {
        DiscoverDetailContentView(item: item)
            .navigationBarHidden(true)
    }
}

// MARK: - Preview

#Preview {
    NavigationStack {
        DiscoverDetailView(
            item: DiscoverItem(
                imageUrls: [
                    "https://c-ssl.dtstatic.com/uploads/blog/202408/14/9WSP7q6eh8wwMP6.thumb.1000_0.jpg",
                    "https://img2.baidu.com/it/u=3563687558,836722079&fm=253&fmt=auto?w=606&h=1216"
                ],
                title: "示例标题",
                username: "用户名",
                userAvatarUrl: "https://img1.baidu.com/it/u=1747081318,2650263390&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
                likes: 100,
                description: "这是一段示例描述文本，展示了详情页的内容布局。",
                comments: [
                    Comment(username: "用户1", content: "这是一条评论"),
                    Comment(username: "用户2", content: "这是另一条评论")
                ]
            )
        )
    }
}
