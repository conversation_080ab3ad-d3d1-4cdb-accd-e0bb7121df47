import SwiftUI

struct CommentInputView: View {
    @Binding var commentText: String
    @Binding var isVisible: Bool
    let onSubmit: () -> Void
    
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        if isVisible {
            VStack(spacing: 0) {
                Divider()
                
                HStack(spacing: 12) {
                    TextField("写下你的想法...", text: $commentText, axis: .vertical)
                        .textFieldStyle(PlainTextFieldStyle())
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color(.systemGray6))
                        )
                        .focused($isTextFieldFocused)
                        .lineLimit(1...4)
                    
                    <PERSON><PERSON>(action: {
                        if !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            onSubmit()
                            commentText = ""
                            isTextFieldFocused = false
                        }
                    }) {
                        Image(systemName: "paperplane.fill")
                            .font(.system(size: 18))
                            .foregroundColor(commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? .gray : .blue)
                    }
                    .disabled(commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemBackground))
            }
            .transition(.move(edge: .bottom).combined(with: .opacity))
            .onAppear {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isTextFieldFocused = true
                }
            }
        }
    }
}

// MARK: - 预览
#Preview {
    VStack {
        Spacer()
        
        CommentInputView(
            commentText: .constant(""),
            isVisible: .constant(true),
            onSubmit: {}
        )
    }
    .background(Color(.systemGroupedBackground))
}
