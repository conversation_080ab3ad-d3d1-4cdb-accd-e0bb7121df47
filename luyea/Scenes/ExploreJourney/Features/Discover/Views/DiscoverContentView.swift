import SwiftUI
import SwiftUIMasonryLayouts

/// 发现页内容视图
struct DiscoverContentView: View {
    @ObservedObject var viewModel: DiscoverViewModel
    @Binding var isTopicDrawerShowing: Bool
    @Binding var selectedTopicIds: Set<String>
    let onCardTapped: (DiscoverItem) -> Void

    // 简化的滚动位置管理 - 依赖SwiftUI原生机制
    @State private var isNavigatingToDetail = false
    
    var body: some View {
        VStack(spacing: 0) {
            if shouldShowTopicRefreshIndicator {
                topicRefreshIndicator
                    .transition(.move(edge: .top).combined(with: .opacity))
            }

            contentView
        }
        .frame(maxWidth: .infinity)
        .appBackground()
        .navigationBarTitleDisplayMode(.inline)
    }
    private var shouldShowTopicRefreshIndicator: Bool {
        viewModel.isTopicRefreshing &&
        !viewModel.isManualRefreshing &&
        !viewModel.discoverItems.isEmpty
    }

    private var shouldShowEmptyState: Bool {
        viewModel.discoverItems.isEmpty &&
        viewModel.hasLoadedData &&
        !shouldShowLoadingState
    }

    // MARK: - Action Handlers

    /// 处理话题选择（从卡片话题标签点击）
    private func handleTopicSelection(_ topicIds: Set<String>) {
        // 同步到ViewModel并触发数据重新加载
        viewModel.updateSelectedTopics(topicIds, force: true)
    }

    /// 处理卡片点击（进入详情页）
    private func handleCardTapped(_ item: DiscoverItem) {
        // 标记即将导航到详情页
        isNavigatingToDetail = true
        // 调用原始的卡片点击处理
        onCardTapped(item)
    }

    private var shouldShowLoadingState: Bool {
        viewModel.isTopicRefreshing || viewModel.isInitializing
    }




    private var skeletonItems: [DiscoverItem] {
        (0..<12).map { _ in
            DiscoverItem(
                imageUrls: [""],
                title: "",
                username: "",
                userAvatarUrl: "",
                likes: 0,
                description: "",
                location: ""
            )
        }
    }
    @ViewBuilder
    private var contentView: some View {
        if let error = viewModel.error {
            ErrorView(error: error) {
                Task { await viewModel.refreshData() }
            }
        } else if shouldShowEmptyState {
            DiscoverEmptyStateView()
        } else {
            masonryContent
        }
    }
    private var masonryContent: some View {
        ScrollViewReader { proxy in
            LazyMasonryStack(
                viewModel.discoverItems.isEmpty ? skeletonItems : viewModel.discoverItems,
                columns: DiscoverConstants.Layout.columns,
                spacing: DiscoverConstants.Layout.itemSpacing,
                bottomTriggerThreshold: 0.8,
                debounceInterval: 1.0
            ) { item in
                cardView(for: item)
                    .id(item.id) // 确保每个item有稳定的ID
            }
            .onReachBottom {
                handleLoadMore()
            }
            .footer {
                // 使用 SwiftUIMasonryLayouts v1.1.0 的原生 footer API
                footerContentView
            }
            .refreshable {
                await handleRefresh()
            }
            // 移除onAppear图片预加载，避免从详情页返回时的闪动
            .padding(.horizontal)
            .padding(.top, 8)
            .onChange(of: viewModel.scrollToTopTrigger) { _, _ in
                // 滚动到第一个元素，如果没有元素则不执行滚动
                if let firstItem = viewModel.discoverItems.first {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        proxy.scrollTo(firstItem.id, anchor: .top)
                    }
                }
            }
            // 简化处理 - 依赖SwiftUI原生滚动位置保持
            .onAppear {
                // 重置导航标记
                isNavigatingToDetail = false
            }
        }
    }
    

    @ViewBuilder
    private func cardView(for item: DiscoverItem) -> some View {
        if viewModel.discoverItems.isEmpty {
            DiscoverCardSkeletonView()
                .id("skeleton-\(item.id)")
        } else {
            DiscoverCardView(
                item: item,
                selectedTopicIds: $selectedTopicIds,
                onTopicSelected: handleTopicSelection,
                onCardTapped: handleCardTapped
            )
        }
    }
    
    private var topicRefreshIndicator: some View {
        HStack {
            Spacer()

            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
                .scaleEffect(1.2)

            Spacer()
        }
        .padding(.vertical, 16)
    }
    @ViewBuilder
    private var footerContentView: some View {
        if !viewModel.discoverItems.isEmpty {
            TabBarAwareContainer {
                VStack(spacing: 0) {
                    PaginationFooterView.standard(
                        isLoadingMore: viewModel.isLoading,
                        hasMoreData: viewModel.hasMoreData,
                        hasData: !viewModel.discoverItems.isEmpty
                    )

                    Color.clear.frame(height: DiscoverConstants.Layout.contentInset.bottom)
                }
            }
        }
    }
    private func handleRefresh() async {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        await viewModel.refreshData(isManual: true)
    }

    private func handleLoadMore() {
        guard !viewModel.discoverItems.isEmpty else { return }
        Task {
            await viewModel.loadMoreData()
        }
    }
    /// 智能图片预加载 - 基于可见区域优化
    private func scheduleImagePreloading() {
        // 预加载可见区域的图片，提升用户体验
        let imageUrls = viewModel.discoverItems.prefix(DiscoverConstants.Performance.imagePreloadCount).compactMap { $0.imageUrls.first }
        Task {
            await ImagePreloadService.shared.preloadImages(
                urls: imageUrls,
                priority: .background // 降低优先级，避免影响UI响应
            )
        }
    }

    // MARK: - 滚动位置管理
    // 简化处理 - 完全依赖SwiftUI原生滚动位置保持机制
}

// MARK: - Supporting Views

/// 错误视图
private struct ErrorView: View {
    let error: Error
    let retryAction: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.red)
            Text(error.localizedDescription)
                .font(.headline)
                .multilineTextAlignment(.center)
            Button("重试") {
                retryAction()
            }
            .buttonStyle(.bordered)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 100)
    }
}

/// 发现页空状态视图
private struct DiscoverEmptyStateView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "photo.on.rectangle")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            Text("暂无发现内容")
                .font(.headline)
                .foregroundColor(.primary)
            Text("换个话题或目的地试试吧")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 100)
    }
}

// MARK: - Preview

#Preview {
    DiscoverContentView(
        viewModel: DiscoverViewModel(),
        isTopicDrawerShowing: .constant(false),
        selectedTopicIds: .constant([]),
        onCardTapped: { _ in }
    )
}
