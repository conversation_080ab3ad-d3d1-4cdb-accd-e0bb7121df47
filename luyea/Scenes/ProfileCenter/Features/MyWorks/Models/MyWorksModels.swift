import Foundation

/// MyWorks模块专用数据模型集合
///
/// 专门为"我的作品"功能模块设计的数据模型，独立于个人中心的公用模型。
/// 专注于作品管理、编辑、发布等核心功能需求。
///
/// 功能特性:
/// - 作品管理专用的数据结构
/// - 支持作品状态管理和编辑历史
/// - 提供创作者友好的数据接口
/// - 支持Codable序列化和本地存储
///
/// 架构职责:
/// - 📝 作品数据：定义作品的核心属性和元数据
/// - 🔄 状态管理：支持草稿、发布、隐藏等状态
/// - 📊 统计信息：提供作品相关的统计数据
/// - ✏️ 编辑支持：支持作品的版本管理和编辑历史
///
/// - Note: 所有模型都支持Codable，便于数据持久化和网络传输
/// - Warning: 修改模型结构时需要考虑数据迁移和向后兼容性
enum MyWorksModels {
    
    // MARK: - Enums

    enum WorkStatus: String, Codable, CaseIterable {
        case draft = "draft"
        case published = "published"
        case hidden = "hidden"
        case deleted = "deleted"
        
        var displayName: String {
            switch self {
            case .draft: return "草稿"
            case .published: return "已发布"
            case .hidden: return "已隐藏"
            case .deleted: return "已删除"
            }
        }
        
        var color: String {
            switch self {
            case .draft: return "orange"
            case .published: return "green"
            case .hidden: return "gray"
            case .deleted: return "red"
            }
        }
    }



    enum WorksSortType: String, Codable, CaseIterable {
        case createTime = "create_time"
        case viewCount = "view_count"
        case likeCount = "like_count"
        case forkCount = "fork_count"

        var displayName: String {
            switch self {
            case .createTime: return "创建时间"
            case .viewCount: return "浏览量"
            case .likeCount: return "喜欢数"
            case .forkCount: return "Fork数"
            }
        }

        var icon: String {
            switch self {
            case .createTime: return "clock"
            case .viewCount: return "eye"
            case .likeCount: return "heart"
            case .forkCount: return "arrow.triangle.branch"
            }
        }
    }

    // MARK: - Data Models

    /// 话题统计数据模型
    struct TopicStats: Identifiable, Codable, Equatable {
        let id: String
        let name: String
        let worksCount: Int

        static let allTopics = TopicStats(
            id: "all",
            name: "全部",
            worksCount: 0
        )
    }

    /// 作品统计数据模型
    struct WorksStats: Codable, Equatable {
        let totalWorks: Int
        let publishedWorks: Int
        let draftWorks: Int
        let totalViews: Int
        let totalLikes: Int
        let totalComments: Int
        let totalForks: Int
        
        static let empty = WorksStats(
            totalWorks: 0,
            publishedWorks: 0,
            draftWorks: 0,
            totalViews: 0,
            totalLikes: 0,
            totalComments: 0,
            totalForks: 0
        )
    }

    /// 我的作品数据模型
    struct WorkItem: Identifiable, Codable, Equatable {
        let id: String
        let title: String
        let description: String
        let coverImageUrl: String?
        let mediaUrls: [String]
        let topic: Topic?
        let location: String?
        let status: WorkStatus
        let createdAt: Date
        let updatedAt: Date
        let publishedAt: Date?
        let viewCount: Int
        let likeCount: Int
        let commentCount: Int
        let forkCount: Int
        let isFeatured: Bool
        let allowFork: Bool
        let editCount: Int
        let lastEditorId: String?

        init(
            id: String = UUID().uuidString,
            title: String,
            description: String,
            coverImageUrl: String? = nil,
            mediaUrls: [String] = [],
            topic: Topic? = nil,
            location: String? = nil,
            status: WorkStatus = .draft,
            createdAt: Date = Date(),
            updatedAt: Date = Date(),
            publishedAt: Date? = nil,
            viewCount: Int = 0,
            likeCount: Int = 0,
            commentCount: Int = 0,
            forkCount: Int = 0,
            isFeatured: Bool = false,
            allowFork: Bool = true,
            editCount: Int = 0,
            lastEditorId: String? = nil
        ) {
            self.id = id
            self.title = title
            self.description = description
            self.coverImageUrl = coverImageUrl
            self.mediaUrls = mediaUrls
            self.topic = topic
            self.location = location
            self.status = status
            self.createdAt = createdAt
            self.updatedAt = updatedAt
            self.publishedAt = publishedAt
            self.viewCount = viewCount
            self.likeCount = likeCount
            self.commentCount = commentCount
            self.forkCount = forkCount
            self.isFeatured = isFeatured
            self.allowFork = allowFork
            self.editCount = editCount
            self.lastEditorId = lastEditorId
        }
    }

    /// 作品列表查询参数
    struct WorksQueryParams {
        let paginationRequest: PaginationRequest
        let keyword: String?
        let topicId: String?
        let status: WorkStatus?

        init(
            page: Int = 1,
            pageSize: Int = 20,
            keyword: String? = nil,
            topicId: String? = nil,
            sortBy: String? = nil,
            status: WorkStatus? = nil
        ) {
            self.paginationRequest = PaginationRequest(
                page: page,
                pageSize: pageSize,
                sortBy: sortBy,
                sortOrder: .descending
            )
            self.keyword = keyword?.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty == true ? nil : keyword
            self.topicId = topicId
            self.status = status
        }

        var page: Int { paginationRequest.page }
        var pageSize: Int { paginationRequest.pageSize }
        var sortBy: String? { paginationRequest.sortBy }
    }

    /// 作品分页响应类型别名
    typealias WorksPageResponse = PaginationResponse<WorkItem>

    /// 统计数据响应
    struct WorksStatsResponse: Codable {
        let stats: WorksStats

        init(stats: WorksStats = .empty) {
            self.stats = stats
        }
    }
}
