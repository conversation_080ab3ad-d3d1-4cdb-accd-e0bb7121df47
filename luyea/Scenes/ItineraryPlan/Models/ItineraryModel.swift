import Foundation

/// ItineraryModel（行程）模型
/// 用于表示用户的单个行程计划，包含目的地、时间、状态等信息。
/// - Parameters:
///   - id: 行程唯一标识符
///   - title: 行程标题
///   - dateRange: 行程日期范围
///   - destination: 目的地
///   - coverImage: 封面图片URL
///   - status: 行程状态
///   - estimatedCost: 预计花费
///   - distance: 跨越里程
///   - trafficForecast: 流量预测
struct ItineraryModel: Identifiable, Equatable, Codable {
    let id: String                // 行程唯一标识符
    let title: String             // 行程标题
    let dateRange: String         // 行程日期范围
    let destination: String       // 目的地
    let coverImage: String?       // 封面图片URL
    let status: String            // 行程状态
    let estimatedCost: Double     // 预计花费（元）
    let distance: Double          // 跨越里程（公里）
    let trafficForecast: String   // 景区流量预测，如"高峰""中等""低"或百分比
    var trafficFeedback: String?   // 用户反馈的流量峰值（如有）
    let isRoundTrip: Bool?        // 是否为往返行程
}

// MARK: - Computed Properties
extension ItineraryModel {
    /// 从 dateRange 计算行程天数
    var days: Int {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy.MM.dd"
        let parts = dateRange.components(separatedBy: " - ")
        guard parts.count == 2,
              let start = formatter.date(from: parts[0].trimmingCharacters(in: .whitespaces)),
              let end = formatter.date(from: parts[1].trimmingCharacters(in: .whitespaces)) else { return 1 }
        let days = Calendar.current.dateComponents([.day], from: start, to: end).day ?? 0
        return max(days + 1, 1)
    }
} 