import SwiftUI

/// Fieldset 输入框组件 - 简化版 HTML fieldset + legend 效果
/// 
/// 特性：
/// - 单色边框设计，简洁清晰
/// - 标签嵌入边框顶部缺口，实现 legend 效果
/// - 支持多种状态：普通、聚焦、成功、错误
/// - 统一的输入框视觉风格
struct FieldsetInputView<Content: View>: View {
    
    // MARK: - Properties
    
    private let content: Content
    private let cornerRadius: CGFloat
    private let borderColor: Color
    private let borderWidth: CGFloat
    private let labelText: String?
    private let labelColor: Color
    private let backgroundColor: Color
    private let borderGradient: LinearGradient?
    
    // MARK: - Initializer
    
    init(
        cornerRadius: CGFloat = 12,
        borderColor: Color = Color(.systemGray4),
        borderWidth: CGFloat = 1,
        labelText: String? = nil,
        labelColor: Color = .secondary,
        backgroundColor: Color = Color(.systemBackground),
        borderGradient: LinearGradient? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.cornerRadius = cornerRadius
        self.borderColor = borderColor
        self.borderWidth = borderWidth
        self.labelText = labelText
        self.labelColor = labelColor
        self.backgroundColor = backgroundColor
        self.borderGradient = borderGradient
        self.content = content()
    }
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(backgroundColor)
            
            // 边框（带标签缺口）
            FieldsetBorderShape(
                cornerRadius: cornerRadius,
                labelText: labelText
            )
            .stroke(
                borderGradient != nil ? 
                    AnyShapeStyle(borderGradient!) : 
                    AnyShapeStyle(borderColor),
                lineWidth: borderWidth
            )
            
            // 内容
            content
        }
        .overlay(
            // 标签（使用 overlay 定位）
            labelOverlay,
            alignment: .topLeading
        )
    }
    
    // MARK: - Private Views
    
    @ViewBuilder
    private var labelOverlay: some View {
        if let labelText = labelText, !labelText.isEmpty {
            Text(labelText)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(labelColor)
                .padding(.horizontal, 4)
                .offset(x: 16, y: -6)
        }
    }
    
    /// 计算标签宽度（与 FieldsetBorderShape 中的方法一致）
    private func calculateLabelWidth(for text: String) -> CGFloat {
        let font = UIFont.systemFont(ofSize: 12, weight: .medium)
        let attributes = [NSAttributedString.Key.font: font]
        let size = (text as NSString).size(withAttributes: attributes)
        return size.width
    }
}

// MARK: - Fieldset Border Shape

/// Fieldset 边框形状 - 创建带缺口的边框效果
private struct FieldsetBorderShape: Shape {
    let cornerRadius: CGFloat
    let labelText: String?
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        if let labelText = labelText, !labelText.isEmpty {
            // 有标签时，创建带缺口的边框
            let labelWidth = calculateLabelWidth(for: labelText)
            let labelOffsetX: CGFloat = 16 // 标签容器的偏移位置（与 labelView 中的 offset 一致）
            let labelPadding: CGFloat = 4 // 标签内部的 padding（与 labelView 中的 padding 一致）
            
            // 缺口需要包含整个标签容器（包括 padding）
            let gapStart = labelOffsetX
            let gapEnd = min(labelOffsetX + (labelPadding * 2) + labelWidth, rect.width - cornerRadius - 8)
            
            // 绘制带缺口的边框
            drawBorderWithGap(
                in: rect,
                path: &path,
                gapStart: gapStart,
                gapEnd: gapEnd
            )
        } else {
            // 无标签时，绘制普通圆角矩形边框
            path.addRoundedRect(
                in: rect,
                cornerSize: CGSize(width: cornerRadius, height: cornerRadius)
            )
        }
        
        return path
    }
    
    /// 绘制带缺口的边框 - 确保缺口处无边框线
    private func drawBorderWithGap(
        in rect: CGRect,
        path: inout Path,
        gapStart: CGFloat,
        gapEnd: CGFloat
    ) {
        // 第一段路径：左边 + 底边 + 右边 + 顶边右段
        path.move(to: CGPoint(x: 0, y: cornerRadius))
        
        // 左边
        path.addLine(to: CGPoint(x: 0, y: rect.height - cornerRadius))
        
        // 左下角
        path.addQuadCurve(
            to: CGPoint(x: cornerRadius, y: rect.height),
            control: CGPoint(x: 0, y: rect.height)
        )
        
        // 底边
        path.addLine(to: CGPoint(x: rect.width - cornerRadius, y: rect.height))
        
        // 右下角
        path.addQuadCurve(
            to: CGPoint(x: rect.width, y: rect.height - cornerRadius),
            control: CGPoint(x: rect.width, y: rect.height)
        )
        
        // 右边
        path.addLine(to: CGPoint(x: rect.width, y: cornerRadius))
        
        // 右上角
        path.addQuadCurve(
            to: CGPoint(x: rect.width - cornerRadius, y: 0),
            control: CGPoint(x: rect.width, y: 0)
        )
        
        // 顶边右段（从右上角到缺口右端，但不包含缺口）
        path.addLine(to: CGPoint(x: gapEnd, y: 0))
        
        // 第二段路径：顶边左段 + 左上角（与第一段不连接）
        path.move(to: CGPoint(x: gapStart, y: 0))
        
        // 顶边左段（从缺口左端到左上角）
        path.addLine(to: CGPoint(x: cornerRadius, y: 0))
        
        // 左上角
        path.addQuadCurve(
            to: CGPoint(x: 0, y: cornerRadius),
            control: CGPoint(x: 0, y: 0)
        )
        
        // 注意：这里不连接回第一段，保持缺口处无边框线
    }
    
    /// 计算标签宽度
    private func calculateLabelWidth(for text: String) -> CGFloat {
        let font = UIFont.systemFont(ofSize: 12, weight: .medium)
        let attributes = [NSAttributedString.Key.font: font]
        let size = (text as NSString).size(withAttributes: attributes)
        return size.width
    }
}

// MARK: - Preview

#Preview("Fieldset 输入框组件 - HTML fieldset + legend 效果") {
    VStack(spacing: 24) {
        // 普通状态
        FieldsetInputView {
            HStack {
                Image(systemName: "phone")
                    .foregroundColor(.gray)
                TextField("请输入手机号", text: .constant(""))
                Spacer()
            }
            .padding()
        }
        
        // 聚焦状态
        FieldsetInputView(
            borderColor: .blue,
            borderWidth: 2
        ) {
            HStack {
                Image(systemName: "phone")
                    .foregroundColor(.blue)
                TextField("请输入手机号", text: .constant("138"))
                Spacer()
            }
            .padding()
        }
        
        // 成功状态
        FieldsetInputView(
            borderColor: .green,
            borderWidth: 2
        ) {
            HStack {
                Image(systemName: "phone")
                    .foregroundColor(.green)
                TextField("请输入手机号", text: .constant("13812345678"))
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
            }
            .padding()
        }
        
        // 错误状态 - 标签嵌入边框缺口中
        FieldsetInputView(
            borderColor: .red,
            borderWidth: 2,
            labelText: "请输入正确的手机号码",
            labelColor: .red
        ) {
            HStack {
                Image(systemName: "phone")
                    .foregroundColor(.red)
                TextField("请输入手机号", text: .constant("123"))
                Button(action: {}) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.red)
                }
            }
            .padding()
        }
        
        // 状态标签 - 展示 fieldset + legend 效果
        FieldsetInputView(
            borderColor: .orange,
            borderWidth: 2,
            labelText: "发送中...",
            labelColor: .orange
        ) {
            HStack {
                Image(systemName: "lock")
                    .foregroundColor(.orange)
                TextField("请输入验证码", text: .constant(""))
                Button("获取验证码") {}
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange)
                    .foregroundColor(.white)
                    .cornerRadius(6)
            }
            .padding()
        }
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}