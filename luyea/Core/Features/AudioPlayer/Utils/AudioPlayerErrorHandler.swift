import Foundation
import SwiftUI

/// 音频播放器错误处理器
///
/// 简化的错误处理器，提供基本的错误处理和日志记录
@MainActor
final class AudioPlayerErrorHandler: ObservableObject {

    // MARK: - Published Properties

    /// 当前错误状态
    @Published private(set) var currentError: AudioPlayerError?

    /// 是否显示错误提示
    @Published var showErrorAlert: Bool = false

    // MARK: - Private Properties

    /// 播放器管理器的弱引用
    private weak var playerManager: AudioPlayerManager?

    // MARK: - Initialization

    /// 初始化错误处理器
    init() {
    }

    // MARK: - Public Methods

    /// 设置播放器管理器
    /// - Parameter playerManager: 播放器管理器实例
    func setPlayerManager(_ playerManager: AudioPlayerManager) {
        self.playerManager = playerManager
    }

    /// 处理错误
    /// - Parameter error: 要处理的错误
    func handleError(_ error: AudioPlayerError) {
        Log.error("❌ 播放器错误: \(error.localizedDescription)")

        // 记录错误
        currentError = error

        // 尝试简单的自动恢复
        if error.isRecoverable {
            attemptSimpleRecovery(for: error)
        } else {
            // 显示用户错误提示
            showUserError(error)
        }
    }

    /// 清除当前错误
    func clearCurrentError() {
        currentError = nil
        showErrorAlert = false
    }

    /// 手动重试
    func retryRecovery() {
        guard let error = currentError else { return }

        attemptSimpleRecovery(for: error)
    }

    // MARK: - Private Methods

    /// 尝试简单的错误恢复
    /// - Parameter error: 要恢复的错误
    private func attemptSimpleRecovery(for error: AudioPlayerError) {
        guard let playerManager = playerManager else {
            showUserError(error)
            return
        }

        switch error.category {
        case .playerState:
            // 重置播放器状态
            if playerManager.isPlaying {
                playerManager.togglePlayPause()
            }
            if playerManager.playerState == .expanded {
                playerManager.collapsePlayer()
            }
            clearCurrentError()

        case .position:
            // 重置位置 - 隐藏并重新显示播放器
            playerManager.hidePlayer()
            clearCurrentError()

        case .settings:
            // 设置相关错误 - 简单清除错误
            clearCurrentError()

        default:
            // 其他错误显示给用户
            showUserError(error)
        }
    }

    /// 显示用户错误提示
    /// - Parameter error: 错误实例
    private func showUserError(_ error: AudioPlayerError) {
        currentError = error
        showErrorAlert = true
    }
}