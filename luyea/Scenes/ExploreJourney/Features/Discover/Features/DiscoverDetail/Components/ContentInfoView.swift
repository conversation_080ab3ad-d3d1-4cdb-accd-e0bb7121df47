import SwiftUI

struct ContentInfoView: View {
    let title: String
    let description: String
    let location: String
    let tags: [String]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            // 描述
            Text(description)
                .font(.body)
                .foregroundColor(.secondary)
                .lineLimit(nil)
            
            // 位置信息
            if !location.isEmpty {
                HStack(spacing: 6) {
                    Image(systemName: "location")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(location)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 标签
            if !tags.isEmpty {
                TagsView(tags: tags)
            }
        }
        .padding(.horizontal, 16)
    }
}

struct TagsView: View {
    let tags: [String]
    
    var body: some View {
        LazyVGrid(columns: [
            GridItem(.adaptive(minimum: 80), spacing: 8)
        ], spacing: 8) {
            ForEach(tags, id: \.self) { tag in
                Text(tag)
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.blue.opacity(0.1))
                    )
                    .foregroundColor(.blue)
            }
        }
    }
}

// MARK: - 预览
#Preview {
    ScrollView {
        VStack(spacing: 20) {
            ContentInfoView(
                title: "美丽的日落风景",
                description: "今天在海边看到了非常美丽的日落，天空被染成了橙红色，海浪轻柔地拍打着岸边，整个场景让人心旷神怡。这样的美景让人忘记了一天的疲劳，只想静静地欣赏大自然的馈赠。",
                location: "青岛·栈桥",
                tags: ["旅行", "摄影", "美食", "日落", "海边"]
            )
            
            ContentInfoView(
                title: "简单标题",
                description: "简短描述",
                location: "",
                tags: []
            )
        }
    }
}
