import SwiftUI

// MARK: - Authentication Aware Modifier

/// 根据认证状态显示不同内容的修饰符
struct AuthenticationAwareModifier<AuthenticatedContent: View, UnauthenticatedContent: View>: ViewModifier {

    @Environment(\.authenticationManager) private var authManager
    @State private var authState: AuthenticationModels.AuthState = .unauthenticated

    let authenticatedContent: () -> AuthenticatedContent
    let unauthenticatedContent: () -> UnauthenticatedContent
    let showLoadingState: Bool

    init(
        showLoadingState: Bool = false,
        @ViewBuilder authenticated: @escaping () -> AuthenticatedContent,
        @ViewBuilder unauthenticated: @escaping () -> UnauthenticatedContent
    ) {
        self.showLoadingState = showLoadingState
        self.authenticatedContent = authenticated
        self.unauthenticatedContent = unauthenticated
    }

    func body(content: Content) -> some View {
        Group {
            if showLoadingState && isAuthenticating {
                // 认证状态检查中
                ProgressView("检查登录状态...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if isAuthenticated {
                // 已登录状态
                authenticatedContent()
            } else {
                // 未登录状态
                unauthenticatedContent()
            }
        }
        .onAppear {
            // 安全地同步初始状态
            authState = authManager.authenticationState
        }
        .onReceive(authManager.$authenticationState) { newState in
            // 确保状态更新在主线程
            DispatchQueue.main.async {
                authState = newState
            }
        }
    }

    private var isAuthenticated: Bool {
        if case .authenticated = authState {
            return true
        }
        return false
    }

    private var isAuthenticating: Bool {
        if case .authenticating = authState {
            return true
        }
        return authManager.isProcessing
    }
}

// MARK: - View Extension

extension View {
    /// 根据认证状态显示不同内容
    /// - Parameters:
    ///   - showLoadingState: 是否显示加载状态，默认false
    ///   - authenticated: 登录后显示的内容
    ///   - unauthenticated: 登录前显示的内容
    func authenticationAware<AuthenticatedContent: View, UnauthenticatedContent: View>(
        showLoadingState: Bool = false,
        @ViewBuilder authenticated: @escaping () -> AuthenticatedContent,
        @ViewBuilder unauthenticated: @escaping () -> UnauthenticatedContent
    ) -> some View {
        self.modifier(
            AuthenticationAwareModifier(
                showLoadingState: showLoadingState,
                authenticated: authenticated,
                unauthenticated: unauthenticated
            )
        )
    }
}

// MARK: - Authentication State Container

/// 认证状态容器组件，提供更灵活的状态管理
struct AuthenticationStateContainer<AuthenticatedContent: View, UnauthenticatedContent: View, LoadingContent: View>: View {

    @Environment(\.authenticationManager) private var authManager
    @State private var authState: AuthenticationModels.AuthState = .unauthenticated

    let authenticatedContent: () -> AuthenticatedContent
    let unauthenticatedContent: () -> UnauthenticatedContent
    let loadingContent: () -> LoadingContent

    init(
        @ViewBuilder authenticated: @escaping () -> AuthenticatedContent,
        @ViewBuilder unauthenticated: @escaping () -> UnauthenticatedContent,
        @ViewBuilder loading: @escaping () -> LoadingContent = { ProgressView() }
    ) {
        self.authenticatedContent = authenticated
        self.unauthenticatedContent = unauthenticated
        self.loadingContent = loading
    }

    var body: some View {
        Group {
            if isAuthenticating {
                loadingContent()
            } else if isAuthenticated {
                authenticatedContent()
            } else {
                unauthenticatedContent()
            }
        }
        .onAppear {
            // 安全地同步初始状态
            authState = authManager.authenticationState
        }
        .onReceive(authManager.$authenticationState) { newState in
            // 确保状态更新在主线程
            DispatchQueue.main.async {
                authState = newState
            }
        }
    }

    private var isAuthenticated: Bool {
        if case .authenticated = authState {
            return true
        }
        return false
    }

    private var isAuthenticating: Bool {
        if case .authenticating = authState {
            return true
        }
        return authManager.isProcessing
    }
}

// MARK: - Conditional Authentication View

/// 条件认证视图，只在特定认证状态下显示内容
struct ConditionalAuthView<Content: View>: View {
    
    @Environment(\.authenticationManager) private var authManager
    
    let requiresAuthentication: Bool
    let content: () -> Content
    let fallback: (() -> AnyView)?
    
    init(
        requiresAuthentication: Bool = true,
        fallback: (() -> AnyView)? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.requiresAuthentication = requiresAuthentication
        self.content = content
        self.fallback = fallback
    }
    
    var body: some View {
        Group {
            if shouldShowContent {
                content()
            } else if let fallback = fallback {
                fallback()
            } else {
                EmptyView()
            }
        }
    }
    
    private var shouldShowContent: Bool {
        if requiresAuthentication {
            return authManager.isAuthenticated
        } else {
            return !authManager.isAuthenticated
        }
    }
}

// MARK: - Authentication-Dependent Text

/// 根据认证状态显示不同文本的组件
struct AuthDependentText: View {
    
    @Environment(\.authenticationManager) private var authManager
    
    let authenticatedText: String
    let unauthenticatedText: String
    let style: Font?
    
    init(
        authenticated: String,
        unauthenticated: String,
        style: Font? = nil
    ) {
        self.authenticatedText = authenticated
        self.unauthenticatedText = unauthenticated
        self.style = style
    }
    
    var body: some View {
        Text(authManager.isAuthenticated ? authenticatedText : unauthenticatedText)
            .font(style)
    }
}

// MARK: - User Info Display

/// 用户信息显示组件
struct UserInfoDisplay: View {
    
    @Environment(\.authenticationManager) private var authManager
    
    let showAvatar: Bool
    let showName: Bool
    let placeholder: String
    
    init(
        showAvatar: Bool = true,
        showName: Bool = true,
        placeholder: String = "未登录用户"
    ) {
        self.showAvatar = showAvatar
        self.showName = showName
        self.placeholder = placeholder
    }
    
    var body: some View {
        HStack {
            if showAvatar {
                Circle()
                    .fill(authManager.isAuthenticated ? Color.blue : Color.gray)
                    .frame(width: 32, height: 32)
                    .overlay(
                        Text(authManager.isAuthenticated ? 
                             String(authManager.currentUser?.displayName.prefix(1) ?? "?") : "?")
                            .foregroundColor(.white)
                            .font(.system(size: 14, weight: .medium))
                    )
            }
            
            if showName {
                Text(authManager.isAuthenticated ? 
                     authManager.currentUser?.displayName ?? "用户" : placeholder)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(authManager.isAuthenticated ? .primary : .secondary)
            }
        }
    }
}
