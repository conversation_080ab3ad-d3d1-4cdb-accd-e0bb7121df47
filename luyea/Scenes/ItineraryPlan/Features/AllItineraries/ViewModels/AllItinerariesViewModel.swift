import SwiftUI
import Combine

/// 全部行程页面的ViewModel
/// 负责管理分页加载、下拉刷新、上拉加载等功能
@MainActor
final class AllItinerariesViewModel: ObservableObject {
    
    // MARK: - Published Properties

    /// 行程列表数据（当前筛选状态下的数据）
    @Published var filteredItineraries: [ItineraryModel] = []

    /// 当前选中的状态筛选
    @Published var selectedStatus: String = "全部"

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 是否正在刷新
    @Published var isRefreshing: Bool = false

    /// 是否正在加载更多
    @Published var isLoadingMore: Bool = false

    /// 是否还有更多数据
    @Published var hasMoreData: Bool = true

    /// 错误信息
    @Published var error: Error?

    /// 是否显示错误提示
    @Published var showErrorAlert: Bool = false

    /// 可用的状态筛选选项
    let statusOptions = ["全部", "在途中", "待出行", "已结束"]
    
    // MARK: - Private Properties
    
    /// 当前页码
    private var currentPage: Int = 1
    
    /// 每页数据量
    private let pageSize: Int = 10
    
    /// 全部行程服务
    private let allItinerariesService: AllItinerariesServiceProtocol
    
    /// Combine订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(allItinerariesService: AllItinerariesServiceProtocol = AllItinerariesService()) {
        self.allItinerariesService = allItinerariesService
        setupErrorHandling()
    }
    
    // MARK: - Public Methods
    
    /// 初始加载数据
    func loadInitialData() {
        guard !isLoading else { return }

        isLoading = true
        currentPage = 1
        hasMoreData = true

        Task {
            do {
                let response = try await allItinerariesService.fetchItineraries(
                    page: currentPage,
                    pageSize: pageSize,
                    status: selectedStatus == "全部" ? nil : selectedStatus
                )

                self.filteredItineraries = response.items
                self.hasMoreData = response.hasMore
                self.error = nil

            } catch {
                self.handleError(error)
            }

            isLoading = false
        }
    }
    
    /// 下拉刷新
    func refreshData() async {
        guard !isRefreshing else { return }

        isRefreshing = true
        currentPage = 1
        hasMoreData = true

        do {
            let response = try await allItinerariesService.refreshItineraries(
                status: selectedStatus == "全部" ? nil : selectedStatus
            )

            self.filteredItineraries = response.items
            self.hasMoreData = response.hasMore
            self.error = nil

        } catch {
            self.handleError(error)
        }

        isRefreshing = false
    }
    
    /// 加载更多数据
    func loadMoreData() {
        guard !isLoadingMore && hasMoreData && !isLoading else { return }

        isLoadingMore = true
        currentPage += 1

        Task {
            do {
                let response = try await allItinerariesService.fetchItineraries(
                    page: currentPage,
                    pageSize: pageSize,
                    status: selectedStatus == "全部" ? nil : selectedStatus
                )

                // 使用Service合并数据
                self.filteredItineraries = allItinerariesService.mergePageData(
                    existing: self.filteredItineraries,
                    newPage: response,
                    isRefresh: false
                )

                self.hasMoreData = response.hasMore
                self.error = nil

            } catch {
                self.handleError(error)
                self.currentPage -= 1 // 回退页码
            }

            isLoadingMore = false
        }
    }
    
    /// 检查是否需要加载更多（当滚动到接近底部时调用）
    func checkForLoadMore(currentItem: ItineraryModel) {
        let displayedItems = filteredItineraries
        guard displayedItems.count >= 3 else { return }

        if let itemIndex = displayedItems.firstIndex(where: { $0.id == currentItem.id }) {
            // 使用Service判断是否需要加载更多
            if allItinerariesService.shouldLoadMore(
                currentIndex: itemIndex,
                totalItems: displayedItems.count,
                threshold: 3
            ) {
                loadMoreData()
            }
        }
    }

    /// 更新状态筛选
    func updateStatusFilter(_ status: String) {
        selectedStatus = status
        // 状态改变时重新加载数据
        currentPage = 1
        hasMoreData = true

        Task {
            await refreshData()
        }
    }
    
    // MARK: - Private Methods


    // MARK: - Error Handling

    /// 设置错误处理
    private func setupErrorHandling() {
        // 监听错误状态变化
        $error
            .compactMap { $0 }
            .sink { [weak self] _ in
                self?.showErrorAlert = true
            }
            .store(in: &cancellables)
    }

    /// 处理错误
    private func handleError(_ error: Error) {
        // 如果是取消错误，不显示给用户
        if error is CancellationError {
            return
        }

        // 使用Service处理错误转换
        if let serviceError = error as? AllItinerariesError {
            self.error = serviceError
        } else {
            self.error = allItinerariesService.handleNetworkError(error)
        }

        self.isLoading = false
        self.isLoadingMore = false
    }
}
