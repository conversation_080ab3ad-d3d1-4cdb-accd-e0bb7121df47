//
//  luyeaApp.swift
//  luyea
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/11.
//

import SwiftUI

@main
struct luyeaApp: App {

    // MARK: - State Objects

    /// 音频播放器管理器
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(TabBarStateManager.shared)
                .environmentObject(audioPlayerManager)
                .environment(\.authenticationManager, AuthenticationManager.shared)

                .onAppear {
                    // 显示环境和日志配置信息
                    Log.showEnvironmentInfo()

                    // 应用界面出现时开始预加载LocationManager
                    LocationPreloadService.shared.startPreloading()

                    // 初始化音频播放器
                    initializeAudioPlayer()
                }
        }
    }

    // MARK: - Private Methods

    /// 初始化音频播放器
    private func initializeAudioPlayer() {
        // 音频播放器初始化（静默）
    }
}
