import SwiftUI

struct ThirdPartyLoginButton: View {
    let type: LoginType
    let isLoading: Bool
    let action: () -> Void
    
    enum LoginType: Hashable {
        case wechat, qq, apple

        var imageName: String? {
            switch self {
            case .wechat: return "wechat_login"
            case .qq: return "qq_login"
            case .apple: return nil
            }
        }

        var systemImageName: String? {
            switch self {
            case .apple: return "applelogo"
            default: return nil
            }
        }

        var backgroundColor: Color {
            switch self {
            case .apple: return .black
            default: return .clear
            }
        }
    }
    
    var body: some View {
        Button(action: action) {
            Group {
                if type == .apple {
                    // Apple 登录使用系统图标
                    ZStack {
                        Circle()
                            .fill(type.backgroundColor)
                            .frame(width: 50, height: 50)
                            .shadow(color: .black.opacity(0.15), radius: 3, x: 0, y: 2)
                        
                        Image(systemName: type.systemImageName!)
                            .font(.system(size: 24, weight: .medium))
                            .foregroundColor(.white)
                    }
                } else {
                    // 微信和 QQ 使用自定义图片
                    Image(type.imageName!)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 50, height: 50)
                }
            }
        }
        .disabled(isLoading)
        .opacity(isLoading ? 0.6 : 1.0)
        .scaleEffect(isLoading ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isLoading)
    }
}

// MARK: - Preview

#Preview("第三方登录按钮") {
    HStack(spacing: 20) {
        ThirdPartyLoginButton(type: .wechat, isLoading: false) {
            print("微信登录")
        }
        
        ThirdPartyLoginButton(type: .qq, isLoading: false) {
            print("QQ登录")
        }
        
        ThirdPartyLoginButton(type: .apple, isLoading: false) {
            print("Apple登录")
        }
        
        // 加载状态
        ThirdPartyLoginButton(type: .wechat, isLoading: true) {
            print("微信登录")
        }
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}