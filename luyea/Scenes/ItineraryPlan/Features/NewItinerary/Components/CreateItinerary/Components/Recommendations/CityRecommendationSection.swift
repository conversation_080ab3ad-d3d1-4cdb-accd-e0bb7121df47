import SwiftUI

/// 城市推荐区域组件
///
/// 功能特性：
/// - 包装城市推荐卡片
/// - 提供区域标题和布局
/// - 与已选择地点紧密关联的推荐功能
struct CityRecommendationSection: View {
    
    // MARK: - Properties

    @Binding var enableCityRecommendation: Bool
    @Binding var recommendationTypes: Set<RecommendationType>
    let selectedDestinations: [Destination]
    let selectedAttractions: [AttractionModel]
    let shouldForceEnable: Bool // 是否应该强制开启且不可关闭
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            HStack {
                Text("为我推荐")
                    .font(.title2.weight(.bold))
                    .foregroundColor(.primary)

                Text("可选")
                    .font(.caption.weight(.medium))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.secondary.opacity(0.1))
                    .clipShape(Capsule())

                Spacer()
            }
            
            // 城市推荐卡片
            CityRecommendationCard(
                enableRecommendation: $enableCityRecommendation,
                recommendationTypes: $recommendationTypes,
                selectedDestinations: selectedDestinations,
                selectedAttractions: selectedAttractions,
                shouldForceEnable: shouldForceEnable
            )
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        CityRecommendationSection(
            enableCityRecommendation: .constant(true),
            recommendationTypes: .constant([.attractions, .restaurants]),
            selectedDestinations: Destination.samples.filter { $0.type == .city }.prefix(2).map { $0 },
            selectedAttractions: AttractionModel.samples.prefix(1).map { $0 },
            shouldForceEnable: false
        )

        CityRecommendationSection(
            enableCityRecommendation: .constant(false),
            recommendationTypes: .constant([]),
            selectedDestinations: [],
            selectedAttractions: [],
            shouldForceEnable: false
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
