import SwiftUI

/// 快速日期选择视图
///
/// 提供常用日期的快速选择功能，包括今天、明天、周末和节假日。
/// 采用网格布局，支持动画效果和交互反馈。
struct QuickDateSelectionView: View {
    
    // MARK: - Properties
    
    @Binding var selectedDate: Date
    let onDateSelected: (() -> Void)?
    let onDismiss: () -> Void
    
    // MARK: - State
    
    @State private var quickOptions: [DateUtils.QuickDateOption] = []
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题区域
            titleSection
            
            // 快速选择网格
            quickSelectionGrid
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
        .onAppear {
            loadQuickOptions()
        }
    }
    
    // MARK: - Private Views
    
    /// 标题区域
    private var titleSection: some View {
        HStack {
            HStack(spacing: 8) {
                Image(systemName: "clock.fill")
                    .font(.title3)
                    .foregroundColor(.blue)
                
                Text("快速选择")
                    .font(.title2.weight(.semibold))
                    .foregroundColor(.primary)
            }
            
            Spacer()
            
            // 装饰性元素
            HStack(spacing: 4) {
                ForEach(0..<3, id: \.self) { _ in
                    Circle()
                        .fill(Color.blue.opacity(0.3))
                        .frame(width: 6, height: 6)
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 24)
        .padding(.bottom, 20)
    }
    
    /// 快速选择网格
    private var quickSelectionGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 12),
            GridItem(.flexible(), spacing: 12),
            GridItem(.flexible(), spacing: 12)
        ], spacing: 16) {
            ForEach(Array(quickOptions.enumerated()), id: \.element.title) { index, option in
                QuickDateButton(
                    option: option,
                    isSelected: DateUtils.isSameDay(selectedDate, option.date),
                    onTap: {
                        selectedDate = option.date
                        // 不要直接关闭弹窗，让用户通过确认按钮来确认选择
                    }
                )
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 24)
    }
    
    // MARK: - Private Methods
    
    /// 加载快速选择选项
    private func loadQuickOptions() {
        quickOptions = DateUtils.generateQuickDateOptions(count: 6)
    }
}

// MARK: - QuickDateButton

/// 快速日期选择按钮
private struct QuickDateButton: View {
    let option: DateUtils.QuickDateOption
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 6) {
                Text(option.title)
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(isSelected ? .white : .primary)
                    .lineLimit(1)
                
                Text(option.subtitle)
                    .font(.caption)
                    .foregroundColor(isSelected ? .white.opacity(0.9) : .secondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.default)
                    .fill(isSelected ? Color.blue : Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Preview

#Preview {
    QuickDateSelectionView(
        selectedDate: .constant(Date()),
        onDateSelected: nil,
        onDismiss: {}
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
