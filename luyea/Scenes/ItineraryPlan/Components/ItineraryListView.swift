import SwiftUI

/// 一个视图，用于以列表形式展示行程卡片。
///
/// 此视图使用 `LazyVStack` 来高效地渲染行程卡片列表，并包含一个可选的"查看全部"按钮，
/// 用于在行程总数超过当前显示数量时导航到完整列表。
struct ItineraryListView: View {

    // MARK: - Properties
    
    /// 当前要显示的行程数据模型数组。
    let itineraries: [ItineraryModel]
    
    /// 行程的总数，用于判断是否显示"查看全部"按钮。
    let totalItineraryCount: Int
    
    /// 绑定当前展开的行程卡片的 ID，用于控制卡片的展开和折叠状态。
    @Binding var expandedId: String?
    
    /// 绑定一个布尔值，当用户点击"查看全部"时，此值会变为 `true`。
    @Binding var showAll: Bool

    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 14) {
            itineraryCards
            
            if itineraries.count >= 3 {
                viewAllButton
            }
        }
    }
    
    // MARK: - Private Views
    
    /// 懒加载的行程卡片列表 - 性能优化版本
    private var itineraryCards: some View {
        LazyVStack(spacing: 14) {
            ForEach(Array(itineraries.enumerated()), id: \.element.id) { idx, itinerary in
                // 使用LazyView包装，减少初始渲染压力
                LazyView {
                    ItineraryCardView(
                        itinerary: itinerary,
                        isExpanded: expandedId == itinerary.id,
                        onTap: {
                            // 添加触觉反馈提升交互体验
                            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                            impactFeedback.impactOccurred()

                            handleTap(on: itinerary)
                        },
                        showInfoButton: idx == 0 // 仅第一张卡片显示信息按钮
                    )
                }
                .id(itinerary.id) // 明确指定ID优化重用
            }
        }
        .padding(.top, 4)
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: itineraries.count) // 只对数量变化做动画
        .onAppear {
            // 预加载可见卡片的图片，使用统一的预加载服务
            scheduleImagePreloading()
        }
    }
    
    /// "查看全部"按钮，仅在行程总数大于当前显示数量时出现。
    private var viewAllButton: some View {
        Button(action: { showAll = true }) {
            Text("查看全部行程")
                .font(.system(size: 13, weight: .regular, design: .rounded))
                .foregroundColor(.blue)
        }
        .padding(.top, 8)
    }
    
    // MARK: - Private Methods
    
    /// 处理行程卡片的点击事件，切换其展开/折叠状态。
    /// - Parameter itinerary: 被点击的行程模型。
    private func handleTap(on itinerary: ItineraryModel) {
        withAnimation(.spring(response: 0.45, dampingFraction: 0.85)) {
            expandedId = (expandedId == itinerary.id) ? nil : itinerary.id
        }
    }

    /// 统一的图片预加载策略
    private func scheduleImagePreloading() {
        let imagesToPreload = itineraries.compactMap { $0.coverImage }

        if !imagesToPreload.isEmpty {
            Task {
                await ImagePreloadService.shared.preloadImages(urls: imagesToPreload, priority: .background)
            }
        }
    }
}

// MARK: - Preview

#if DEBUG
struct ItineraryListView_Previews: PreviewProvider {
    
    private static let sampleItineraries: [ItineraryModel] = [
        ItineraryModel(id: "1", title: "夏日海岛之旅", dateRange: "2024.07.20 - 2024.07.27", destination: "海南三亚", coverImage: "https://example.com/image1.jpg", status: "在途中", estimatedCost: 8000, distance: 3000, trafficForecast: "高峰", trafficFeedback: nil, isRoundTrip: true),
        ItineraryModel(id: "2", title: "秋季山地徒步", dateRange: "2024.10.01 - 2024.10.03", destination: "黄山", coverImage: "https://example.com/image2.jpg", status: "待出行", estimatedCost: 2500, distance: 800, trafficForecast: "中等", trafficFeedback: nil, isRoundTrip: false)
    ]
    
    static var previews: some View {
        ScrollView {
            ItineraryListView(
                itineraries: sampleItineraries,
                totalItineraryCount: 5,
                expandedId: .constant("1"),
                showAll: .constant(false)
            )
            .padding()
        }
        .background(Color(.systemGroupedBackground))
        .previewDisplayName("Default List")
        
        ScrollView {
            ItineraryListView(
                itineraries: Array(sampleItineraries.prefix(1)),
                totalItineraryCount: 1,
                expandedId: .constant(nil),
                showAll: .constant(false)
            )
            .padding()
        }
        .background(Color(.systemGroupedBackground))
        .previewDisplayName("Single Item (No 'View All')")
    }
}
#endif 
