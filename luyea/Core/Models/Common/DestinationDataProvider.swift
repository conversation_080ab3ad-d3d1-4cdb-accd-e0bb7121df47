import Foundation

/// 目的地数据提供者
///
/// 负责提供目的地相关的静态数据和配置信息。
/// 采用静态属性方式，避免重复创建大量数据对象。
struct DestinationDataProvider {
    
    // MARK: - Additional Cities Data
    
    /// 额外的城市数据
    static let additionalCities: [Destination] = [
        Destination(
            id: "city_beijing",
            name: "北京",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1508804185872-d7badad00f7d",
            description: "中国的首都，历史文化名城"
        ),
        Destination(
            id: "city_hangzhou",
            name: "杭州",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
            description: "人间天堂，西湖美景"
        ),
        Destination(
            id: "city_tokyo",
            name: "东京",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1465101046530-73398c7f28ca",
            description: "日本首都，现代化都市"
        ),
        Destination(
            id: "city_paris",
            name: "巴黎",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1502602898657-3e91760cbb34",
            description: "浪漫之都，艺术之城"
        ),
        Destination(
            id: "city_guangzhou",
            name: "广州",
            type: .city,
            description: "南国商都，美食之城"
        ),
        Destination(
            id: "city_shenzhen",
            name: "深圳",
            type: .city,
            description: "科技之城，创新之都"
        ),
        Destination(
            id: "city_chengdu",
            name: "成都",
            type: .city,
            description: "天府之国，美食之都"
        ),
        Destination(
            id: "city_xian",
            name: "西安",
            type: .city,
            description: "古都长安，历史悠久"
        ),
        Destination(
            id: "city_nanjing",
            name: "南京",
            type: .city,
            description: "六朝古都，文化名城"
        ),
        Destination(
            id: "city_suzhou",
            name: "苏州",
            type: .city,
            description: "园林之城，江南水乡"
        )
    ]
    
    // MARK: - Popular Destinations
    
    /// 热门目的地
    static let popularDestinations: [Destination] = [
        Destination(
            id: "popular_shanghai",
            name: "上海",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2",
            description: "国际大都市，东方明珠"
        ),
        Destination(
            id: "popular_beijing",
            name: "北京",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1508804185872-d7badad00f7d",
            description: "首都北京，文化中心"
        ),
        Destination(
            id: "popular_guangzhou",
            name: "广州",
            type: .city,
            description: "南方门户，商贸中心"
        ),
        Destination(
            id: "popular_shenzhen",
            name: "深圳",
            type: .city,
            description: "创新之城，科技前沿"
        ),
        Destination(
            id: "popular_hangzhou",
            name: "杭州",
            type: .city,
            description: "人间天堂，电商之都"
        )
    ]
    
    // MARK: - International Destinations
    
    /// 国际目的地
    static let internationalDestinations: [Destination] = [
        Destination(
            id: "intl_tokyo",
            name: "东京",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1465101046530-73398c7f28ca",
            description: "日本首都，现代与传统并存"
        ),
        Destination(
            id: "intl_paris",
            name: "巴黎",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1502602898657-3e91760cbb34",
            description: "浪漫之都，时尚之城"
        ),
        Destination(
            id: "intl_london",
            name: "伦敦",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1513635269975-59663e0ac1ad",
            description: "英伦风情，历史悠久"
        ),
        Destination(
            id: "intl_newyork",
            name: "纽约",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9",
            description: "不夜之城，世界金融中心"
        ),
        Destination(
            id: "intl_seoul",
            name: "首尔",
            type: .city,
            description: "韩流文化，现代都市"
        ),
        Destination(
            id: "intl_singapore",
            name: "新加坡",
            type: .city,
            description: "花园城市，多元文化"
        )
    ]
    
    // MARK: - Helper Methods
    
    /// 获取所有城市目的地
    static func getAllCities() -> [Destination] {
        return Destination.samples + additionalCities + popularDestinations + internationalDestinations
    }
    
    /// 根据类型筛选目的地
    static func getDestinations(by type: DestinationType) -> [Destination] {
        return getAllCities().filter { $0.type == type }
    }
    
    /// 搜索目的地
    static func searchDestinations(query: String) -> [Destination] {
        guard !query.isEmpty else { return getAllCities() }
        
        return getAllCities().filter { destination in
            destination.name.localizedCaseInsensitiveContains(query) ||
            (destination.description?.localizedCaseInsensitiveContains(query) ?? false)
        }
    }
    
    /// 获取热门搜索关键词
    static let popularSearchKeywords: [String] = [
        "北京", "上海", "广州", "深圳", "杭州",
        "成都", "西安", "南京", "苏州", "重庆",
        "东京", "巴黎", "伦敦", "纽约", "首尔"
    ]
}
