import SwiftUI

/// 全局可复用的缩放按钮样式
public struct ScaleButtonStyle: ButtonStyle {
    var scale: CGFloat = 0.95
    var response: Double = 0.3
    var damping: Double = 0.7

    public func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? scale : 1)
            .animation(.spring(response: response, dampingFraction: damping), value: configuration.isPressed)
    }
} 