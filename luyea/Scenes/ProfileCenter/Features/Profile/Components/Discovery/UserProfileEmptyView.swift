import SwiftUI

/// 用户主页的空状态视图
struct UserProfileEmptyView: View {
    let selectedTag: String?
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            Text(emptyStateText)
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 60)
    }
    
    private var emptyStateText: String {
        selectedTag == nil ? "暂无发现" : "暂无\(selectedTag ?? "")相关发现"
    }
}

#Preview {
    UserProfileEmptyView(selectedTag: nil)
}

#Preview("有标签") {
    UserProfileEmptyView(selectedTag: "城市探索")
} 