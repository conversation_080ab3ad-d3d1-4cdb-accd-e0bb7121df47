import Foundation

/// 目的地数据模型
///
/// 表示旅游目的地的基本信息，支持层级结构（标签页 -> 国家 -> 省份 -> 城市）。
/// 用于目的地选择、搜索和展示功能。
struct Destination: Identifiable, Codable, Equatable, Hashable {
    // MARK: - Properties

    /// 目的地唯一标识符
    let id: String

    /// 目的地名称
    let name: String

    /// 目的地类型（标签页、国家、省份、城市）
    let type: DestinationType

    /// 父级目的地ID（用于构建层级关系）
    let parentId: String?

    /// 所属标签页ID（国内/国外）
    let tabId: String?

    /// 目的地封面图片URL
    let imageUrl: String?

    /// 目的地描述信息
    let description: String?

    /// 目的地坐标信息
    let coordinate: Coordinate?

    /// 热门程度（0-100，用于排序）
    let popularity: Int?



    /// 创建时间
    let createdAt: Date?

    /// 更新时间
    let updatedAt: Date?

    // MARK: - Computed Properties

    /// 是否为顶级目的地（无父级）
    var isTopLevel: Bool {
        parentId == nil
    }

    /// 是否为叶子节点（城市级别）
    var isLeafNode: Bool {
        type == .city
    }

    /// 显示名称（可能包含层级信息）
    var displayName: String {
        name
    }



    // MARK: - Initialization

    /// 完整初始化方法
    init(
        id: String,
        name: String,
        type: DestinationType,
        parentId: String? = nil,
        tabId: String? = nil,
        imageUrl: String? = nil,
        description: String? = nil,
        coordinate: Coordinate? = nil,
        popularity: Int? = nil,

        createdAt: Date? = nil,
        updatedAt: Date? = nil
    ) {
        self.id = id
        self.name = name
        self.type = type
        self.parentId = parentId
        self.tabId = tabId
        self.imageUrl = imageUrl
        self.description = description
        self.coordinate = coordinate
        self.popularity = popularity

        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // MARK: - Hashable

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    // MARK: - Equatable

    static func == (lhs: Destination, rhs: Destination) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - 目的地类型枚举

/// 目的地类型
///
/// 定义目的地的层级类型，用于构建目的地的层级结构。
enum DestinationType: String, Codable, CaseIterable {
    /// 标签页（国内/国外）
    case tab = "tab"
    /// 国家
    case country = "country"
    /// 省份/州
    case province = "province"
    /// 城市
    case city = "city"

    /// 类型显示名称
    var displayName: String {
        switch self {
        case .tab:
            return "标签页"
        case .country:
            return "国家"
        case .province:
            return "省份"
        case .city:
            return "城市"
        }
    }

    /// 层级深度（用于排序和展示）
    var level: Int {
        switch self {
        case .tab:
            return 0
        case .country:
            return 1
        case .province:
            return 2
        case .city:
            return 3
        }
    }
}

// MARK: - 坐标模型已统一
// 坐标相关定义已迁移到 Core/Models/Coordinate.swift
// 使用统一的 Coordinate 结构体

// MARK: - 扩展方法

extension Destination {
    /// 创建示例数据（用于预览和测试）
    static let samples: [Destination] = [
        // 标签页
        Destination(
            id: "tab_domestic",
            name: "国内",
            type: .tab
        ),
        Destination(
            id: "tab_international",
            name: "国外",
            type: .tab
        ),

        // 国内热门城市
        Destination(
            id: "city_shanghai",
            name: "上海",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2",
            description: "国际化大都市，中国的经济金融中心",
            coordinate: Coordinate(latitude: 31.2304, longitude: 121.4737),
            popularity: 98
        ),
        Destination(
            id: "city_beijing",
            name: "北京",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1508804185872-d7badad00f7d",
            description: "中国的首都，历史文化名城",
            coordinate: Coordinate(latitude: 39.9042, longitude: 116.4074),
            popularity: 97
        ),
        Destination(
            id: "city_hangzhou",
            name: "杭州",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
            description: "人间天堂，西湖美景",
            coordinate: Coordinate(latitude: 30.2741, longitude: 120.1551),
            popularity: 92
        ),
        Destination(
            id: "city_guangzhou",
            name: "广州",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1555400082-6c3b6d6d5d9a",
            description: "南方门户，商贸中心",
            coordinate: Coordinate(latitude: 23.1291, longitude: 113.2644),
            popularity: 90
        ),
        Destination(
            id: "city_shenzhen",
            name: "深圳",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96",
            description: "创新之城，科技前沿",
            coordinate: Coordinate(latitude: 22.5431, longitude: 114.0579),
            popularity: 89
        ),
        Destination(
            id: "city_chengdu",
            name: "成都",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1570197788417-0e82375c9371",
            description: "天府之国，美食之都",
            coordinate: Coordinate(latitude: 30.5728, longitude: 104.0668),
            popularity: 88
        ),

        // 国际热门城市
        Destination(
            id: "city_tokyo",
            name: "东京",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1465101046530-73398c7f28ca",
            description: "日本首都，现代化都市",
            coordinate: Coordinate(latitude: 35.6762, longitude: 139.6503),
            popularity: 95
        ),
        Destination(
            id: "city_paris",
            name: "巴黎",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1502602898657-3e91760cbb34",
            description: "浪漫之都，艺术之城",
            coordinate: Coordinate(latitude: 48.8566, longitude: 2.3522),
            popularity: 94
        ),
        Destination(
            id: "city_london",
            name: "伦敦",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1513635269975-59663e0ac1ad",
            description: "英伦风情，历史悠久",
            coordinate: Coordinate(latitude: 51.5074, longitude: -0.1278),
            popularity: 93
        ),
        Destination(
            id: "city_newyork",
            name: "纽约",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9",
            description: "不夜之城，世界金融中心",
            coordinate: Coordinate(latitude: 40.7128, longitude: -74.0060),
            popularity: 96
        ),
        Destination(
            id: "city_seoul",
            name: "首尔",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1517154421773-0529f29ea451",
            description: "韩流文化，现代都市",
            coordinate: Coordinate(latitude: 37.5665, longitude: 126.9780),
            popularity: 87
        ),
        Destination(
            id: "city_singapore",
            name: "新加坡",
            type: .city,
            imageUrl: "https://images.unsplash.com/photo-1525625293386-3f8f99389edd",
            description: "花园城市，多元文化",
            coordinate: Coordinate(latitude: 1.3521, longitude: 103.8198),
            popularity: 91
        )
    ]
}