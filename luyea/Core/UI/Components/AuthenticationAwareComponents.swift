import SwiftUI

// MARK: - Login Prompt Card

/// 登录提示卡片组件
struct LoginPromptCard: View {
    
    @Environment(\.authenticationManager) private var authManager
    
    let title: String
    let message: String
    let buttonText: String
    let showIcon: Bool
    
    init(
        title: String = "需要登录",
        message: String = "登录后即可使用此功能",
        buttonText: String = "立即登录",
        showIcon: Bool = true
    ) {
        self.title = title
        self.message = message
        self.buttonText = buttonText
        self.showIcon = showIcon
    }
    
    var body: some View {
        VStack(spacing: 16) {
            if showIcon {
                Image(systemName: "person.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.accentColor)
            }
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(message)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: {
                authManager.presentLogin()
            }) {
                Text(buttonText)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.accentColor)
                    .cornerRadius(DesignSystemConstants.CornerRadius.small)
            }
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(DesignSystemConstants.CornerRadius.default)
        .shadow(color: Color.primary.opacity(0.1), radius: 8, x: 0, y: 2)
    }
}

// MARK: - Personal Greeting

/// 个人问候组件
struct PersonalGreeting: View {
    
    @Environment(\.authenticationManager) private var authManager
    
    let timeBasedGreeting: Bool
    
    init(timeBasedGreeting: Bool = true) {
        self.timeBasedGreeting = timeBasedGreeting
    }
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(greetingText)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.primary)
                
                if authManager.isAuthenticated {
                    Text(authManager.currentUser?.displayName ?? "用户")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.accentColor)
                } else {
                    Text("点击登录开始使用")
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            if !authManager.isAuthenticated {
                Button("登录") {
                    authManager.presentLogin()
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.accentColor)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    private var greetingText: String {
        if !timeBasedGreeting {
            return authManager.isAuthenticated ? "欢迎回来" : "欢迎使用"
        }
        
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return authManager.isAuthenticated ? "早上好" : "早上好"
        case 12..<18:
            return authManager.isAuthenticated ? "下午好" : "下午好"
        default:
            return authManager.isAuthenticated ? "晚上好" : "晚上好"
        }
    }
}

// MARK: - Feature Access Card

/// 功能访问卡片
struct FeatureAccessCard: View {
    
    @Environment(\.authenticationManager) private var authManager
    
    let title: String
    let description: String
    let icon: String
    let authenticatedAction: () -> Void
    let requiresAuth: Bool
    
    init(
        title: String,
        description: String,
        icon: String,
        requiresAuth: Bool = true,
        authenticatedAction: @escaping () -> Void
    ) {
        self.title = title
        self.description = description
        self.icon = icon
        self.requiresAuth = requiresAuth
        self.authenticatedAction = authenticatedAction
    }
    
    var body: some View {
        Button(action: handleTap) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(.accentColor)
                    .frame(width: 40, height: 40)
                    .background(Color.accentColor.opacity(0.1))
                    .cornerRadius(DesignSystemConstants.CornerRadius.small)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Text(description)
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                if requiresAuth && !authManager.isAuthenticated {
                    Image(systemName: "lock.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.orange)
                } else {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
            }
            .padding(16)
            .background(Color(.systemBackground))
            .cornerRadius(DesignSystemConstants.CornerRadius.default)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.default)
                    .stroke(Color(.systemGray5), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func handleTap() {
        if requiresAuth {
            authManager.requireAuthentication {
                authenticatedAction()
            }
        } else {
            authenticatedAction()
        }
    }
}

// MARK: - Authentication Status Banner

/// 认证状态横幅
struct AuthenticationStatusBanner: View {
    
    @Environment(\.authenticationManager) private var authManager
    
    let showWhenAuthenticated: Bool
    
    init(showWhenAuthenticated: Bool = false) {
        self.showWhenAuthenticated = showWhenAuthenticated
    }
    
    var body: some View {
        Group {
            if shouldShow {
                HStack {
                    Image(systemName: authManager.isAuthenticated ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                        .foregroundColor(authManager.isAuthenticated ? .green : .orange)
                    
                    Text(authManager.isAuthenticated ? "已登录" : "未登录")
                        .font(.system(size: 14, weight: .medium))
                    
                    Spacer()
                    
                    if !authManager.isAuthenticated {
                        Button("登录") {
                            authManager.presentLogin()
                        }
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.accentColor)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, DesignSystemConstants.Spacing.small)
                .background(authManager.isAuthenticated ? Color.green.opacity(0.1) : Color.orange.opacity(0.1))
                .cornerRadius(DesignSystemConstants.CornerRadius.small)
            }
        }
    }
    
    private var shouldShow: Bool {
        if showWhenAuthenticated {
            return authManager.isAuthenticated
        } else {
            return !authManager.isAuthenticated
        }
    }
}

// MARK: - Quick Login Button

/// 快速登录按钮
struct QuickLoginButton: View {
    
    @Environment(\.authenticationManager) private var authManager
    
    let style: Style
    let text: String?
    
    enum Style {
        case primary
        case secondary
        case minimal
    }
    
    init(style: Style = .primary, text: String? = nil) {
        self.style = style
        self.text = text
    }
    
    var body: some View {
        if !authManager.isAuthenticated {
            Button(action: {
                authManager.presentLogin()
            }) {
                HStack {
                    Image(systemName: "person.circle")
                    Text(text ?? "登录")
                }
                .font(.system(size: buttonFontSize, weight: .medium))
                .foregroundColor(buttonForegroundColor)
                .padding(.horizontal, buttonHorizontalPadding)
                .padding(.vertical, buttonVerticalPadding)
                .background(buttonBackground)
                .cornerRadius(buttonCornerRadius)
            }
        }
    }
    
    private var buttonFontSize: CGFloat {
        switch style {
        case .primary: return 16
        case .secondary: return 14
        case .minimal: return 12
        }
    }
    
    private var buttonForegroundColor: Color {
        switch style {
        case .primary: return .white
        case .secondary: return .accentColor
        case .minimal: return .accentColor
        }
    }

    private var buttonBackground: Color {
        switch style {
        case .primary: return .accentColor
        case .secondary: return Color.accentColor.opacity(0.1)
        case .minimal: return .clear
        }
    }
    
    private var buttonHorizontalPadding: CGFloat {
        switch style {
        case .primary: return 20
        case .secondary: return 16
        case .minimal: return 8
        }
    }
    
    private var buttonVerticalPadding: CGFloat {
        switch style {
        case .primary: return 12
        case .secondary: return 8
        case .minimal: return 4
        }
    }
    
    private var buttonCornerRadius: CGFloat {
        switch style {
        case .primary: return 8
        case .secondary: return 6
        case .minimal: return 4
        }
    }
}
