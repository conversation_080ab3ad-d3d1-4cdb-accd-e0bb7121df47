import Foundation

/// API 接口路径常量定义
///
/// 集中管理所有后端接口路径，便于维护和修改。
/// 按业务模块分组，提高代码可读性和可维护性。
enum APIPaths {
    // MARK: - 启动页相关

    /// 启动页配置接口
    static let splashConfig = "/splash/config"

    // MARK: - 用户相关

    /// 用户资料接口
    static let userProfile = "/user/profile"
    /// 用户登录接口
    static let userLogin = "/user/login"
    /// 用户注册接口
    static let userRegister = "/user/register"
    /// 用户注销接口
    static let userLogout = "/user/logout"

    // MARK: - 认证相关

    /// 发送验证码接口
    static let authSendCode = "/auth/send-code"
    /// 手机号登录接口
    static let authLogin = "/auth/login"
    /// 登出接口
    static let authLogout = "/auth/logout"

    // 注意：微信登录和Apple登录通过SDK实现，不需要API接口

    // MARK: - 个人中心相关

    /// 当前用户资料接口
    static let profileCurrent = "/profile/current"
    /// 用户统计数据接口
    static let profileStats = "/profile/stats"

    // MARK: - 发现相关

    /// 发现列表接口
    static let discoverList = "/discover/list"
    /// 话题列表接口
    static let topicList = "/topic/list"
    /// 目的地列表接口
    static let destinationList = "/destination/list"
    /// 精选目的地接口
    static let featuredDestinations = "/destination/featured"
    /// 全部目的地接口
    static let allDestinations = "/destination/all"

    // MARK: - 行程相关

    /// 行程列表接口
    static let itineraryList = "/itinerary/list"
    /// 全部行程列表接口（分页）
    static let allItinerariesList = "/itinerary/all"
    /// 行程相关接口路径前缀（用于分页匹配）
    static let itineraryPrefix = "/itinerary/"
    /// 创建行程接口
    static let itineraryCreate = "/itinerary/create"
    /// 更新行程接口
    static let itineraryUpdate = "/itinerary/update"
    /// 删除行程接口
    static let itineraryDelete = "/itinerary/delete"

    // MARK: - 足迹相关

    /// 足迹列表接口
    static let footprintList = "/footprint/list"
    /// 创建足迹接口
    static let footprintCreate = "/footprint/create"

    // MARK: - 我的作品相关

    /// 我的作品统计数据接口
    static let myWorksStats = "/my-works/stats"
    /// 我的作品列表接口（分页）
    static let myWorksList = "/my-works/list"
    /// 我的作品话题统计接口（包含作品数量）
    static let myWorksTopics = "/my-works/topics"

    // MARK: - 探索旅程相关

    /// 推荐内容接口
    static let exploreRecommendations = "/explore/recommendations"
    /// 搜索内容接口
    static let exploreSearch = "/explore/search"
    /// 热门目的地接口
    static let explorePopularDestinations = "/explore/destinations/popular"
    /// 附近内容接口
    static let exploreNearby = "/explore/nearby"
    /// 用户偏好接口
    static let exploreUserPreferences = "/explore/preferences"

    // MARK: - 新建行程相关

    /// 创建行程接口
    static let createItinerary = "/itinerary/create"
    /// 搜索景点接口
    static let searchAttractions = "/attractions/search"
    /// 获取推荐景点接口
    static let getRecommendedAttractions = "/attractions/recommendations"
    /// 获取热门目的地接口
    static let getPopularDestinations = "/destinations/popular"
    /// 估算行程费用接口
    static let estimateItineraryCost = "/itinerary/estimate-cost"
}