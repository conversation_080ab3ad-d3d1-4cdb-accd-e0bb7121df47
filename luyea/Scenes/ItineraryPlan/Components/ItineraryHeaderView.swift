import SwiftUI

/// 行程计划页的头部视图，包含标题、口号和用于添加新行程的按钮。
struct ItineraryHeaderView: View {
    
    // MARK: - Properties
    
    /// 当用户点击"新行程"按钮时执行的回调闭包。
    var onAdd: () -> Void

    @Environment(\.authenticationManager) private var authManager
    
    // MARK: - Body
    
    var body: some View {
        HStack {
            titleAndSlogan
            Spacer()
            newItineraryButton
        }
    }
    
    // MARK: - Private Views
    
    /// 显示主标题"我的旅程"和副标题口号的视图组件。
    private var titleAndSlogan: some View {
        VStack(alignment: .leading) {
            ItinerarySectionTitleView(title: "我的旅程")
            ItinerarySloganView(text: "计划每一步，享受每一程")
        }
    }
    
    /// "新行程"按钮，采用简洁的线框胶囊样式。
    private var newItineraryButton: some View {
        Button(action: handleNewItineraryTap) {
            HStack(spacing: 2) {
                Image(systemName: "plus")
                    .font(.system(size: 11, weight: .medium))
                Text("新行程")
                    .font(.system(size: 12, weight: .semibold))
            }
            .foregroundColor(.blue)
            .padding(.horizontal, 10)
            .padding(.vertical, 5)
            .background(Capsule().stroke(Color.blue, lineWidth: 1))
        }
        .buttonStyle(ScaleButtonStyle(scale: 0.95, response: 0.2, damping: 0.8))
        .accessibilityLabel("新行程")
    }

    // MARK: - Private Methods

    /// 处理新行程按钮点击
    private func handleNewItineraryTap() {
        // 立即提供触觉反馈，提升响应感
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // 检查认证状态
        authManager.requireAuthentication {
            // 用户登录后执行原有逻辑
            TabBarStateManager.shared.hideImmediately()
            DispatchQueue.main.async {
                onAdd()
            }
        }
    }
}

// MARK: - Preview

#if DEBUG
struct ItineraryHeaderView_Previews: PreviewProvider {
    static var previews: some View {
        ItineraryHeaderView(onAdd: {})
            .padding()
            .preferredColorScheme(.light)
            .previewLayout(.sizeThatFits)
    }
}
#endif
