import Foundation
import SwiftUI

/// 音频播放器API
///
/// 提供便捷的播放器控制接口。
@MainActor
final class AudioPlayerAPI {

    static let shared = AudioPlayerAPI()

    private var playerManager: AudioPlayerManager {
        return AudioPlayerManager.shared
    }

    private var audioService: AudioPlayerService {
        return AudioPlayerService.shared
    }

    private init() {}
    
    // MARK: - Public API - 播放控制

    /// 播放音频
    /// - Parameters:
    ///   - title: 音频标题
    ///   - audioURL: 音频URL（可选）
    /// - Returns: 是否成功开始播放
    @discardableResult
    func playAudio(
        title: String,
        audioURL: URL? = nil
    ) -> Bool {
        let audioInfo = AudioInfo(
            id: UUID().uuidString,
            title: title,
            audioURL: audioURL
        )

        return playAudio(audioInfo: audioInfo)
    }
    
    /// 播放音频信息
    /// - Parameter audioInfo: 音频信息
    /// - Returns: 是否成功开始播放
    @discardableResult
    func playAudio(audioInfo: AudioInfo) -> Bool {
        playerManager.showPlayer(with: audioInfo)

        Task {
            do {
                try await audioService.play(audioInfo: audioInfo)
            } catch {
                Log.error("音频播放失败: \(error)")
            }
        }

        return true
    }
    
    /// 隐藏播放器
    func hidePlayer() {
        playerManager.hidePlayer()
    }
    
}
