import SwiftUI

/// 创建行程按钮组件
///
/// 功能特性：
/// - 动态按钮文案
/// - 行程创建处理
/// - 完整信息收集和日志输出
struct CreateItineraryButtonView: View {
    
    // MARK: - Properties

    let selectedDestinations: [Destination]
    let selectedAttractions: [AttractionModel]
    let departureLocation: String
    let departureDate: Date?
    let tripDays: Int
    let includeReturn: Bool
    let estimatedBudget: String
    let enableCityRecommendation: Bool
    let recommendationTypes: Set<RecommendationType>
    let isCreating: Bool
    let onCreateItinerary: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        Button(action: handleCreateItinerary) {
            HStack(spacing: 12) {
                if isCreating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "sparkles")
                        .font(.headline.weight(.semibold))
                }

                Text(createButtonText)
                    .font(.headline.weight(.semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                LinearGradient(
                    colors: isCreating ? [.gray, .gray.opacity(0.8)] : [.blue, .blue.opacity(0.8)],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
            .shadow(color: isCreating ? .gray.opacity(0.3) : .blue.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isCreating)
        .scaleEffect(1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedAttractions.count)
    }
    
    // MARK: - Private Properties
    
    /// 创建按钮文案
    private var createButtonText: String {
        if isCreating {
            return "创建中..."
        } else if selectedAttractions.isEmpty {
            return "创建行程并获取推荐"
        } else {
            return "创建我的行程"
        }
    }
    
    // MARK: - Private Methods
    
    /// 处理创建行程操作
    private func handleCreateItinerary() {
        let destinationNames = selectedDestinations.map { $0.name }.joined(separator: "、")
        _ = destinationNames.isEmpty ? "未选择" : destinationNames
        _ = selectedAttractions.map { $0.name }.joined(separator: "、")
        let budgetText = estimatedBudget.isEmpty ? "未设置" : "¥\(estimatedBudget)"
        let departureText = departureLocation.isEmpty ? "未设置" : departureLocation
        let tripTypeText = includeReturn ? "往返" : "单程"

        let departureDateText: String
        if let departureDate = departureDate {
            let dateFormatter = DateFormatter()
            dateFormatter.locale = Locale(identifier: "zh_CN")
            dateFormatter.dateFormat = "yyyy年M月d日"
            departureDateText = dateFormatter.string(from: departureDate)
        } else {
            departureDateText = "未设置"
        }

        // 创建行程数据结构（使用字符串便利构造器保持兼容性）
        _ = ItineraryCreationData.fromStrings(
            departureLocation: departureText,
            departureDate: departureDateText,
            destinations: selectedDestinations,
            attractions: selectedAttractions,
            tripType: tripTypeText,
            tripDays: tripDays,
            budget: budgetText,
            recommendationEnabled: enableCityRecommendation ? "启用" : "禁用"
        )

        // 调用外部处理逻辑
        onCreateItinerary()
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        CreateItineraryButtonView(
            selectedDestinations: [Destination.samples[0]],
            selectedAttractions: [],
            departureLocation: "北京",
            departureDate: Date(),
            tripDays: 3,
            includeReturn: true,
            estimatedBudget: "5000",
            enableCityRecommendation: true,
            recommendationTypes: [.attractions, .restaurants],
            isCreating: false,
            onCreateItinerary: {}
        )

        CreateItineraryButtonView(
            selectedDestinations: [Destination.samples[0]],
            selectedAttractions: [AttractionModel.samples[0]],
            departureLocation: "",
            departureDate: Date(),
            tripDays: 5,
            includeReturn: false,
            estimatedBudget: "",
            enableCityRecommendation: false,
            recommendationTypes: [],
            isCreating: true,
            onCreateItinerary: {}
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
