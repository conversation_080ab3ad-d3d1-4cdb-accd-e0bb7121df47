import Foundation
import SwiftUI

/// 通用防抖工具
///
/// 基于 iOS 18.0+ 的现代 Swift 并发技术实现的简洁高效防抖工具。
/// 专注于核心防抖功能，适用于所有场景。
///
/// 核心特性：
/// - 线程安全，基于 Swift 6 并发模型
/// - 支持同步和异步操作
/// - 自动内存管理
/// - 简洁的 API 设计
/// - 高性能，低开销
///
/// 使用示例：
/// ```swift
/// // 基础防抖
/// let debouncer = Debouncer(delay: 0.5)
/// debouncer.call {
///     print("防抖执行")
/// }
///
/// // 异步防抖
/// let result = await debouncer.callAsync {
///     return await someAsyncOperation()
/// }
/// ```
/// 通用防抖器
///
/// 线程安全的防抖工具，支持同步和异步操作
@MainActor
final class Debouncer: ObservableObject {

    // MARK: - Properties

    /// 防抖延迟时间
    private let delay: TimeInterval

    /// 当前的防抖任务
    private var currentTask: Task<Void, Never>?

    /// 防抖状态（可选，用于 UI 绑定）
    @Published private(set) var isActive: Bool = false

    // MARK: - Initialization

    /// 初始化防抖器
    /// - Parameter delay: 防抖延迟时间（秒）
    init(delay: TimeInterval) {
        self.delay = delay
    }

    // MARK: - 同步操作

    /// 执行防抖操作（同步）
    /// - Parameter action: 要执行的操作
    func call(_ action: @escaping @Sendable () -> Void) {
        cancel()
        isActive = true

        currentTask = Task<Void, Never> { @MainActor in
            do {
                try await Task.sleep(for: .seconds(delay))
                guard !Task.isCancelled else {
                    isActive = false
                    return
                }

                action()
                isActive = false

            } catch is CancellationError {
                isActive = false
            } catch {
                isActive = false
            }
        }
    }

    /// 立即执行（跳过防抖）
    /// - Parameter action: 要执行的操作
    func callImmediately(_ action: @escaping @Sendable () -> Void) {
        cancel()
        action()
    }

    // MARK: - 异步操作

    /// 执行防抖操作（异步）
    /// - Parameter action: 要执行的异步操作
    /// - Returns: 操作结果，如果被取消则返回 nil
    func callAsync<T>(_ action: @escaping @Sendable () async throws -> T) async -> T? {
        cancel()
        isActive = true

        let task = Task<T?, Never> { @MainActor in
            do {
                try await Task.sleep(for: .seconds(delay))
                guard !Task.isCancelled else {
                    isActive = false
                    return nil
                }

                let result = try await action()
                isActive = false
                return result

            } catch is CancellationError {
                isActive = false
                return nil
            } catch {
                isActive = false
                return nil
            }
        }

        currentTask = Task { await _ = task.value }
        return await task.value
    }

    /// 立即执行异步操作（跳过防抖）
    /// - Parameter action: 要执行的异步操作
    /// - Returns: 操作结果
    func callAsyncImmediately<T>(_ action: @escaping @Sendable () async throws -> T) async throws -> T {
        cancel()
        return try await action()
    }

    // MARK: - 控制方法

    /// 取消当前的防抖操作
    func cancel() {
        currentTask?.cancel()
        currentTask = nil
        isActive = false
    }

    deinit {
        currentTask?.cancel()
    }
}

// MARK: - SwiftUI 扩展

/// SwiftUI View 扩展，提供便捷的防抖修饰符
extension View {

    /// 为文本输入添加防抖功能
    /// - Parameters:
    ///   - text: 绑定的文本
    ///   - delay: 防抖延迟时间
    ///   - action: 防抖后执行的操作
    /// - Returns: 带有防抖功能的视图
    func debounced(
        text: Binding<String>,
        delay: TimeInterval = 0.5,
        action: @escaping @Sendable (String) -> Void
    ) -> some View {
        self.modifier(DebouncedTextModifier(
            text: text,
            delay: delay,
            action: action
        ))
    }

    /// 为任意值变化添加防抖功能
    /// - Parameters:
    ///   - value: 要监听的值
    ///   - delay: 防抖延迟时间
    ///   - action: 防抖后执行的操作
    /// - Returns: 带有防抖功能的视图
    func debounced<T: Equatable>(
        value: T,
        delay: TimeInterval = 0.5,
        action: @escaping @Sendable (T) -> Void
    ) -> some View {
        self.modifier(DebouncedValueModifier(
            value: value,
            delay: delay,
            action: action
        ))
    }

    /// 为异步操作添加防抖功能
    /// - Parameters:
    ///   - trigger: 触发防抖的值
    ///   - delay: 防抖延迟时间
    ///   - action: 防抖后执行的异步操作
    /// - Returns: 带有防抖功能的视图
    func debouncedAsync<T: Equatable>(
        trigger: T,
        delay: TimeInterval = 0.5,
        action: @escaping @Sendable (T) async -> Void
    ) -> some View {
        self.modifier(DebouncedAsyncModifier(
            trigger: trigger,
            delay: delay,
            action: action
        ))
    }
}

// MARK: - 修饰符实现

/// 防抖文本修饰符
private struct DebouncedTextModifier: ViewModifier {
    @Binding var text: String
    let delay: TimeInterval
    let action: @Sendable (String) -> Void

    @StateObject private var debouncer: Debouncer

    init(text: Binding<String>, delay: TimeInterval, action: @escaping @Sendable (String) -> Void) {
        self._text = text
        self.delay = delay
        self.action = action
        self._debouncer = StateObject(wrappedValue: Debouncer(delay: delay))
    }

    func body(content: Content) -> some View {
        content
            .onChange(of: text) { _, newValue in
                debouncer.call {
                    action(newValue)
                }
            }
    }
}

/// 防抖值修饰符
private struct DebouncedValueModifier<T: Equatable>: ViewModifier {
    let value: T
    let delay: TimeInterval
    let action: @Sendable (T) -> Void

    @StateObject private var debouncer: Debouncer

    init(value: T, delay: TimeInterval, action: @escaping @Sendable (T) -> Void) {
        self.value = value
        self.delay = delay
        self.action = action
        self._debouncer = StateObject(wrappedValue: Debouncer(delay: delay))
    }

    func body(content: Content) -> some View {
        content
            .onChange(of: value) { _, newValue in
                debouncer.call {
                    action(newValue)
                }
            }
    }
}

/// 防抖异步修饰符
private struct DebouncedAsyncModifier<T: Equatable>: ViewModifier {
    let trigger: T
    let delay: TimeInterval
    let action: @Sendable (T) async -> Void

    @StateObject private var debouncer: Debouncer

    init(trigger: T, delay: TimeInterval, action: @escaping @Sendable (T) async -> Void) {
        self.trigger = trigger
        self.delay = delay
        self.action = action
        self._debouncer = StateObject(wrappedValue: Debouncer(delay: delay))
    }

    func body(content: Content) -> some View {
        content
            .onChange(of: trigger) { _, newValue in
                Task {
                    await debouncer.callAsync {
                        await action(newValue)
                    }
                }
            }
    }
}

// MARK: - 使用示例和最佳实践

/// 使用示例
///
/// ```swift
/// // 1. 基础用法
/// let debouncer = Debouncer(delay: 0.5)
/// debouncer.call {
///     print("防抖执行")
/// }
///
/// // 2. 异步用法
/// let result = await debouncer.callAsync {
///     return await networkRequest()
/// }
///
/// // 3. SwiftUI 中使用
/// TextField("搜索", text: $searchText)
///     .debounced(text: $searchText) { query in
///         performSearch(query)
///     }
///
/// // 4. 监听值变化
/// SomeView()
///     .debounced(value: someValue) { newValue in
///         handleValueChange(newValue)
///     }
///
/// // 5. 异步操作
/// SomeView()
///     .debouncedAsync(trigger: searchQuery) { query in
///         await performAsyncSearch(query)
///     }
/// ```
