import Foundation

// MARK: - Mock分页筛选策略入口

/// Mock分页筛选策略的主要入口类
///
/// 提供统一的策略查找和应用接口，是整个Mock分页策略系统的门面。
/// 通过这个类可以访问所有已注册的策略，并自动应用相应的筛选和排序逻辑。
///
/// ## 主要功能
/// - 自动策略匹配：根据API路径自动选择合适的策略
/// - 统一接口：提供一致的数据处理接口
/// - 错误处理：优雅的错误处理和降级机制
///
/// ## 使用示例
/// ```swift
/// let filteredItems = MockPaginationFilterStrategy.applyStrategy(
///     for: "/api/my-works",
///     to: originalItems,
///     with: ["sortBy": "fork_count", "order": "desc"]
/// )
/// ```
///
/// ## 架构设计
/// ```
/// MockPaginationFilterStrategy (门面)
///     ↓
/// MockPaginationStrategyConfigs (配置管理)
///     ↓
/// MockStandardPaginationStrategy (具体实现)
///     ↓
/// MockQueryParam + MockFilterHandler + MockSortHandler (处理器)
/// ```
///
/// - Author: Augment Agent
/// - Version: 3.0
/// - Since: iOS 18.0
public struct MockPaginationFilterStrategy {
    
    // MARK: - 公共接口
    
    /// 为指定API路径应用Mock分页策略
    /// - Parameters:
    ///   - apiPath: API路径
    ///   - data: 原始JSON数据
    ///   - queryParams: 查询参数
    ///   - decoder: JSON解码器（默认配置ISO8601日期格式）
    /// - Returns: 处理后的JSON数据
    /// - Throws: 如果处理过程中出现错误
    public static func applyStrategy(
        for apiPath: String,
        to data: Data,
        with queryParams: [String: String] = [:],
        decoder: JSONDecoder = {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return decoder
        }()
    ) throws -> Data {
        guard let strategy = MockPaginationStrategyConfigs.findStrategy(for: apiPath) else {
            return data
        }

        do {
            return try strategy.applyFilters(to: data, with: queryParams, decoder: decoder)
        } catch {
            Log.error("Mock策略应用失败: \(apiPath) - \(error)")
            throw MockPaginationStrategyError.configurationError(message: "策略应用失败: \(error.localizedDescription)")
        }
    }
    
    /// 获取指定API路径的策略信息
    /// - Parameter apiPath: API路径
    /// - Returns: 策略名称，如果没有找到则返回nil
    public static func getStrategyName(for apiPath: String) -> String? {
        return MockPaginationStrategyConfigs.findStrategy(for: apiPath)?.name
    }
    
    /// 获取所有可用的策略名称
    /// - Returns: 策略名称数组
    public static func getAllStrategyNames() -> [String] {
        return MockPaginationStrategyConfigs.getAllStrategyNames()
    }
    
    /// 验证系统配置
    /// - Returns: 验证结果
    public static func validateConfiguration() -> (isValid: Bool, errors: [String]) {
        return MockPaginationStrategyConfigs.validateConfigs()
    }
    
    // MARK: - 调试和监控
    
    public static func printSystemStatus() {
        Log.info("Mock分页策略系统状态: \(MockPaginationStrategyConfigs.strategies.count)个策略")

        let validation = validateConfiguration()
        if !validation.isValid {
            Log.warning("Mock配置问题: \(validation.errors.joined(separator: ", "))")
        }
    }
}

// MARK: - 便利方法

extension MockPaginationFilterStrategy {
    
    public static func quickApply(for apiPath: String, to data: Data) -> Data {
        do {
            return try applyStrategy(for: apiPath, to: data)
        } catch {
            return data
        }
    }
    
    /// 检查API路径是否有对应的策略
    /// - Parameter apiPath: API路径
    /// - Returns: 是否有对应策略
    public static func hasStrategy(for apiPath: String) -> Bool {
        return MockPaginationStrategyConfigs.findStrategy(for: apiPath) != nil
    }
    
    /// 获取策略处理能力概览
    /// - Returns: 处理能力描述
    public static func getCapabilityOverview() -> [String: [String]] {
        var capabilities: [String: [String]] = [:]
        
        for strategy in MockPaginationStrategyConfigs.strategies {
            capabilities[strategy.name] = [
                "筛选功能",
                "排序功能", 
                "分页功能"
            ]
        }
        
        return capabilities
    }
}

// MARK: - 系统初始化

extension MockPaginationFilterStrategy {
    
    public static func initializeSystem() {
        let strategiesCount = MockPaginationStrategyConfigs.strategies.count
        Log.info("Mock分页策略初始化: \(strategiesCount)个策略")

        let validation = validateConfiguration()
        if !validation.isValid {
            Log.error("Mock配置验证失败: \(validation.errors.joined(separator: ", "))")
        }
    }
}
