import SwiftUI

/// 分隔线视图
///
/// 提供渐变效果的分隔线，用于分隔不同的内容区域。
/// 采用线性渐变设计，提供优雅的视觉分隔效果。
struct DividerView: View {
    
    // MARK: - Properties
    
    let opacity: Double
    let horizontalPadding: CGFloat
    let verticalPadding: CGFloat
    
    // MARK: - Initialization
    
    init(
        opacity: Double = 0.2,
        horizontalPadding: CGFloat = 20,
        verticalPadding: CGFloat = 8
    ) {
        self.opacity = opacity
        self.horizontalPadding = horizontalPadding
        self.verticalPadding = verticalPadding
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            LinearGradient(
                colors: [
                    Color.clear,
                    Color.secondary.opacity(opacity),
                    Color.clear
                ],
                startPoint: .leading,
                endPoint: .trailing
            )
            .frame(height: 1)
            .padding(.horizontal, horizontalPadding)
        }
        .padding(.vertical, verticalPadding)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        Text("上方内容")
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        
        DividerView()
        
        Text("下方内容")
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
