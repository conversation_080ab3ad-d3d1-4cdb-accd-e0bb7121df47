import SwiftUI

struct AuthenticationContentView: View {
    @ObservedObject var viewModel: AuthenticationViewModel
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    var body: some View {
        ScrollView {
            VStack(spacing: DesignSystemConstants.Spacing.xl) {
                logoSection
                    .padding(.top, 20)
                Spacer()
                welcomeSection
                loginFormSection
                Spacer()
                Spacer()
                thirdPartyLoginSection
                    .padding(.bottom, 20)
            }
            .padding(.horizontal, DesignSystemConstants.Spacing.large)
        }
        .appBackground()
        .alert(alertTitle, isPresented: $showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }

    private var logoSection: some View {
        RoundedRectangle(cornerRadius: 20, style: .continuous)
            .fill(.ultraThinMaterial)
            .frame(width: 80, height: 80)
            .shadow(color: Color.primary.opacity(0.1), radius: 12, x: 0, y: 6)
            .shadow(color: Color.accentColor.opacity(0.15), radius: 8, x: 0, y: 4)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.5),
                                Color.white.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }

    private var welcomeSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(AuthenticationConstants.Text.loginTitle)
                    .font(.system(size: 26, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                Text(AuthenticationConstants.Text.loginSubtitle)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
            }

            Spacer()
        }
        .padding(.horizontal, 8)
    }
    
    private var loginFormSection: some View {
        VStack(spacing: DesignSystemConstants.Spacing.standard) {
            PhoneInputView(
                phoneNumber: $viewModel.phoneNumber,
                isValid: viewModel.isPhoneNumberValid
            )

            VerificationCodeInputView(
                verificationCode: $viewModel.verificationCode,
                codeSendState: viewModel.codeSendState,
                sendCodeButtonTitle: viewModel.sendCodeButtonTitle,
                canSendCode: viewModel.canSendCode,
                isValid: viewModel.isVerificationCodeValid,
                onSendCode: {
                    Task {
                        await viewModel.sendVerificationCode()
                    }
                }
            )

            HStack {
                agreementTextView
                Spacer()
            }
            .padding(.top, 8)

            AuthenticationButtonView.primary(
                title: AuthenticationConstants.Text.loginButtonTitle,
                isEnabled: true,
                isLoading: viewModel.isLoading
            ) {
                Task {
                    await handleLoginButtonTap()
                }
            }
        }
    }

    private var agreementTextView: some View {
        HStack(spacing: 4) {
            Text("登录即同意")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.secondary)

            Button("《用户协议》") {
                print("显示用户协议")
            }
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.blue)

            Text("和")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.secondary)

            Button("《隐私政策》") {
                print("显示隐私政策")
            }
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.blue)
        }
    }

    private var thirdPartyLoginSection: some View {
        VStack(spacing: 16) {
            // 简洁的分隔线
            HStack {
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 0.5)

                Text("其他登录方式")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .fixedSize()

                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 0.5)
            }

            // 第三方登录按钮
            HStack(spacing: 20) {
                ForEach([
                    (type: ThirdPartyLoginButton.LoginType.wechat, action: viewModel.loginWithWeChat),
                    (type: ThirdPartyLoginButton.LoginType.qq, action: viewModel.loginWithQQ),
                    (type: ThirdPartyLoginButton.LoginType.apple, action: viewModel.loginWithApple)
                ], id: \.type) { item in
                    ThirdPartyLoginButton(type: item.type, isLoading: viewModel.isLoading) {
                        Task {
                            await item.action()
                        }
                    }
                }
            }
        }
    }

    // MARK: - Actions

    /// 处理登录按钮点击
    private func handleLoginButtonTap() async {
        if let validationError = validateLoginForm() {
            showInputAlert(title: validationError.title, message: validationError.message)
            return
        }

        await viewModel.loginWithPhoneNumber()
    }

    /// 验证登录表单
    private func validateLoginForm() -> (title: String, message: String)? {
        if viewModel.phoneNumber.isEmpty {
            return ("请输入手机号", "请输入您的手机号码以继续登录")
        }

        if !viewModel.isPhoneNumberValid {
            return ("手机号格式错误", "请输入正确的11位手机号码")
        }

        if viewModel.verificationCode.isEmpty {
            return ("请输入验证码", "请先获取并输入验证码")
        }

        if !viewModel.isVerificationCodeValid {
            return ("验证码格式错误", "请输入正确的6位验证码")
        }

        return nil
    }

    /// 显示输入提示弹窗
    private func showInputAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showAlert = true
    }
}

// MARK: - Preview

#Preview("Authentication内容视图") {
    AuthenticationContentView(viewModel: AuthenticationViewModel())
}
