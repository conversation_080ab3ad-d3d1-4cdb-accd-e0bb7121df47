import SwiftUI

/// LogoTitleView
/// 品牌logo+主副标题美化组件，首页导航栏专用。
struct LogoTitleView: View {
    @State private var animate = false
    var body: some View {
        HStack(alignment: .center, spacing: 12) {
            ZStack {
                RoundedRectangle(cornerRadius: 12, style: .continuous)
                    .fill(
                        LinearGradient(gradient: Gradient(colors: [Color.blue.opacity(0.22), Color.purple.opacity(0.18), Color.cyan.opacity(0.18)]), startPoint: .topLeading, endPoint: .bottomTrailing)
                    )
                    .frame(width: 44, height: 44)
                    .shadow(color: Color.blue.opacity(0.10), radius: 8, x: 0, y: 4)
                Image("AppLogo")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 28, height: 28)
                    .shadow(color: Color.blue.opacity(0.10), radius: 2, x: 0, y: 1)
                    .scaleEffect(animate ? 1.0 : 0.7)
                    .rotationEffect(.degrees(animate ? 0 : -30))
                    .opacity(animate ? 1 : 0)
                    .animation(.spring(response: 0.7, dampingFraction: 0.7).delay(0.1), value: animate)
            }
            .onAppear { animate = true }
            Text("路亦")
                .font(.system(size: 22, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(gradient: Gradient(colors: [Color.blue, Color.purple]), startPoint: .leading, endPoint: .trailing)
                )
                .shadow(color: Color.blue.opacity(0.18), radius: 2, x: 0, y: 1)
                .scaleEffect(animate ? 1.0 : 0.8)
                .opacity(animate ? 1 : 0)
                .animation(.spring(response: 0.7, dampingFraction: 0.7).delay(0.18), value: animate)
                .padding(.top, 10)
        }
    }
} 