import SwiftUI

/// 热门城市选择区域组件
///
/// 显示热门城市网格，支持快速选择常用城市。
/// 采用网格布局，提供直观的城市选择体验。
struct PopularCitiesSection: View {
    
    // MARK: - Properties
    
    let onCitySelected: (String) -> Void
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            sectionHeader
            citiesGrid
        }
    }
    
    // MARK: - Private Views
    
    /// 区域标题
    private var sectionHeader: some View {
        HStack {
            Image(systemName: "star.fill")
                .font(.title3)
                .foregroundColor(.orange)
            
            Text("热门城市")
                .font(.headline.weight(.medium))
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
    
    /// 城市网格
    private var citiesGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
            ForEach(LocationDataService.popularCities, id: \.self) { city in
                CityButton(
                    city: city,
                    onTap: {
                        onCitySelected(city)
                    }
                )
            }
        }
    }
}

// MARK: - CityButton

/// 城市选择按钮
private struct CityButton: View {
    let city: String
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(city)
                .font(.subheadline.weight(.medium))
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color(.systemBackground))
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    PopularCitiesSection(
        onCitySelected: { _ in }
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
