import SwiftUI

/// 行程计划主入口视图
///
/// 负责整合背景、内容容器和导航元素的主视图。
/// 作为行程管理功能的入口点，提供行程浏览和创建功能。
///
/// 主要功能：
/// - 展示用户的所有行程计划
/// - 提供应用 Logo 和标题显示
/// - 集成全局背景样式系统
/// - 管理到行程创建页面的导航
///
/// 架构特点：
/// - 采用 MVVM 架构模式
/// - 使用 NavigationStack 进行页面导航
/// - 集成 TabBar 显示控制
/// - 支持响应式布局和状态管理
struct ItineraryPlanView: View {

    // MARK: - ViewModels & State

    /// 行程计划页面的视图模型
    ///
    /// 负责管理行程数据的加载、缓存和业务逻辑处理。
    /// 提供行程列表的数据源和相关操作方法。
    @StateObject private var viewModel = ItineraryPlanViewModel()

    /// 控制是否导航到行程创建页面的状态
    ///
    /// 当用户触发创建行程操作时，此状态会被设置为 true，
    /// 触发导航到 NewItineraryMainView。
    @State private var navigateToItineraryCreation = false

    // MARK: - Body
    
    var body: some View {
        NavigationStack {
            content
                .navigationTitle("")
                .toolbar { navigationToolbar }
                .navigationDestination(isPresented: $navigateToItineraryCreation) {
                    NewItineraryMainView()
                }
        }
    }
    
    // MARK: - Private Views
    
    /// 主内容区域
    ///
    /// 包含行程内容容器，应用全局背景样式，并确保 TabBar 正确显示。
    /// 整合了行程列表的展示和相关交互功能。
    private var content: some View {
        ItineraryContentContainer(
            viewModel: viewModel,
            navigateToItineraryCreation: $navigateToItineraryCreation
        )
        .appBackground()
        .showTabBar()
    }

    /// 导航栏的工具栏内容
    ///
    /// 在导航栏左侧显示应用 Logo 和标题，
    /// 提供品牌标识和页面标识功能。
    @ToolbarContentBuilder
    private var navigationToolbar: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            LogoTitleView()
        }
    }
}

// MARK: - Preview

#Preview {
    ItineraryPlanView()
        // 全局Toast无需环境对象
}
