import Foundation
import os.log

/// 通用日志工具类
///
/// 提供统一的日志记录功能，支持多级别输出和性能监控。
/// 在Debug模式下输出到控制台，支持系统日志记录。
enum Log {
    // MARK: - Log Level

    /// 日志级别枚举
    enum Level: String, CaseIterable {
        case debug = "🐞"
        case info = "ℹ️"
        case warning = "⚠️"
        case error = "❌"
        case success = "✅"
        case network = "🌐"
        case performance = "⚡"

        /// 级别优先级（数字越大优先级越高）
        var priority: Int {
            switch self {
            case .debug: return 0
            case .info: return 1
            case .warning: return 2
            case .error: return 3
            case .success: return 1
            case .network: return 1
            case .performance: return 1
            }
        }

        /// 系统日志类型
        var osLogType: OSLogType {
            switch self {
            case .debug: return .debug
            case .info: return .info
            case .warning: return .default
            case .error: return .error
            case .success: return .info
            case .network: return .info
            case .performance: return .info
            }
        }
    }

    // MARK: - Configuration

    /// 日志配置（根据环境自动配置）
    private static var config = LogConfig.forCurrentEnvironment()

    /// 系统日志对象
    private static let osLog = OSLog(subsystem: AppConstants.bundleIdentifier, category: "Application")

    // MARK: - Public Methods

    /// 调试日志（仅在 DEBUG 模式下有效）
    static func debug(_ message: @autoclosure () -> Any, file: String = #file, function: String = #function, line: Int = #line) {
        #if DEBUG
        log(message(), level: .debug, file: file, function: function, line: line)
        #endif
    }

    /// 信息日志
    static func info(_ message: @autoclosure () -> Any, file: String = #file, function: String = #function, line: Int = #line) {
        log(message(), level: .info, file: file, function: function, line: line)
    }

    /// 警告日志
    static func warning(_ message: @autoclosure () -> Any, file: String = #file, function: String = #function, line: Int = #line) {
        log(message(), level: .warning, file: file, function: function, line: line)
    }

    /// 错误日志
    static func error(_ message: @autoclosure () -> Any, file: String = #file, function: String = #function, line: Int = #line) {
        log(message(), level: .error, file: file, function: function, line: line)
    }

    /// 成功日志
    static func success(_ message: @autoclosure () -> Any, file: String = #file, function: String = #function, line: Int = #line) {
        log(message(), level: .success, file: file, function: function, line: line)
    }

    /// 网络日志
    static func network(_ message: @autoclosure () -> Any, file: String = #file, function: String = #function, line: Int = #line) {
        log(message(), level: .network, file: file, function: function, line: line)
    }

    /// 性能日志（在生产环境中可选择性启用）
    static func performance(_ message: @autoclosure () -> Any, file: String = #file, function: String = #function, line: Int = #line) {
        #if DEBUG
        log(message(), level: .performance, file: file, function: function, line: line)
        #endif
    }

    /// 配置日志系统
    ///
    /// - Parameter config: 日志配置
    static func configure(_ config: LogConfig) {
        self.config = config
    }

    /// 显示当前环境和日志配置信息
    static func showEnvironmentInfo() {
        let env = EnvironmentConfig.current
        info("🚀 应用启动 - 环境: \(env.rawValue.uppercased())")
        info("📊 日志配置:")
        info("   • 最小级别: \(config.minimumLevel.rawValue)")
        info("   • 控制台输出: \(config.enableConsoleOutput ? "✅" : "❌")")
        info("   • 系统日志: \(config.enableSystemLog ? "✅" : "❌")")
        info("   • 详细网络日志: \(config.enableDetailedNetworkLogs ? "✅" : "❌")")

        switch env {
        case .production:
            warning("⚠️ 生产环境 - 详细日志已禁用")
        case .development:
            success("🛠️ 开发环境 - 详细日志已启用")
        case .mock:
            info("🎭 Mock环境 - 详细日志已启用")
        }
    }



    // MARK: - Private Methods

    /// 核心日志记录方法
    private static func log(_ message: Any, level: Level, file: String, function: String, line: Int) {
        // 检查日志级别过滤
        guard level.priority >= config.minimumLevel.priority else { return }

        let timestamp = config.dateFormatter.string(from: Date())

        // 统一的日志格式：[级别] [时间] 消息
        let logMessage = "\(level.rawValue) [\(timestamp)] \(message)"

        // 控制台输出
        if config.enableConsoleOutput {
            print(logMessage)
        }

        // 系统日志
        if config.enableSystemLog {
            os_log("%{public}@", log: osLog, type: level.osLogType, logMessage)
        }
    }
}

// MARK: - Log Configuration

/// 日志配置结构体
struct LogConfig {
    /// 最小日志级别
    var minimumLevel: Log.Level

    /// 是否启用控制台输出
    var enableConsoleOutput: Bool

    /// 是否启用系统日志
    var enableSystemLog: Bool

    /// 是否启用详细网络日志
    var enableDetailedNetworkLogs: Bool

    /// 日期格式化器
    var dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter
    }()

    /// 根据当前环境自动创建配置
    static func forCurrentEnvironment() -> LogConfig {
        switch EnvironmentConfig.current {
        case .production:
            return LogConfig(
                minimumLevel: .warning,
                enableConsoleOutput: false,
                enableSystemLog: true,
                enableDetailedNetworkLogs: false
            )
        case .development:
            return LogConfig(
                minimumLevel: .debug,
                enableConsoleOutput: true,
                enableSystemLog: false,
                enableDetailedNetworkLogs: false
            )
        case .mock:
            return LogConfig(
                minimumLevel: .debug,
                enableConsoleOutput: true,
                enableSystemLog: false,
                enableDetailedNetworkLogs: false
            )
        }
    }

    /// 手动初始化配置
    init(
        minimumLevel: Log.Level,
        enableConsoleOutput: Bool,
        enableSystemLog: Bool,
        enableDetailedNetworkLogs: Bool
    ) {
        self.minimumLevel = minimumLevel
        self.enableConsoleOutput = enableConsoleOutput
        self.enableSystemLog = enableSystemLog
        self.enableDetailedNetworkLogs = enableDetailedNetworkLogs
    }
}

// MARK: - Performance Monitoring

extension Log {
    /// 性能监控工具
    struct PerformanceMonitor {
        private let startTime: CFAbsoluteTime
        private let operation: String

        init(_ operation: String) {
            self.operation = operation
            self.startTime = CFAbsoluteTimeGetCurrent()
            #if DEBUG
            Log.performance("开始 \(operation)")
            #endif
        }

        func end() {
            #if DEBUG
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            Log.performance("完成 \(operation) - 耗时: \(String(format: "%.2f", duration * 1000))ms")
            #endif
        }
    }

    /// 监控代码块执行时间
    ///
    /// - Parameters:
    ///   - operation: 操作名称
    ///   - block: 要监控的代码块
    /// - Returns: 代码块的返回值
    static func measure<T>(_ operation: String, block: () throws -> T) rethrows -> T {
        let monitor = PerformanceMonitor(operation)
        defer { monitor.end() }
        return try block()
    }

    /// 异步监控代码块执行时间
    ///
    /// - Parameters:
    ///   - operation: 操作名称
    ///   - block: 要监控的异步代码块
    /// - Returns: 代码块的返回值
    static func measureAsync<T>(_ operation: String, block: () async throws -> T) async rethrows -> T {
        let monitor = PerformanceMonitor(operation)
        defer { monitor.end() }
        return try await block()
    }
}

// MARK: - Network Logging Extensions

extension Log {

    /// 网络请求日志（带详细信息）
    ///
    /// 根据环境和配置控制详细信息显示：
    /// - Production: 只显示基础信息
    /// - Development/Mock: 根据全局配置和强制标志决定
    ///
    /// - Parameters:
    ///   - message: 基础日志消息
    ///   - forceDetail: 强制显示详情，忽略全局配置（用于单独调试特定接口）
    ///   - details: 详细信息闭包
    static func networkDetail(
        _ message: @autoclosure () -> Any,
        forceDetail: Bool = false,
        details: () -> String = { "" }
    ) {
        network(message())

        // 决定是否显示详细信息：强制显示 或 全局配置启用
        let shouldShowDetail = forceDetail || config.enableDetailedNetworkLogs

        if shouldShowDetail {
            let detailString = details()
            if !detailString.isEmpty {
                if forceDetail && !config.enableDetailedNetworkLogs {
                    // 强制显示时添加特殊标记
                    debug("🔍 [强制详情] ==================== 单独调试 ====================")
                    debug(detailString)
                    debug("🔍 ================================================")
                } else {
                    debug(detailString)
                }
            }
        }
    }

    /// 获取当前是否启用详细网络日志
    static var isDetailedNetworkLoggingEnabled: Bool {
        return config.enableDetailedNetworkLogs
    }

    /// 临时关闭详细网络日志（仅在开发环境有效）
    static func disableDetailedNetworkLogs() {
        guard EnvironmentConfig.current != .production else {
            warning("⚠️ 生产环境无法修改日志配置")
            return
        }

        config.enableDetailedNetworkLogs = false
        info("🔇 详细网络日志已临时关闭（使用 forceDetail: true 可单独开启特定接口）")
    }

    /// 重新启用详细网络日志（仅在开发环境有效）
    static func enableDetailedNetworkLogs() {
        guard EnvironmentConfig.current != .production else {
            warning("⚠️ 生产环境无法修改日志配置")
            return
        }

        config.enableDetailedNetworkLogs = true
        info("🔊 详细网络日志已重新启用")
    }
}
