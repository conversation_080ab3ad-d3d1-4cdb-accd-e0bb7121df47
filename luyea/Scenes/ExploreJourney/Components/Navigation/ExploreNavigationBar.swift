import SwiftUI

// MARK: - Constants

/// 导航栏样式常量
private enum NavigationBarConstants {
    static let verticalPadding: CGFloat = 8
    static let tabSpacing: CGFloat = 20
    static let animationDuration: Double = 0.25
    static let textAnimationDuration: Double = 0.2
    static let underlineHeight: CGFloat = 1.5
    static let underlineOffset: CGFloat = 6
}

/// 探索旅途导航栏组件
///
/// 提供发现和周边功能之间的切换导航。
/// 从原有的ExploreJourneyView中提取出来，实现组件化。
struct ExploreNavigationBar: View {

    // MARK: - Bindings

    /// 当前选中的标签页
    @Binding var selectedTab: DiscoverJourneyTab

    // MARK: - Callbacks

    /// 标签页切换回调
    let onTabChange: (DiscoverJourneyTab) -> Void

    // MARK: - Body

    var body: some View {
        // 标签页切换器
        tabSwitcher
            .padding(.vertical, NavigationBarConstants.verticalPadding)
    }

    // MARK: - Components

    /// 标签页切换器
    private var tabSwitcher: some View {
        HStack(spacing: NavigationBarConstants.tabSpacing) {
            // 发现标签
            tabButton(for: .discover)

            // 周边标签
            tabButton(for: .nearby)
        }
    }

    /// 创建标签按钮
    private func tabButton(for tab: DiscoverJourneyTab) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: NavigationBarConstants.animationDuration)) {
                selectedTab = tab
                onTabChange(tab)
            }
        }) {
            Text(tab.rawValue)
                .font(.headline)
                .foregroundColor(selectedTab == tab ? .primary : .secondary)
                .fontWeight(selectedTab == tab ? .semibold : .regular)
                .animation(.easeInOut(duration: NavigationBarConstants.textAnimationDuration), value: selectedTab)
                .overlay(
                    Rectangle()
                        .frame(height: NavigationBarConstants.underlineHeight)
                        .foregroundColor(selectedTab == tab ? Color.primary.opacity(0.7) : .clear)
                        .offset(y: NavigationBarConstants.underlineOffset)
                        .animation(.easeInOut(duration: NavigationBarConstants.animationDuration), value: selectedTab),
                    alignment: .bottom
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    ExploreNavigationBar(
        selectedTab: .constant(.discover),
        onTabChange: { _ in }
    )
    .padding()
}
