import Foundation

/// 位置数据服务
///
/// 负责提供位置相关的静态数据，包括热门城市、搜索数据等。
/// 采用静态属性方式，提高性能和减少内存占用。
struct LocationDataService {
    
    // MARK: - Popular Cities
    
    /// 热门城市列表
    static let popularCities: [String] = [
        "北京", "上海", "广州",
        "深圳", "杭州", "南京",
        "成都", "重庆", "西安",
        "武汉", "天津", "苏州"
    ]
    
    // MARK: - All Cities
    
    /// 所有城市列表（用于搜索）
    static let allCities: [String] = [
        // 直辖市
        "北京", "上海", "天津", "重庆",
        
        // 省会城市
        "广州", "深圳", "杭州", "南京", "成都", "西安", "武汉", "沈阳",
        "长春", "哈尔滨", "石家庄", "太原", "呼和浩特", "济南", "郑州",
        "合肥", "南昌", "长沙", "福州", "南宁", "海口", "昆明", "贵阳",
        "拉萨", "兰州", "西宁", "银川", "乌鲁木齐",
        
        // 重要城市
        "苏州", "青岛", "大连", "宁波", "厦门", "无锡", "佛山", "东莞",
        "泉州", "温州", "嘉兴", "台州", "金华", "绍兴", "湖州", "衢州",
        "舟山", "丽水", "常州", "徐州", "南通", "连云港", "淮安", "盐城",
        "扬州", "镇江", "泰州", "宿迁", "芜湖", "蚌埠", "淮南", "马鞍山",
        "淮北", "铜陵", "安庆", "黄山", "滁州", "阜阳", "宿州", "六安",
        "亳州", "池州", "宣城",
        
        // 特别行政区
        "香港", "澳门"
    ]
    
    // MARK: - International Cities
    
    /// 国际城市列表
    static let internationalCities: [String] = [
        "东京", "大阪", "京都", "名古屋", "福冈",
        "首尔", "釜山", "济州",
        "曼谷", "清迈", "普吉",
        "新加坡",
        "吉隆坡", "槟城",
        "雅加达", "巴厘岛",
        "马尼拉", "宿务",
        "胡志明市", "河内", "岘港",
        "金边", "暹粒",
        "仰光", "曼德勒",
        "万象",
        "斯里巴加湾",
        "帝力"
    ]
    
    // MARK: - Search Methods
    
    /// 搜索城市
    /// - Parameter query: 搜索关键词
    /// - Returns: 匹配的城市列表
    static func searchCities(query: String) -> [String] {
        guard !query.isEmpty else { return [] }
        
        let allSearchableCities = allCities + internationalCities
        
        return allSearchableCities.filter { city in
            city.localizedCaseInsensitiveContains(query)
        }.sorted { city1, city2 in
            // 优先显示以搜索词开头的城市
            let city1StartsWithQuery = city1.localizedCaseInsensitiveHasPrefix(query)
            let city2StartsWithQuery = city2.localizedCaseInsensitiveHasPrefix(query)
            
            if city1StartsWithQuery && !city2StartsWithQuery {
                return true
            } else if !city1StartsWithQuery && city2StartsWithQuery {
                return false
            } else {
                return city1 < city2
            }
        }
    }
    
    /// 获取搜索建议
    /// - Parameter query: 搜索关键词
    /// - Returns: 搜索建议列表
    static func getSearchSuggestions(query: String) -> [String] {
        let results = searchCities(query: query)
        return Array(results.prefix(10)) // 限制返回10个结果
    }
    
    // MARK: - Helper Methods
    
    /// 检查是否为热门城市
    /// - Parameter city: 城市名称
    /// - Returns: 是否为热门城市
    static func isPopularCity(_ city: String) -> Bool {
        return popularCities.contains(city)
    }
    
    /// 检查是否为国际城市
    /// - Parameter city: 城市名称
    /// - Returns: 是否为国际城市
    static func isInternationalCity(_ city: String) -> Bool {
        return internationalCities.contains(city)
    }
    
    /// 获取城市类型
    /// - Parameter city: 城市名称
    /// - Returns: 城市类型描述
    static func getCityType(_ city: String) -> String {
        if isPopularCity(city) {
            return "热门城市"
        } else if isInternationalCity(city) {
            return "国际城市"
        } else {
            return "国内城市"
        }
    }
}

// MARK: - String Extensions

private extension String {
    func localizedCaseInsensitiveHasPrefix(_ prefix: String) -> Bool {
        return self.lowercased().hasPrefix(prefix.lowercased())
    }
}
