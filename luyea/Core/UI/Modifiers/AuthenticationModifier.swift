import SwiftUI

// MARK: - Authentication Requirement Modifier

/// 认证要求修饰符
///
/// 为视图添加认证检查功能，当用户未登录时自动显示登录界面。
/// 提供便捷的认证拦截机制，支持多种使用场景。
struct AuthenticationRequirementModifier: ViewModifier {
    
    // MARK: - Properties
    
    /// 认证管理器
    @Environment(\.authenticationManager) private var authManager
    
    /// 认证成功后执行的操作
    private let action: () -> Void
    
    /// 是否在未认证时禁用交互
    private let disableWhenUnauthenticated: Bool
    
    /// 未认证时的提示信息
    private let unauthenticatedMessage: String?
    
    // MARK: - Initialization
    
    init(
        action: @escaping () -> Void = {},
        disableWhenUnauthenticated: Bool = false,
        unauthenticatedMessage: String? = nil
    ) {
        self.action = action
        self.disableWhenUnauthenticated = disableWhenUnauthenticated
        self.unauthenticatedMessage = unauthenticatedMessage
    }
    
    // MARK: - Body
    
    func body(content: Content) -> some View {
        content
            .disabled(disableWhenUnauthenticated && !authManager.isAuthenticated)
            .opacity(disableWhenUnauthenticated && !authManager.isAuthenticated ? 0.6 : 1.0)
            .onTapGesture {
                authManager.requireAuthentication(action: action)
            }
            .overlay(alignment: .center) {
                if disableWhenUnauthenticated && !authManager.isAuthenticated {
                    if let message = unauthenticatedMessage {
                        Text(message)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 8)
                    }
                }
            }
    }
}

// MARK: - Authentication Button Modifier

/// 认证按钮修饰符
///
/// 专门为按钮设计的认证修饰符，提供更好的用户体验。
/// 支持加载状态、错误状态和成功状态的视觉反馈。
struct AuthenticationButtonModifier: ViewModifier {
    
    // MARK: - Properties
    
    /// 认证管理器
    @Environment(\.authenticationManager) private var authManager
    
    /// 按钮点击后的操作
    private let action: () -> Void
    
    /// 是否显示加载状态
    private let showLoadingState: Bool
    
    /// 未认证时的按钮文本
    private let unauthenticatedText: String
    
    /// 已认证时的按钮文本
    private let authenticatedText: String
    
    // MARK: - Initialization
    
    init(
        action: @escaping () -> Void,
        showLoadingState: Bool = true,
        unauthenticatedText: String = "登录后继续",
        authenticatedText: String = "继续"
    ) {
        self.action = action
        self.showLoadingState = showLoadingState
        self.unauthenticatedText = unauthenticatedText
        self.authenticatedText = authenticatedText
    }
    
    // MARK: - Body
    
    func body(content: Content) -> some View {
        Button {
            authManager.requireAuthentication(action: action)
        } label: {
            HStack {
                if showLoadingState && authManager.isAuthenticating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                }
                
                Text(authManager.isAuthenticated ? authenticatedText : unauthenticatedText)
            }
        }
        .disabled(showLoadingState && authManager.isAuthenticating)
        .animation(.easeInOut(duration: 0.2), value: authManager.isAuthenticating)
    }
}

// MARK: - Authentication Guard Modifier

/// 认证守卫修饰符
///
/// 为整个视图或视图层次结构提供认证保护。
/// 当用户未认证时，显示占位符内容或重定向到登录页面。
struct AuthenticationGuardModifier<UnauthenticatedContent: View>: ViewModifier {
    
    // MARK: - Properties
    
    /// 认证管理器
    @Environment(\.authenticationManager) private var authManager
    
    /// 未认证时显示的内容
    private let unauthenticatedContent: () -> UnauthenticatedContent
    
    /// 是否自动显示登录界面
    private let autoShowLogin: Bool
    
    // MARK: - Initialization
    
    init(
        autoShowLogin: Bool = true,
        @ViewBuilder unauthenticatedContent: @escaping () -> UnauthenticatedContent
    ) {
        self.autoShowLogin = autoShowLogin
        self.unauthenticatedContent = unauthenticatedContent
    }
    
    // MARK: - Body
    
    func body(content: Content) -> some View {
        Group {
            if authManager.isAuthenticated {
                content
            } else {
                unauthenticatedContent()
                    .onAppear {
                        if autoShowLogin {
                            authManager.presentLogin()
                        }
                    }
            }
        }
    }
}

// MARK: - View Extensions

extension View {
    
    /// 要求用户认证后才能执行操作
    /// - Parameters:
    ///   - action: 认证成功后执行的操作
    ///   - disableWhenUnauthenticated: 是否在未认证时禁用交互
    ///   - message: 未认证时的提示信息
    /// - Returns: 应用了认证要求的视图
    func requiresAuthentication(
        action: @escaping () -> Void = {},
        disableWhenUnauthenticated: Bool = false,
        message: String? = nil
    ) -> some View {
        modifier(AuthenticationRequirementModifier(
            action: action,
            disableWhenUnauthenticated: disableWhenUnauthenticated,
            unauthenticatedMessage: message
        ))
    }
    
    /// 创建认证按钮
    /// - Parameters:
    ///   - action: 按钮点击后的操作
    ///   - showLoadingState: 是否显示加载状态
    ///   - unauthenticatedText: 未认证时的按钮文本
    ///   - authenticatedText: 已认证时的按钮文本
    /// - Returns: 认证按钮视图
    func authenticationButton(
        action: @escaping () -> Void,
        showLoadingState: Bool = true,
        unauthenticatedText: String = "登录后继续",
        authenticatedText: String = "继续"
    ) -> some View {
        modifier(AuthenticationButtonModifier(
            action: action,
            showLoadingState: showLoadingState,
            unauthenticatedText: unauthenticatedText,
            authenticatedText: authenticatedText
        ))
    }
    
    /// 为视图添加认证守卫
    /// - Parameters:
    ///   - autoShowLogin: 是否自动显示登录界面
    ///   - unauthenticatedContent: 未认证时显示的内容
    /// - Returns: 受认证保护的视图
    func authenticationGuard<UnauthenticatedContent: View>(
        autoShowLogin: Bool = true,
        @ViewBuilder unauthenticatedContent: @escaping () -> UnauthenticatedContent
    ) -> some View {
        modifier(AuthenticationGuardModifier(
            autoShowLogin: autoShowLogin,
            unauthenticatedContent: unauthenticatedContent
        ))
    }
    
    /// 为视图添加简单的认证守卫（使用默认的未认证内容）
    /// - Parameter autoShowLogin: 是否自动显示登录界面
    /// - Returns: 受认证保护的视图
    func requiresAuthentication(autoShowLogin: Bool = true) -> some View {
        authenticationGuard(autoShowLogin: autoShowLogin) {
            VStack(spacing: 16) {
                Image(systemName: "person.circle")
                    .font(.system(size: 60))
                    .foregroundColor(.secondary)
                
                Text("需要登录")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("请登录后继续使用此功能")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Button("立即登录") {
                    AuthenticationManager.shared.presentLogin()
                }
                .buttonStyle(.borderedProminent)
            }
            .padding()
        }
    }
}

// MARK: - Authentication State View Modifier

/// 认证状态视图修饰符
///
/// 根据认证状态显示不同的视图内容，提供完整的状态管理。
struct AuthenticationStateViewModifier<AuthenticatedContent: View, UnauthenticatedContent: View, LoadingContent: View, ErrorContent: View>: ViewModifier {

    // MARK: - Properties

    @Environment(\.authenticationManager) private var authManager

    private let authenticatedContent: (AuthenticationModels.User) -> AuthenticatedContent
    private let unauthenticatedContent: () -> UnauthenticatedContent
    private let loadingContent: () -> LoadingContent
    private let errorContent: (AuthenticationModels.AuthError) -> ErrorContent
    
    // MARK: - Initialization
    
    init(
        authenticatedContent: @escaping (AuthenticationModels.User) -> AuthenticatedContent,
        unauthenticatedContent: @escaping () -> UnauthenticatedContent,
        loadingContent: @escaping () -> LoadingContent,
        errorContent: @escaping (AuthenticationModels.AuthError) -> ErrorContent
    ) {
        self.authenticatedContent = authenticatedContent
        self.unauthenticatedContent = unauthenticatedContent
        self.loadingContent = loadingContent
        self.errorContent = errorContent
    }
    
    // MARK: - Body
    
    func body(content: Content) -> some View {
        Group {
            switch authManager.authenticationState {
            case .authenticated(let user):
                authenticatedContent(user)
            case .unauthenticated:
                unauthenticatedContent()
            case .authenticating:
                loadingContent()
            case .error(let error):
                errorContent(error)
            }
        }
    }
}

extension View {
    
    /// 根据认证状态显示不同内容
    func authenticationStateView<AuthenticatedContent: View, UnauthenticatedContent: View, LoadingContent: View, ErrorContent: View>(
        @ViewBuilder authenticated: @escaping (AuthenticationModels.User) -> AuthenticatedContent,
        @ViewBuilder unauthenticated: @escaping () -> UnauthenticatedContent,
        @ViewBuilder loading: @escaping () -> LoadingContent,
        @ViewBuilder error: @escaping (AuthenticationModels.AuthError) -> ErrorContent
    ) -> some View {
        modifier(AuthenticationStateViewModifier(
            authenticatedContent: authenticated,
            unauthenticatedContent: unauthenticated,
            loadingContent: loading,
            errorContent: error
        ))
    }
}
