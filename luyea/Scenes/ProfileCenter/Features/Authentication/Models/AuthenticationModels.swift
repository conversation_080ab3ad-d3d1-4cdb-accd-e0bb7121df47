import Foundation

/// Authentication功能数据模型命名空间
enum AuthenticationModels {
    
    // MARK: - 用户认证信息
    
    /// 用户基本信息
    struct User: Codable, Identifiable, Equatable {
        let id: String
        let phoneNumber: String
        let nickname: String?
        let avatar: String?
        let email: String?
        let createdAt: Date
        let updatedAt: Date
        let isVerified: Bool
        
        /// 显示名称（优先使用昵称，否则使用手机号）
        var displayName: String {
            return nickname ?? phoneNumber.maskedPhoneNumber
        }
    }
    
    // MARK: - 认证状态
    
    /// 认证状态枚举
    enum AuthState: Equatable {
        case unauthenticated
        case authenticating
        case authenticated(User)
        case error(AuthError)
    }
    
    /// 验证码发送状态
    enum CodeSendState: Equatable {
        case idle
        case sending
        case sent(expiresAt: Date)
        case failed(AuthError)
        case cooldown(remainingSeconds: Int)
        case canResend // 倒计时结束后，可以重新发送

        /// 是否为失败状态
        var isFailed: Bool {
            if case .failed = self {
                return true
            }
            return false
        }

        /// 是否可以发送验证码
        var canSend: Bool {
            switch self {
            case .idle, .failed, .canResend:
                return true
            case .sending, .sent, .cooldown:
                return false
            }
        }
    }
    
    // MARK: - 请求模型
    
    /// 登录请求
    struct LoginRequest: Codable {
        let phoneNumber: String
        let verificationCode: String
        let deviceId: String
        let platform: String = "iOS"

        enum CodingKeys: String, CodingKey {
            case phoneNumber
            case verificationCode
            case deviceId
            case platform
        }
    }
    
    /// 验证码请求
    struct VerificationCodeRequest: Codable {
        let phoneNumber: String
        let type: CodeType
        
        enum CodeType: String, Codable {
            case login = "login"
            case register = "register"
            case resetPassword = "reset_password"
        }
    }
    
    // MARK: - 第三方登录相关
    // 注意：微信登录和Apple登录通过SDK实现，不需要请求模型
    // TODO: 如需要，可在此处添加第三方登录相关的数据模型
    
    // MARK: - 响应模型
    
    /// 登录响应
    struct LoginResponse: Codable {
        let user: User
        let accessToken: String
        let refreshToken: String
        let expiresIn: TimeInterval
    }
    
    /// 验证码响应
    struct VerificationCodeResponse: Codable {
        let success: Bool
        let message: String
        let expiresIn: TimeInterval
    }
    
    // MARK: - 错误类型
    
    /// 认证错误枚举
    enum AuthError: Error, LocalizedError, Equatable {
        case invalidPhoneNumber
        case invalidVerificationCode
        case codeSendFailed
        case loginFailed(String)
        case networkError
        case unknownError
        
        var errorDescription: String? {
            switch self {
            case .invalidPhoneNumber:
                return "请输入正确的手机号码"
            case .invalidVerificationCode:
                return "请输入正确的验证码"
            case .codeSendFailed:
                return "验证码发送失败，请重试"
            case .loginFailed(let message):
                return message
            case .networkError:
                return "网络连接失败，请检查网络设置"
            case .unknownError:
                return "未知错误，请重试"
            }
        }
        
        var localizedDescription: String {
            return errorDescription ?? "未知错误"
        }
    }
}

// MARK: - 扩展方法

extension String {
    /// 手机号脱敏显示
    var maskedPhoneNumber: String {
        guard self.count == 11 else { return self }
        let start = self.prefix(3)
        let end = self.suffix(4)
        return "\(start)****\(end)"
    }
    
    /// 验证验证码格式
    var isValidVerificationCode: Bool {
        return self.count == AuthenticationConstants.Validation.verificationCodeLength && 
               self.allSatisfy { $0.isNumber }
    }
}
