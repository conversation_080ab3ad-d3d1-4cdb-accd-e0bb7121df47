import SwiftUI

/// 发现页卡片骨架屏组件
struct DiscoverCardSkeletonView: View {

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            imageSkeletonSection
            contentSkeletonSection
        }
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 5)) // 发现卡片圆角
        .shadow(
            color: Color.primary.opacity(0.07), // 发现卡片阴影透明度
            radius: 6, // 发现卡片阴影半径
            x: 0,
            y: 2
        )
        .shimmer(
            isActive: true,
            duration: 1.5,
            delay: Double.random(in: 0...0.3)
        )
    }

    // MARK: - 图片骨架区域（简化设计）

    private var imageSkeletonSection: some View {
        Rectangle()
            .fill(Color(.systemGray6).opacity(0.4))
            .aspectRatio(4/3, contentMode: .fill)
            .frame(maxWidth: .infinity)
            .cornerRadius(5, corners: [.topLeft, .topRight]) // 发现卡片圆角
            .padding(.bottom, 8)
    }

    // 移除复杂的标签组件

    // MARK: - 内容骨架区域（简化设计）

    private var contentSkeletonSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题骨架（两行）
            titleSkeleton

            // 描述骨架（一行）
            descriptionSkeleton
        }
        .padding(.horizontal, 12)
        .padding(.bottom, 12)
    }

    // MARK: - 标题骨架

    private var titleSkeleton: some View {
        VStack(alignment: .leading, spacing: 4) {
            // 第一行标题
            Rectangle()
                .fill(Color(.systemGray5))
                .frame(height: 16)
                .frame(maxWidth: .infinity)
                .cornerRadius(3)

            // 第二行标题（较短）
            Rectangle()
                .fill(Color(.systemGray5))
                .frame(height: 16)
                .frame(maxWidth: titleSecondLineWidth)
                .cornerRadius(3)
        }
    }

    // MARK: - 描述骨架

    private var descriptionSkeleton: some View {
        Rectangle()
            .fill(Color(.systemGray6))
            .frame(height: 14)
            .frame(maxWidth: descriptionLineWidth)
            .cornerRadius(3)
    }

    // MARK: - 计算属性

    /// 标题第二行宽度
    private var titleSecondLineWidth: CGFloat {
        let ratios: [CGFloat] = [0.6, 0.7, 0.8]
        return ratios.randomElement() ?? 0.7
    }

    /// 描述行宽度
    private var descriptionLineWidth: CGFloat {
        let ratios: [CGFloat] = [0.8, 0.9, 1.0]
        return ratios.randomElement() ?? 0.9
    }
}

#Preview {
    LazyVGrid(
        columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2),
        spacing: 12
    ) {
        ForEach(0..<6, id: \.self) { _ in
            DiscoverCardSkeletonView()
        }
    }
    .padding()
    .background(Color(.systemGroupedBackground))
} 
