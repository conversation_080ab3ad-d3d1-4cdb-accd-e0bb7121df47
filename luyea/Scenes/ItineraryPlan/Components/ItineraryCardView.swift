import SwiftUI

/// 显示单个行程信息的卡片视图，支持展开/折叠以显示详情。
///
/// 功能特性：
/// - 支持展开/折叠显示详细信息
/// - 根据行程状态显示不同的操作按钮
/// - 支持悬停效果和平滑动画
/// - 集成编辑和查看功能
struct ItineraryCardView: View {

    // MARK: - Constants

    /// 图片相关常量
    private enum ImageConstants {
        static let width: CGFloat = 120
        static let height: CGFloat = 80
        static let cornerRadius: CGFloat = 8
    }

    // MARK: - Properties

    /// 行程数据模型
    let itinerary: ItineraryModel

    /// 是否处于展开状态
    var isExpanded: Bool

    /// 点击卡片的回调
    var onTap: () -> Void

    /// 是否显示信息按钮
    var showInfoButton: Bool = false

    // MARK: - State

    @State private var isHovered = false
    @State private var showEditSheet = false
    @State private var showTips = false
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            mainCardContent
            
            if isExpanded {
                detailView
            }
        }
    }
    
    // MARK: - Private Views
    
    /// 主卡片内容，包含封面、标题、日期等核心信息
    private var mainCardContent: some View {
        HStack(alignment: .top, spacing: 14) {
            // 左侧：封面图片 - 使用overlay方式匹配高度，保持圆角
            Rectangle()
                .fill(Color.clear)
                .frame(width: ImageConstants.width)
                .overlay(
                    coverImageView
                        .frame(maxHeight: 120) // 最大高度限制
                )
                .clipShape(RoundedRectangle(cornerRadius: ImageConstants.cornerRadius))

            // 中间：行程信息 - 统一间距布局
            VStack(alignment: .leading, spacing: 6) {
                // 标题和往返标记
                HStack(alignment: .center, spacing: 8) {
                    Text(itinerary.title)
                        .font(.system(size: 16, weight: .semibold, design: .default))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                        .truncationMode(.tail)
                        .layoutPriority(0) // 标题可以被压缩

                    // 往返标记
                    if let isRoundTrip = itinerary.isRoundTrip, isRoundTrip {
                        roundTripBadge
                            .layoutPriority(1) // 往返标记优先保持完整
                    }
                }

                destinationView

                dateInfoView

                statusView
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 右侧：编辑按钮（展开时显示）
            if isExpanded {
                editButton
                    .padding(.top, 2)
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 10)
        .contentShape(Rectangle())
        .onTapGesture {
            // 使用异步操作，确保点击响应不被阻塞
            Task { @MainActor in
                handleCardTap()
            }
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isHovered)
        .transition(.asymmetric(
            insertion: .move(edge: .bottom).combined(with: .opacity),
            removal: .opacity
        ))
        .background(Color(uiColor: .systemBackground).opacity(0.95))
        .cornerRadius(
            ItineraryPlanConstants.Layout.cardCornerRadius,
            corners: isExpanded ? [.topLeft, .topRight] : [.allCorners]
        )
        .cornerRadius(
            isExpanded ? 0 : ItineraryPlanConstants.Layout.cardCornerRadius,
            corners: [.bottomLeft, .bottomRight]
        )
        .overlay(cardBorderOverlay)
        .animation(ItineraryPlanConstants.Animation.cardExpansion, value: isExpanded)
    }




    /// 目的地标签视图 - 优化版本
    private var destinationView: some View {
        HStack(spacing: 4) {
            Image(systemName: "location.circle.fill")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.blue.opacity(0.8))

            Text(itinerary.destination)
                .font(.system(size: 13, weight: .medium, design: .default))
                .foregroundColor(.blue)
                .lineLimit(1)
                .truncationMode(.tail)
        }
    }

    /// 卡片边框覆盖层
    private var cardBorderOverlay: some View {
        UnifiedRoundedCorner(
            radius: ItineraryPlanConstants.Layout.cardCornerRadius,
            corners: isExpanded ? [.topLeft, .topRight] : [.allCorners]
        )
        .stroke(Color.gray.opacity(0.18), lineWidth: 0.5)
    }

    /// 处理卡片点击事件
    private func handleCardTap() {
        if showTips {
            showTips = false
        }
        onTap()
    }
    
    /// 封面图片视图 - 自适应高度，正确的缩放裁剪
    @ViewBuilder
    private var coverImageView: some View {
        CachedAsyncImage(
            url: URL(string: itinerary.coverImage ?? "")
        ) { image in
            // 加载成功状态
            image
                .resizable()
                .scaledToFill()
                .frame(width: ImageConstants.width)
                .clipped()
                .transition(.scale)
        } placeholder: {
            // 优化的占位符，减少渲染压力
            optimizedPlaceholderView
        } errorView: {
            // 添加错误状态视图
            errorPlaceholderView
        }
        .compositingGroup() // 优化渲染性能
    }

    /// 占位图片视图
    private var placeholderImageView: some View {
        Color.gray.opacity(0.2)
            .frame(width: ImageConstants.width)
    }

    /// 优化的占位符视图 - 自适应高度，圆角由外部容器控制
    private var optimizedPlaceholderView: some View {
        Rectangle()
            .fill(Color.gray.opacity(0.12))
            .frame(width: ImageConstants.width)
            .overlay(
                Image(systemName: "photo")
                    .font(.system(size: 20, weight: .light))
                    .foregroundColor(.gray.opacity(0.5))
            )
            .shimmer()
    }

    /// 错误状态占位符 - 自适应高度，圆角由外部容器控制
    private var errorPlaceholderView: some View {
        ImageErrorView(showText: true)
            .frame(width: ImageConstants.width)
    }
    
    /// 日期信息视图（天数 + 具体日期）- 支持未规划时间的情况
    private var dateInfoView: some View {
        HStack(spacing: 6) {
            // 行程天数标签或规划状态
            if isDatePlanned {
                Text("\(itinerary.days)天")
                    .font(.system(size: 12, weight: .semibold, design: .rounded))
                    .foregroundColor(.orange)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.orange.opacity(0.12))
                    .clipShape(RoundedRectangle(cornerRadius: 4))
            } else {
                Text("待规划")
                    .font(.system(size: 12, weight: .semibold, design: .rounded))
                    .foregroundColor(.gray)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.gray.opacity(0.12))
                    .clipShape(RoundedRectangle(cornerRadius: 4))
            }

            // 具体日期范围或提示文本
            Text(dateDisplayText)
                .font(.system(size: 12, weight: .regular, design: .default))
                .foregroundColor(.secondary)
                .lineLimit(1)
                .truncationMode(.tail)
        }
    }

    /// 判断是否已规划出行时间
    private var isDatePlanned: Bool {
        // 检查dateRange是否为空或只包含空白字符
        !itinerary.dateRange.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    /// 日期显示文本
    private var dateDisplayText: String {
        if isDatePlanned {
            return itinerary.dateRange
        } else {
            return "出行时间待确定"
        }
    }

    /// 往返标记
    private var roundTripBadge: some View {
        Text("往返")
            .font(.system(size: 10, weight: .medium))
            .foregroundColor(.blue)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.blue.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: 4))
    }

    /// 行程状态标签视图 - 优化版本
    private var statusView: some View {
        HStack(spacing: 4) {
            // 状态指示点
            Circle()
                .fill(statusColor)
                .frame(width: 6, height: 6)

            Text(itinerary.status)
                .font(.system(size: 12, weight: .medium, design: .default))
                .foregroundColor(statusColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(statusColor.opacity(0.08))
        .clipShape(RoundedRectangle(cornerRadius: 6))
    }
    
    /// 编辑/查看按钮 - 统一图标大小版本
    @ViewBuilder
    private var editButton: some View {
        if itinerary.status == "已结束" {
            // 已结束的行程只提供查看
            Button(action: { /* 可定义为跳转到行程回顾页面 */ }) {
                VStack(spacing: 3) {
                    Image(systemName: "eye.circle.fill")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.blue)

                    Text("查看")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.blue)
                }
            }
            .buttonStyle(PlainButtonStyle())
            .accessibilityLabel("查看行程")

        } else {
            // 未结束的行程提供编辑
            Button(action: { showEditSheet = true }) {
                VStack(spacing: 3) {
                    Image(systemName: "pencil.circle.fill")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.blue)

                    Text("编辑")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.blue)
                }
            }
            .buttonStyle(PlainButtonStyle())
            .accessibilityLabel("编辑行程")
            .sheet(isPresented: $showEditSheet) {
                EditItinerarySheet(itinerary: itinerary)
            }
        }
    }
    
    /// 展开后的详情视图
    private var detailView: some View {
        ZStack {
            ItineraryDetailView(itinerary: itinerary)
                .background(Color(uiColor: .systemBackground).opacity(0.95))
                .bottomCornerRadius(ItineraryPlanConstants.Layout.cardCornerRadius)
                .overlay(
                    UnifiedRoundedCorner(
                        radius: ItineraryPlanConstants.Layout.cardCornerRadius,
                        corners: [.bottomLeft, .bottomRight]
                    )
                    .stroke(Color.gray.opacity(0.18), lineWidth: 0.5)
                )
                .shadow(color: Color.black.opacity(0.14), radius: 10, x: 0, y: 5)
                .offset(y: isExpanded ? 0 : -20)
                .opacity(isExpanded ? 1 : 0)
                .animation(ItineraryPlanConstants.Animation.cardExpansion, value: isExpanded)
                .modifier(ItineraryTipsAutoCloseModifier(showTips: $showTips))

            if showInfoButton {
                ItineraryInfoTipsOverlay(showTips: $showTips)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /// 根据行程状态返回不同的颜色
    private var statusColor: Color {
        switch itinerary.status {
        case "在途中":
            return ItineraryPlanConstants.StatusColors.inProgress
        case "已结束":
            return ItineraryPlanConstants.StatusColors.completed
        case "待出行":
            return ItineraryPlanConstants.StatusColors.upcoming
        default:
            return ItineraryPlanConstants.StatusColors.completed
        }
    }
}

// MARK: - Helper Components & Styles

/// 卡片内部操作按钮的统一样式
private struct CardActionButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(Color(.systemGray6))
            .foregroundColor(.blue)
            .clipShape(Capsule())
            .contentShape(Rectangle())
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

/// 编辑行程弹窗（占位）
private struct EditItinerarySheet: View {
    let itinerary: ItineraryModel
    var body: some View {
        VStack(spacing: 24) {
            Text("编辑行程（占位界面）")
                .font(.title2.bold())
                .padding(.top, 40)
            
            Image(systemName: "pencil.and.outline")
                .resizable()
                .scaledToFit()
                .frame(width: 80, height: 80)
                .foregroundColor(.blue.opacity(0.7))
            
            Text("这里将展示编辑行程的表单或流程\n\n行程ID: \(itinerary.id)")
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}
