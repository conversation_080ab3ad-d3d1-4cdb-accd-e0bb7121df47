import Foundation

// MARK: - 常量定义

/// Mock分页策略相关常量
public enum MockPaginationStrategyConstants {
    /// 默认分页大小
    static let defaultPageSize = 20
    /// 默认页码
    static let defaultPage = 1
    /// 默认排序方向
    static let defaultSortOrder = "desc"
    /// 路径分隔符
    static let pathSeparator: Character = "."
    /// 日期格式
    static let defaultDateFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
    /// 数值格式化阈值
    static let numberFormatThreshold = 1000
}

// MARK: - 错误处理

/// Mock分页策略相关错误
public enum MockPaginationStrategyError: Error, LocalizedError {
    case strategyNotFound(apiPath: String)
    case invalidFieldPath(path: String)
    case typeConversionFailed(value: Any, targetType: String)
    case configurationError(message: String)
    
    public var errorDescription: String? {
        switch self {
        case .strategyNotFound(let apiPath):
            return "未找到匹配的策略: \(apiPath)"
        case .invalidFieldPath(let path):
            return "无效的字段路径: \(path)"
        case .typeConversionFailed(let value, let targetType):
            return "类型转换失败: \(value) -> \(targetType)"
        case .configurationError(let message):
            return "配置错误: \(message)"
        }
    }
}

// MARK: - 统一参数配置

/// Mock操作类型枚举
public enum MockOperationType {
    case filter    // 筛选操作
    case sort      // 排序操作
}

/// Mock统一的参数配置
public struct MockQueryParam {
    let key: String
    let operation: MockOperationType
    let handler: MockQueryHandler
    
    /// 创建筛选参数
    static func filter(key: String, handler: MockFilterHandler) -> MockQueryParam {
        return MockQueryParam(key: key, operation: .filter, handler: .filter(handler))
    }
    
    /// 创建排序参数
    static func sort(key: String, handler: MockSortHandler) -> MockQueryParam {
        return MockQueryParam(key: key, operation: .sort, handler: .sort(handler))
    }
}

/// Mock查询处理器
public enum MockQueryHandler {
    case filter(MockFilterHandler)
    case sort(MockSortHandler)
}

/// Mock筛选处理器
public enum MockFilterHandler {
    case exact(String)           // 精确匹配
    case contains(String)        // 包含匹配
    case keyword([String])       // 关键词搜索（多个字段）
    case status(String)          // 状态筛选
    case range(String, ClosedRange<Double>) // 数值范围筛选
    case dateRange(String, ClosedRange<Date>) // 日期范围筛选
    case arrayContains(String)   // 数组包含匹配
    case boolean(String)         // 布尔值筛选
    case enumMatch(String)       // 枚举匹配
    case multiSelect(String, [String]) // 多选匹配
    case custom((Any, String) -> Bool) // 自定义筛选逻辑
}

/// Mock排序处理器
public enum MockSortHandler {
    case byNumber(String)        // 按数值排序（字段路径）
    case byDate(String)          // 按日期排序（字段路径）
    case byString(String)        // 按字符串排序（字段路径）
    case byCount(String)         // 按数组长度排序（字段路径）
    case byOptional(String, Any) // 按可选字段排序（字段路径，默认值）
    case custom((Any, Any, Bool) -> Bool) // 自定义排序逻辑
}

// MARK: - 核心协议定义

/// Mock分页策略协议
///
/// 定义了Mock分页数据策略的核心接口，支持筛选、排序和分页功能。
/// 每个策略负责处理特定API路径的数据处理逻辑。
///
/// ## 使用示例
/// ```swift
/// struct MyMockStrategy: MockPaginationStrategy {
///     typealias ItemType = MyModel
///     
///     var name: String { "我的Mock策略" }
///     var itemType: MyModel.Type { MyModel.self }
///     
///     func canHandle(apiPath: String) -> Bool {
///         return apiPath.contains("/my-api")
///     }
///     
///     func applyFilters(to items: [MyModel], with queryParams: [String: String]) -> [MyModel] {
///         // 实现筛选逻辑
///         return items
///     }
/// }
/// ```
public protocol MockPaginationStrategy {
    /// 关联的数据类型，必须遵循Codable协议
    associatedtype ItemType: Codable

    /// 策略名称，用于日志记录和调试
    var name: String { get }
    
    /// 关联的数据类型
    var itemType: ItemType.Type { get }
    
    /// 判断是否可以处理指定的API路径
    /// - Parameter apiPath: API路径
    /// - Returns: 如果可以处理返回true，否则返回false
    func canHandle(apiPath: String) -> Bool
    
    /// 应用筛选和排序逻辑
    /// - Parameters:
    ///   - items: 原始数据项
    ///   - queryParams: 查询参数
    /// - Returns: 处理后的数据项
    func applyFilters(to items: [ItemType], with queryParams: [String: String]) -> [ItemType]
}

// MARK: - 类型擦除包装器

/// 类型擦除的Mock分页策略包装器
public struct AnyMockPaginationStrategy {
    private let _name: String
    private let _canHandle: (String) -> Bool
    private let _applyFilters: (Data, [String: String], JSONDecoder) throws -> Data

    public var name: String { _name }

    public init<Strategy: MockPaginationStrategy>(_ strategy: Strategy) {
        self._name = strategy.name
        self._canHandle = strategy.canHandle
        self._applyFilters = { data, queryParams, decoder in
            return try MockPaginationStrategyManager.handlePagination(
                strategy: strategy,
                api: APIRequest(path: "", query: queryParams),
                data: data,
                decoder: decoder
            )
        }
    }

    public func canHandle(apiPath: String) -> Bool {
        return _canHandle(apiPath)
    }

    public func applyFilters(to data: Data, with queryParams: [String: String], decoder: JSONDecoder) throws -> Data {
        return try _applyFilters(data, queryParams, decoder)
    }
}

// MARK: - Mock分页策略管理器

/// Mock分页策略管理器
///
/// 负责管理所有Mock分页策略的核心逻辑，提供统一的数据处理接口
public struct MockPaginationStrategyManager {

    /// 通用Mock分页处理逻辑
    static func handlePagination<Strategy: MockPaginationStrategy>(
        strategy: Strategy,
        api: APIRequest,
        data: Data,
        decoder: JSONDecoder
    ) throws -> Data {
        let originalResponse = try decoder.decode(PaginationResponse<Strategy.ItemType>.self, from: data)

        let processedItems = strategy.applyFilters(to: originalResponse.items, with: api.query)

        // 处理分页逻辑，使用常量提升可维护性
        let requestedPage = Int(api.query["page"] ?? "\(MockPaginationStrategyConstants.defaultPage)") ?? MockPaginationStrategyConstants.defaultPage
        let requestedPageSize = Int(api.query["pageSize"] ?? "\(MockPaginationStrategyConstants.defaultPageSize)") ?? MockPaginationStrategyConstants.defaultPageSize

        let startIndex = (requestedPage - 1) * requestedPageSize
        let endIndex = min(startIndex + requestedPageSize, processedItems.count)

        guard startIndex < processedItems.count else {
            // 请求的页码超出范围，返回空页面
            let emptyResponse = PaginationResponse(
                currentPage: requestedPage,
                pageSize: requestedPageSize,
                hasMore: false,
                items: [] as [Strategy.ItemType]
            )
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            return try encoder.encode(emptyResponse)
        }

        let paginatedItems = Array(processedItems[startIndex..<endIndex])
        let hasMore = endIndex < processedItems.count

        let response = PaginationResponse(
            currentPage: requestedPage,
            pageSize: requestedPageSize,
            hasMore: hasMore,
            items: paginatedItems
        )



        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        return try encoder.encode(response)
    }
}
