import SwiftUI

// MARK: - Constants

/// 筛选栏样式常量
private enum FilterBarConstants {
    static let horizontalPadding: CGFloat = 16
    static let verticalPadding: CGFloat = 8
    static let topicButtonPadding: CGFloat = 8
    static let topicButtonVerticalPadding: CGFloat = 4
}

/// 话题按钮位置PreferenceKey
struct TopicButtonPositionKey: PreferenceKey {
    static var defaultValue: CGRect = .zero
    static func reduce(value: inout CGRect, nextValue: () -> CGRect) {
        value = nextValue()
    }
}

/// 发现页面筛选栏组件
///
/// 提供话题筛选功能，从原有的ExploreJourneyView中提取出来。
/// 负责显示选中的话题和提供话题抽屉的触发入口。
struct DiscoverFilterBarView: View {

    // MARK: - ViewModels

    /// 探索旅途主视图模型
    @ObservedObject var exploreViewModel: ExploreJourneyViewModel

    /// 发现页面视图模型（现在也管理话题数据）
    @ObservedObject var discoverViewModel: DiscoverViewModel

    /// 选中的位置文本
    let selectedLocation: String

    /// 目的地按钮点击回调
    let onDestinationButtonTap: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 话题筛选区域
            topicFilterSection
                .padding(.horizontal, FilterBarConstants.horizontalPadding)
                .padding(.vertical, FilterBarConstants.verticalPadding)

            // 添加一个不可见的底部标记，用于精确定位
            Rectangle()
                .fill(Color.clear)
                .frame(height: 0)
                .capturePosition(in: .named("ExploreJourneyView"))
        }
    }
    
    // MARK: - Components
    
    /// 话题筛选区域
    private var topicFilterSection: some View {
        HStack {
            // 目的地选择按钮
            destinationButton

            // 选中的话题标签
            selectedTopicsSection

            Spacer()

            // 话题筛选按钮
            topicFilterButton
        }
    }
    
    /// 目的地选择按钮
    private var destinationButton: some View {
        DestinationButtonView(
            selectedLocation: selectedLocation,
            showLikedIcon: selectedLocation == "喜欢",
            onTap: handleDestinationButtonTap
        )
    }



    /// 话题筛选按钮
    private var topicFilterButton: some View {
        Button(action: handleTopicFilterButtonTap) {
            Text("#")
                .font(.system(size: 16, weight: .bold))
                .foregroundStyle(topicFilterButtonGradient)
                .padding(.horizontal, FilterBarConstants.topicButtonPadding)
                .padding(.vertical, FilterBarConstants.topicButtonVerticalPadding)
                .rotationEffect(.degrees(exploreViewModel.isTopicDrawerShowing ? 180 : 0))
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            // 使用几何读取器获取按钮位置
            GeometryReader { geometry in
                Color.clear
                    .preference(key: TopicButtonPositionKey.self,
                              value: geometry.frame(in: .named("DiscoverView")))
            }
        )
    }

    /// 话题筛选按钮渐变
    private var topicFilterButtonGradient: LinearGradient {
        LinearGradient(
            colors: [.blue, .blue.opacity(0.8)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    /// 已选话题标签区域
    @ViewBuilder
    private var selectedTopicsSection: some View {
        if !discoverViewModel.selectedTopicIds.isEmpty {
            SelectedTopicsView(
                topics: discoverViewModel.topics,
                selectedTopicIds: $discoverViewModel.selectedTopicIds,
                onTopicRemoved: handleTopicRemoval
            )
            .frame(height: 26)
            .transition(.move(edge: .leading).combined(with: .opacity))
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: discoverViewModel.selectedTopicIds.isEmpty)
        }
    }

    // MARK: - Action Handlers

    /// 处理目的地按钮点击
    private func handleDestinationButtonTap() {
        exploreViewModel.hideTopicDrawer()
        onDestinationButtonTap()
    }

    /// 处理话题筛选按钮点击
    private func handleTopicFilterButtonTap() {
        exploreViewModel.toggleTopicDrawer()
    }

    /// 处理话题标签移除
    private func handleTopicRemoval(_ topicId: String) {
        discoverViewModel.deselectTopic(topicId)
        // 同步到ExploreJourneyViewModel
        exploreViewModel.selectedTopicIds = discoverViewModel.selectedTopicIds
    }
}

// MARK: - Preview

#Preview {
    DiscoverFilterBarView(
        exploreViewModel: ExploreJourneyViewModel(),
        discoverViewModel: DiscoverViewModel(),
        selectedLocation: "精选",
        onDestinationButtonTap: {}
    )
    .padding()
}
