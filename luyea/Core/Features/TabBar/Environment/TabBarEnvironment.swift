import SwiftUI

/// TabBar 环境值键
///
/// 用于在 SwiftUI 环境值系统中传递 TabBar 相关信息。
/// 通过环境值机制，子视图可以自动获取 TabBar 的布局参数。
struct TabBarEnvironmentKey: EnvironmentKey {
    static let defaultValue: TabBarEnvironment = TabBarEnvironment()
}

extension EnvironmentValues {
    /// TabBar 环境信息
    ///
    /// 通过环境值访问 TabBar 的布局参数，
    /// 子视图可以根据这些信息调整自己的布局。
    var tabBarEnvironment: TabBarEnvironment {
        get { self[TabBarEnvironmentKey.self] }
        set { self[TabBarEnvironmentKey.self] = newValue }
    }
}

/// TabBar 环境信息
///
/// 增强的TabBar环境信息，集成实时状态和完整的布局参数。
/// 确保内容视图能够正确处理TabBar占用的空间并响应状态变化。
///
/// 核心特性：
/// - 集成实时的TabBar状态信息
/// - 提供精确的布局计算参数
/// - 支持动态状态变化响应
/// - 确保内容不被TabBar遮挡
/// - 优化滚动和布局性能
///
/// 主要功能：
/// - 提供TabBar的实时可见性状态
/// - 计算需要预留的底部空间
/// - 管理TabBar相关的布局参数
/// - 提供当前选中的标签页信息
/// - 支持状态变化监听
struct TabBarEnvironment {

    /// TabBar 是否可见
    ///
    /// 实时反映TabBar的当前可见性状态。
    /// 当TabBar隐藏时，内容可以延伸到屏幕底部。
    /// 此状态与TabBarStateManager保持同步。
    let isVisible: Bool

    /// TabBar 预留的底部空间高度
    ///
    /// 包含TabBar高度和安全边距的总高度。
    /// 用于为滚动内容添加底部间距，避免被TabBar遮挡。
    /// 当TabBar隐藏时，此值应为0。
    let bottomInset: CGFloat

    /// TabBar 的实际高度
    ///
    /// TabBar组件本身的高度，不包含安全区域。
    /// 用于精确的布局计算和动画效果。
    let tabBarHeight: CGFloat

    /// 安全边距
    ///
    /// 额外的安全间距，确保内容与TabBar之间有适当的空隙。
    /// 提供更好的视觉效果和用户体验。
    let safetyMargin: CGFloat

    /// 当前选中的标签页
    ///
    /// 提供当前选中的标签页信息，便于子视图根据标签页调整行为。
    /// 与TabBarStateManager的selectedTab保持同步。
    let currentTab: AppTab?

    /// TabBar 是否正在执行动画
    ///
    /// 指示TabBar是否正在执行显示/隐藏动画。
    /// 子视图可以根据此状态调整自己的动画行为。
    let isAnimating: Bool

    /// 初始化 TabBar 环境信息
    ///
    /// - Parameters:
    ///   - isVisible: TabBar 是否可见，默认为 true
    ///   - bottomInset: 预留的底部空间高度，默认为 81pt
    ///   - tabBarHeight: TabBar 的实际高度，默认为 61pt
    ///   - safetyMargin: 安全边距，默认为 20pt
    ///   - currentTab: 当前选中的标签页，默认为 nil
    ///   - isAnimating: 是否正在执行动画，默认为 false
    init(
        isVisible: Bool = true,
        bottomInset: CGFloat = 81,
        tabBarHeight: CGFloat = 61,
        safetyMargin: CGFloat = 20,
        currentTab: AppTab? = nil,
        isAnimating: Bool = false
    ) {
        self.isVisible = isVisible
        self.bottomInset = bottomInset
        self.tabBarHeight = tabBarHeight
        self.safetyMargin = safetyMargin
        self.currentTab = currentTab
        self.isAnimating = isAnimating
    }

    /// 创建基于TabBarStateManager的环境信息
    ///
    /// 从TabBarStateManager获取实时状态信息创建环境对象。
    /// 确保环境信息与状态管理器完全同步。
    ///
    /// - Parameters:
    ///   - stateManager: TabBar状态管理器
    ///   - tabBarHeight: TabBar的实际高度，默认为61pt
    ///   - safetyMargin: 安全边距，默认为20pt
    /// - Returns: 基于状态管理器的环境信息
    @MainActor
    static func from(
        stateManager: TabBarStateManager,
        tabBarHeight: CGFloat = 61,
        safetyMargin: CGFloat = 20
    ) -> TabBarEnvironment {
        let bottomInset = stateManager.isVisible ? (tabBarHeight + safetyMargin) : 0

        return TabBarEnvironment(
            isVisible: stateManager.isVisible,
            bottomInset: bottomInset,
            tabBarHeight: tabBarHeight,
            safetyMargin: safetyMargin,
            currentTab: stateManager.selectedTab,
            isAnimating: stateManager.isAnimating
        )
    }
}

/// TabBar 感知的滚动视图
///
/// 自动处理 TabBar 占用空间的滚动视图组件。
/// 根据 TabBar 的可见性动态调整底部间距，确保内容不被遮挡。
///
/// 主要特性：
/// - 自动检测 TabBar 状态并调整底部间距
/// - 隐藏默认滚动背景，让父视图背景显示
/// - 支持自定义滚动轴向和指示器显示
///
/// 使用示例：
/// ```swift
/// TabBarAwareScrollView {
///     LazyVStack {
///         ForEach(items) { item in
///             ItemView(item: item)
///         }
///     }
/// }
/// ```
struct TabBarAwareScrollView<Content: View>: View {

    // MARK: - Environment & State

    @Environment(\.tabBarEnvironment) private var tabBarEnv
    @EnvironmentObject private var tabBarState: TabBarStateManager

    // MARK: - Properties

    /// 滚动视图内容
    let content: Content

    /// 是否显示滚动指示器
    let showsIndicators: Bool

    /// 滚动轴向
    let axes: Axis.Set

    // MARK: - Initialization

    /// 初始化 TabBar 感知的滚动视图
    ///
    /// - Parameters:
    ///   - axes: 滚动轴向，默认为垂直滚动
    ///   - showsIndicators: 是否显示滚动指示器，默认为 true
    ///   - content: 滚动视图内容
    init(
        _ axes: Axis.Set = .vertical,
        showsIndicators: Bool = true,
        @ViewBuilder content: () -> Content
    ) {
        self.axes = axes
        self.showsIndicators = showsIndicators
        self.content = content()
    }

    // MARK: - Body

    var body: some View {
        ScrollView(axes, showsIndicators: showsIndicators) {
            VStack(spacing: 0) {
                content

                // 动态底部间距：只在 TabBar 可见时添加
                // 确保滚动内容不被 TabBar 遮挡
                // 使用统一的状态管理器判断可见性
                if tabBarState.isVisible && tabBarEnv.isVisible {
                    Color.clear
                        .frame(height: tabBarEnv.bottomInset)
                }
            }
        }
        .scrollContentBackground(.hidden) // 隐藏默认背景，让父视图的背景显示
        .animation(.easeInOut(duration: 0.3), value: tabBarState.isVisible) // 添加底部间距变化动画
    }
}

/// TabBar 感知的容器视图
///
/// 为普通视图添加 TabBar 感知的底部间距。
/// 适用于非滚动的静态内容，确保底部内容不被 TabBar 遮挡。
///
/// 主要特性：
/// - 自动检测 TabBar 状态并添加底部间距
/// - 适用于静态布局的视图
/// - 与 TabBarAwareScrollView 配合使用
///
/// 使用示例：
/// ```swift
/// VStack {
///     Text("内容")
///     Spacer()
/// }
/// .tabBarAware()
/// ```
struct TabBarAwareContainer<Content: View>: View {

    // MARK: - Environment & State

    @Environment(\.tabBarEnvironment) private var tabBarEnv
    @EnvironmentObject private var tabBarState: TabBarStateManager

    // MARK: - Properties

    /// 容器内容
    let content: Content

    // MARK: - Initialization

    /// 初始化 TabBar 感知的容器视图
    ///
    /// - Parameter content: 容器内容
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    // MARK: - Body

    var body: some View {
        VStack(spacing: 0) {
            content

            // 动态底部间距：只在 TabBar 可见时添加
            // 确保静态内容不被 TabBar 遮挡
            // 使用统一的状态管理器判断可见性
            if tabBarState.isVisible && tabBarEnv.isVisible {
                Color.clear
                    .frame(height: tabBarEnv.bottomInset)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: tabBarState.isVisible) // 添加底部间距变化动画
    }
}

// MARK: - View Extensions

extension View {

    /// 为视图添加 TabBar 感知的底部间距
    ///
    /// 适用于静态内容视图，自动根据 TabBar 状态添加底部间距。
    /// 确保内容不被 TabBar 遮挡。
    ///
    /// - Parameter force: 是否强制添加间距，默认为 false（自动检测）
    /// - Returns: 带有 TabBar 感知功能的视图
    func tabBarAware(force: Bool = false) -> some View {
        TabBarAwareContainer {
            self
        }
    }

    /// 为滚动视图提供 TabBar 感知功能的便捷方法
    ///
    /// 将当前视图包装在 TabBarAwareScrollView 中，
    /// 自动处理 TabBar 占用的空间。
    ///
    /// - Parameters:
    ///   - axes: 滚动轴向，默认为垂直滚动
    ///   - showsIndicators: 是否显示滚动指示器，默认为 true
    /// - Returns: 带有 TabBar 感知功能的滚动视图
    func tabBarAwareScrollView(
        _ axes: Axis.Set = .vertical,
        showsIndicators: Bool = true
    ) -> some View {
        TabBarAwareScrollView(axes, showsIndicators: showsIndicators) {
            self
        }
    }
}
