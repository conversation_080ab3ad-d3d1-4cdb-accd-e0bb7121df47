import SwiftUI

struct FloatingActionButtons: View {
    @Binding var isZoomExpanded: Bool
    let currentLocationAction: () -> Void

    @State private var isPressed = false
    @State private var buttonScale: CGFloat = 1.0
    
    // MARK: - Body
    
    var body: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                currentLocationButton
            }
            .padding()
            .padding(.bottom, 200)
        }
    }

    private var currentLocationButton: some View {
        Button(action: handleLocationButtonTap) {
            HStack(spacing: 8) {
                Image(systemName: "location.fill")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundStyle(.blue.gradient)
                    .symbolEffect(.bounce, value: isPressed)

                if isZoomExpanded {
                    Text("当前位置")
                        .font(.caption2.weight(.medium))
                        .foregroundStyle(.blue.gradient)
                        .contentTransition(.opacity)
                }
            }
            .padding(.horizontal, isZoomExpanded ? 16 : 12)
            .padding(.vertical, 12)
            .background(.ultraThinMaterial, in: Capsule())
            .overlay(
                Capsule()
                    .stroke(.blue.opacity(0.2), lineWidth: 1)
            )
            .shadow(color: .black.opacity(0.15), radius: 4, x: 0, y: 2)
            .scaleEffect(buttonScale)
        }
        .buttonStyle(.plain)
        .sensoryFeedback(.impact(flexibility: .soft, intensity: 0.7), trigger: isPressed)
        .onLongPressGesture(minimumDuration: 0) {
        } onPressingChanged: { pressing in
            withAnimation(.easeInOut(duration: 0.15)) {
                isPressed = pressing
                buttonScale = pressing ? 0.95 : 1.0
            }
        }
    }

    private func handleLocationButtonTap() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        currentLocationAction()
        expandAndCollapse()
    }

    private func expandAndCollapse() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7, blendDuration: 0.3)) {
            isZoomExpanded = true
        }

        Task {
            try? await Task.sleep(for: .seconds(2.0))
            await MainActor.run {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0.2)) {
                    isZoomExpanded = false
                }
            }
        }
    }
}

// MARK: - Preview

#Preview {
    @Previewable @State var isExpanded = false

    ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.1), .teal.opacity(0.05)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()

        FloatingActionButtons(
            isZoomExpanded: $isExpanded,
            currentLocationAction: {
                print("Location button tapped")
            }
        )
    }
}
