import SwiftUI

/// 探索旅程主视图模型
///
/// 负责管理探索旅程模块的话题抽屉功能。
/// 这是一个轻量级的视图模型，专注于话题筛选和抽屉状态管理。
@MainActor
final class ExploreJourneyViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 是否显示话题抽屉
    ///
    /// 控制话题选择抽屉的显示隐藏状态。
    /// 用于话题筛选和选择功能。
    @Published var isTopicDrawerShowing: Bool = false

    /// 选中的话题ID集合
    ///
    /// 存储用户当前选中的话题ID，用于筛选内容。
    /// 支持多选话题功能。
    @Published var selectedTopicIds: Set<String> = []

    // MARK: - Initialization

    /// 初始化视图模型
    init() {
        Log.info("🎯 探索旅途视图模型初始化完成")
    }

    // MARK: - Public Methods - 话题抽屉控制

    /// 切换话题抽屉显示状态
    ///
    /// 在显示和隐藏之间切换话题抽屉状态。
    /// 使用弹簧动画提供自然的切换效果。
    func toggleTopicDrawer() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            isTopicDrawerShowing.toggle()
        }

        Log.debug("🎯 话题抽屉状态切换: \(isTopicDrawerShowing ? "显示" : "隐藏")")
    }

    /// 隐藏话题抽屉
    ///
    /// 以动画方式隐藏话题抽屉。
    /// 适用于用户完成话题选择或取消操作的场景。
    func hideTopicDrawer() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            isTopicDrawerShowing = false
        }

        Log.debug("🎯 话题抽屉已隐藏")
    }

    /// 显示话题抽屉
    ///
    /// 以动画方式显示话题抽屉。
    /// 适用于用户需要选择话题的场景。
    func showTopicDrawer() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            isTopicDrawerShowing = true
        }

        Log.debug("🎯 话题抽屉已显示")
    }

    // MARK: - Public Methods - 话题选择控制

    /// 选择话题
    ///
    /// 将指定话题ID添加到选中集合中。
    /// - Parameter topicId: 要选择的话题ID
    func selectTopic(_ topicId: String) {
        selectedTopicIds.insert(topicId)
        Log.debug("🎯 话题已选择: \(topicId), 当前选中: \(selectedTopicIds.count) 个")
    }

    /// 取消选择话题
    ///
    /// 将指定话题ID从选中集合中移除。
    /// - Parameter topicId: 要取消选择的话题ID
    func deselectTopic(_ topicId: String) {
        selectedTopicIds.remove(topicId)
        Log.debug("🎯 话题已取消选择: \(topicId), 当前选中: \(selectedTopicIds.count) 个")
    }

    /// 切换话题选择状态
    ///
    /// 如果话题已选中则取消选择，如果未选中则选择。
    /// - Parameter topicId: 要切换状态的话题ID
    func toggleTopicSelection(_ topicId: String) {
        if selectedTopicIds.contains(topicId) {
            deselectTopic(topicId)
        } else {
            selectTopic(topicId)
        }
    }

    /// 清除所有选中的话题
    ///
    /// 清空选中话题集合，恢复到未筛选状态。
    func clearSelectedTopics() {
        let previousCount = selectedTopicIds.count
        selectedTopicIds.removeAll()
        Log.debug("🎯 已清除所有选中话题，之前选中: \(previousCount) 个")
    }

    /// 设置选中的话题集合
    ///
    /// 直接设置选中话题的集合，用于批量操作。
    /// - Parameter topicIds: 要设置的话题ID集合
    func setSelectedTopics(_ topicIds: Set<String>) {
        selectedTopicIds = topicIds
        Log.debug("🎯 话题选择已更新，当前选中: \(selectedTopicIds.count) 个")
    }

    // MARK: - Computed Properties

    /// 是否有选中的话题
    var hasSelectedTopics: Bool {
        !selectedTopicIds.isEmpty
    }

    /// 选中话题的数量
    var selectedTopicsCount: Int {
        selectedTopicIds.count
    }
}
