import SwiftUI

/// 已选择目的地芯片组件
///
/// 显示已选择的目的地，支持移除操作。
/// 采用胶囊形状设计，提供清晰的视觉反馈。
struct SelectedDestinationChip: View {
    
    // MARK: - Properties
    
    let destination: Destination
    let onRemove: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        HStack(spacing: 6) {
            Text(destination.name)
                .font(.caption.weight(.medium))
                .foregroundColor(.blue)
            
            But<PERSON>(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.caption2.weight(.semibold))
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(Color.blue.opacity(0.1))
        .clipShape(Capsule())
    }
}

// MARK: - Preview

#Preview {
    HStack {
        SelectedDestinationChip(
            destination: Destination.samples[0],
            onRemove: {}
        )
        
        SelectedDestinationChip(
            destination: Destination.samples[1],
            onRemove: {}
        )
    }
    .padding()
}
