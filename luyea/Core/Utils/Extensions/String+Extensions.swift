import Foundation
import UIKit

// MARK: - String Extensions

extension String {
    // MARK: - Validation
    
    /// 是否为空或只包含空白字符
    var isBlank: Bool {
        trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    /// 是否不为空且不只包含空白字符
    var isNotBlank: Bool {
        !isBlank
    }
    
    /// 是否为有效的邮箱地址
    var isValidEmail: Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: self)
    }
    
    /// 是否为有效的手机号码（中国大陆）
    var isValidPhoneNumber: Bool {
        // 先进行与输入处理相同的过滤和限制，确保验证基于处理后的值
        let filtered = self.filter { $0.isNumber }
        let processedValue = filtered.count <= 11 ? filtered : String(filtered.prefix(11))
        
        let phoneRegex = "^1[3-9]\\d{9}$"
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
        return phonePredicate.evaluate(with: processedValue)
    }
    
    /// 是否为有效的URL
    var isValidURL: Bool {
        URL(string: self) != nil
    }
    
    // MARK: - Formatting
    
    /// 移除首尾空白字符
    var trimmed: String {
        trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// 首字母大写
    var capitalizingFirstLetter: String {
        guard !isEmpty else { return self }
        return prefix(1).uppercased() + dropFirst()
    }
    
    /// 限制字符串长度
    /// 
    /// - Parameters:
    ///   - length: 最大长度
    ///   - suffix: 超出时的后缀，默认为"..."
    /// - Returns: 限制长度后的字符串
    func truncated(to length: Int, suffix: String = "...") -> String {
        guard count > length else { return self }
        return String(prefix(length)) + suffix
    }
    
    /// 移除HTML标签
    var removingHTMLTags: String {
        replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
    }
    
    /// 转换为URL安全的字符串
    var urlEncoded: String {
        addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? self
    }
    
    /// URL解码
    var urlDecoded: String {
        removingPercentEncoding ?? self
    }
    
    // MARK: - Localization
    
    /// 本地化字符串
    var localized: String {
        NSLocalizedString(self, comment: "")
    }
    
    /// 带参数的本地化字符串
    /// 
    /// - Parameter arguments: 参数列表
    /// - Returns: 格式化后的本地化字符串
    func localized(with arguments: CVarArg...) -> String {
        String(format: localized, arguments: arguments)
    }
    
    // MARK: - Conversion
    
    /// 转换为Int
    var toInt: Int? {
        Int(self)
    }
    
    /// 转换为Double
    var toDouble: Double? {
        Double(self)
    }
    
    /// 转换为Bool
    var toBool: Bool? {
        switch lowercased() {
        case "true", "yes", "1":
            return true
        case "false", "no", "0":
            return false
        default:
            return nil
        }
    }
    
    /// 转换为Date
    /// 
    /// - Parameter format: 日期格式，默认为"yyyy-MM-dd"
    /// - Returns: 转换后的日期
    func toDate(format: String = "yyyy-MM-dd") -> Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = format
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter.date(from: self)
    }
    
    /// 转换为URL
    var toURL: URL? {
        URL(string: self)
    }
    
    // MARK: - Hashing & Encoding

    /// SHA256哈希（推荐使用，替代已弃用的MD5）
    ///
    /// SHA256是一种安全的加密哈希函数，适用于安全上下文。
    /// 生成256位（32字节）的哈希值。
    var sha256: String {
        let data = Data(utf8)
        var digest = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))

        data.withUnsafeBytes { bytes in
            _ = CC_SHA256(bytes.baseAddress, CC_LONG(data.count), &digest)
        }

        return digest.map { String(format: "%02hhx", $0) }.joined()
    }

    /// SHA1哈希
    ///
    /// 注意：SHA1也被认为是弱哈希算法，建议使用SHA256或更强的算法。
    var sha1: String {
        let data = Data(utf8)
        var digest = [UInt8](repeating: 0, count: Int(CC_SHA1_DIGEST_LENGTH))

        data.withUnsafeBytes { bytes in
            _ = CC_SHA1(bytes.baseAddress, CC_LONG(data.count), &digest)
        }

        return digest.map { String(format: "%02hhx", $0) }.joined()
    }

    /// SHA512哈希（最强安全性）
    ///
    /// SHA512提供最高级别的安全性，生成512位（64字节）的哈希值。
    /// 适用于需要最高安全性的场景。
    var sha512: String {
        let data = Data(utf8)
        var digest = [UInt8](repeating: 0, count: Int(CC_SHA512_DIGEST_LENGTH))

        data.withUnsafeBytes { bytes in
            _ = CC_SHA512(bytes.baseAddress, CC_LONG(data.count), &digest)
        }

        return digest.map { String(format: "%02hhx", $0) }.joined()
    }

    /// MD5哈希（已弃用，请使用sha256）
    @available(*, deprecated, message: "MD5 is cryptographically broken and should not be used in security contexts. Use sha256 instead.")
    var md5: String {
        // 为了向后兼容，保留此方法但标记为已弃用
        // 内部实际使用SHA256以确保安全性
        return sha256
    }
    
    /// Base64编码
    var base64Encoded: String {
        Data(utf8).base64EncodedString()
    }
    
    /// Base64解码
    var base64Decoded: String? {
        guard let data = Data(base64Encoded: self) else { return nil }
        return String(data: data, encoding: .utf8)
    }
    
    // MARK: - Text Measurement
    
    /// 计算文本尺寸
    /// 
    /// - Parameters:
    ///   - font: 字体
    ///   - constrainedToWidth: 约束宽度
    /// - Returns: 文本尺寸
    func size(withFont font: UIFont, constrainedToWidth width: CGFloat = .greatestFiniteMagnitude) -> CGSize {
        let constraintRect = CGSize(width: width, height: .greatestFiniteMagnitude)
        let boundingBox = self.boundingRect(
            with: constraintRect,
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            attributes: [.font: font],
            context: nil
        )
        return boundingBox.size
    }
    
    /// 计算文本高度
    /// 
    /// - Parameters:
    ///   - font: 字体
    ///   - width: 约束宽度
    /// - Returns: 文本高度
    func height(withFont font: UIFont, constrainedToWidth width: CGFloat) -> CGFloat {
        size(withFont: font, constrainedToWidth: width).height
    }
    
    // MARK: - Substring Operations
    
    /// 安全的子字符串
    /// 
    /// - Parameter range: 范围
    /// - Returns: 子字符串
    func safeSubstring(in range: Range<Int>) -> String {
        let start = max(0, range.lowerBound)
        let end = min(count, range.upperBound)
        
        guard start < end else { return "" }
        
        let startIndex = index(startIndex, offsetBy: start)
        let endIndex = index(startIndex, offsetBy: end - start)
        
        return String(self[startIndex..<endIndex])
    }
    
    /// 安全的前缀
    /// 
    /// - Parameter length: 长度
    /// - Returns: 前缀字符串
    func safePrefix(_ length: Int) -> String {
        guard length > 0 else { return "" }
        guard length < count else { return self }
        return String(prefix(length))
    }
    
    /// 安全的后缀
    /// 
    /// - Parameter length: 长度
    /// - Returns: 后缀字符串
    func safeSuffix(_ length: Int) -> String {
        guard length > 0 else { return "" }
        guard length < count else { return self }
        return String(suffix(length))
    }
    
    // MARK: - Search & Replace
    
    /// 包含字符串（忽略大小写）
    /// 
    /// - Parameter string: 要搜索的字符串
    /// - Returns: 是否包含
    func containsIgnoringCase(_ string: String) -> Bool {
        localizedCaseInsensitiveContains(string)
    }
    
    /// 替换字符串（忽略大小写）
    /// 
    /// - Parameters:
    ///   - target: 目标字符串
    ///   - replacement: 替换字符串
    /// - Returns: 替换后的字符串
    func replacingIgnoringCase(_ target: String, with replacement: String) -> String {
        replacingOccurrences(of: target, with: replacement, options: .caseInsensitive)
    }
    
    // MARK: - File Path Operations
    
    /// 文件扩展名
    var fileExtension: String {
        (self as NSString).pathExtension
    }
    
    /// 文件名（不含扩展名）
    var fileName: String {
        ((self as NSString).lastPathComponent as NSString).deletingPathExtension
    }
    
    /// 目录路径
    var directoryPath: String {
        (self as NSString).deletingLastPathComponent
    }
    
    /// 添加路径组件
    /// 
    /// - Parameter component: 路径组件
    /// - Returns: 完整路径
    func appendingPathComponent(_ component: String) -> String {
        (self as NSString).appendingPathComponent(component)
    }
}

// MARK: - CommonCrypto Import

import CommonCrypto

// MARK: - Attributed String Helpers

extension String {
    /// 创建属性字符串
    /// 
    /// - Parameters:
    ///   - font: 字体
    ///   - color: 颜色
    ///   - alignment: 对齐方式
    /// - Returns: 属性字符串
    func attributed(
        font: UIFont? = nil,
        color: UIColor? = nil,
        alignment: NSTextAlignment = .natural
    ) -> NSAttributedString {
        var attributes: [NSAttributedString.Key: Any] = [:]
        
        if let font = font {
            attributes[.font] = font
        }
        
        if let color = color {
            attributes[.foregroundColor] = color
        }
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = alignment
        attributes[.paragraphStyle] = paragraphStyle
        
        return NSAttributedString(string: self, attributes: attributes)
    }
}
