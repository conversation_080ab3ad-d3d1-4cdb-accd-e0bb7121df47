import Foundation

/// 行程类型枚举
enum TripType: String, Codable, CaseIterable {
    case roundTrip = "往返"
    case oneWay = "单程"

    var displayName: String {
        return self.rawValue
    }

    /// 后端API使用的英文值
    var apiValue: String {
        switch self {
        case .roundTrip:
            return "round_trip"
        case .oneWay:
            return "one_way"
        }
    }
}

/// 行程创建数据结构
///
/// 包含创建行程所需的所有信息，支持序列化以便保存草稿和网络传输
struct ItineraryCreationData: Codable {
    let departureLocation: String
    let departureDate: Date?
    let destinations: [Destination]
    let attractions: [AttractionModel]
    let tripType: TripType
    let tripDays: Int
    let budget: Double?
    let recommendationEnabled: Bool
    
    /// 初始化方法
    init(departureLocation: String = "",
         departureDate: Date? = nil,
         destinations: [Destination] = [],
         attractions: [AttractionModel] = [],
         tripType: TripType = .roundTrip,
         tripDays: Int = 3,
         budget: Double? = nil,
         recommendationEnabled: Bool = false) {
        self.departureLocation = departureLocation
        self.departureDate = departureDate
        self.destinations = destinations
        self.attractions = attractions
        self.tripType = tripType
        self.tripDays = tripDays
        self.budget = budget
        self.recommendationEnabled = recommendationEnabled
    }
}

// MARK: - Computed Properties

extension ItineraryCreationData {
    
    /// 是否有选中的目的地
    var hasDestinations: Bool {
        !destinations.isEmpty
    }
    
    /// 是否有选中的景点
    var hasAttractions: Bool {
        !attractions.isEmpty
    }
    
    /// 是否可以创建行程
    var canCreateItinerary: Bool {
        hasDestinations || hasAttractions
    }
    
    /// 是否启用了推荐功能
    var isRecommendationEnabled: Bool {
        recommendationEnabled
    }

    /// 是否包含返程
    var includeReturn: Bool {
        tripType == .roundTrip
    }

    /// 预算数值（已经是Double?类型，直接返回）
    var budgetValue: Double? {
        budget
    }
    
    /// 目的地名称列表
    var destinationNames: [String] {
        destinations.map { $0.name }
    }
    
    /// 景点名称列表
    var attractionNames: [String] {
        attractions.map { $0.name }
    }
    
    /// 生成行程标题
    var suggestedTitle: String {
        if !destinations.isEmpty {
            return "\(destinationNames.joined(separator: "、"))\(tripDays)日游"
        } else if !attractions.isEmpty {
            return "\(attractionNames.prefix(2).joined(separator: "、"))等\(tripDays)日游"
        } else {
            return "\(tripDays)日游"
        }
    }
}

// MARK: - Validation

extension ItineraryCreationData {

    /// 验证数据有效性
    func validate() -> (isValid: Bool, errors: [String]) {
        var errors: [String] = []

        // 验证目的地或景点
        if destinations.isEmpty && attractions.isEmpty {
            errors.append("请至少选择一个目的地或景点")
        }

        // 验证行程天数
        if tripDays < 1 || tripDays > 365 {
            errors.append("行程天数应在1-365天之间")
        }

        // 验证出发日期（Date类型无需格式验证）
        if let date = departureDate {
            let now = Date()
            if date < now {
                errors.append("出发日期不能早于今天")
            }
        }

        // 验证预算
        if let budgetValue = budget {
            if budgetValue < 0 {
                errors.append("预算金额不能为负数")
            } else if budgetValue > 999999999 {
                errors.append("预算金额过大")
            }
        }

        return (isValid: errors.isEmpty, errors: errors)
    }
}

// MARK: - Sample Data

extension ItineraryCreationData {
    
    /// 示例数据
    static let sample = ItineraryCreationData(
        departureLocation: "北京",
        departureDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()),
        destinations: [Destination.samples[0]],
        attractions: [AttractionModel.samples[0]],
        tripType: .roundTrip,
        tripDays: 5,
        budget: 5000.0,
        recommendationEnabled: true
    )
    
    /// 空数据
    static let empty = ItineraryCreationData()
}

// MARK: - String Conversion Helpers

extension ItineraryCreationData {

    /// 出发日期的字符串表示（用于API和显示）
    var departureDateString: String {
        guard let date = departureDate else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    /// 行程类型的字符串表示
    var tripTypeString: String {
        return tripType.rawValue
    }

    /// 预算的字符串表示
    var budgetString: String {
        guard let budget = budget else { return "" }
        return String(format: "%.0f", budget)
    }

    /// 推荐功能的字符串表示
    var recommendationEnabledString: String {
        return recommendationEnabled ? "启用" : "禁用"
    }

    /// 从字符串创建ItineraryCreationData的便利构造器
    static func fromStrings(
        departureLocation: String = "",
        departureDate: String = "",
        destinations: [Destination] = [],
        attractions: [AttractionModel] = [],
        tripType: String = "往返",
        tripDays: Int = 3,
        budget: String = "",
        recommendationEnabled: String = "禁用"
    ) -> ItineraryCreationData {

        // 转换日期
        let date: Date?
        if departureDate.isEmpty {
            date = nil
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            date = formatter.date(from: departureDate)
        }

        // 转换行程类型
        let type = TripType(rawValue: tripType) ?? .roundTrip

        // 转换预算
        let budgetValue: Double?
        if budget.isEmpty {
            budgetValue = nil
        } else {
            budgetValue = Double(budget)
        }

        // 转换推荐功能
        let recommendation = recommendationEnabled == "启用"

        return ItineraryCreationData(
            departureLocation: departureLocation,
            departureDate: date,
            destinations: destinations,
            attractions: attractions,
            tripType: type,
            tripDays: tripDays,
            budget: budgetValue,
            recommendationEnabled: recommendation
        )
    }
}

// MARK: - API Encoding

extension ItineraryCreationData {

    /// 自定义编码以适配后端API格式
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(departureLocation, forKey: .departureLocation)
        try container.encodeIfPresent(departureDateString.isEmpty ? nil : departureDateString, forKey: .departureDate)

        // 只传输必要的目的地字段
        let destinationData = destinations.map { ["id": $0.id, "name": $0.name] }
        try container.encode(destinationData, forKey: .destinations)

        // 只传输必要的景点字段
        let attractionData = attractions.map { ["id": $0.id, "name": $0.name] }
        try container.encode(attractionData, forKey: .attractions)

        try container.encode(tripType.apiValue, forKey: .tripType)
        try container.encode(tripDays, forKey: .tripDays)
        try container.encodeIfPresent(budget, forKey: .budget)
        try container.encode(recommendationEnabled, forKey: .recommendationEnabled)
    }

    enum CodingKeys: String, CodingKey {
        case departureLocation = "departure_location"
        case departureDate = "departure_date"
        case destinations
        case attractions
        case tripType = "trip_type"
        case tripDays = "trip_days"
        case budget
        case recommendationEnabled = "recommendation_enabled"
    }
}
