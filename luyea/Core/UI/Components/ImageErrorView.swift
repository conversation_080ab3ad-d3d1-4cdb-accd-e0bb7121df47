import SwiftUI

/// 通用图片错误视图组件
///
/// 自动根据父视图大小选择合适的图标尺寸和布局。
/// 用户通过修饰frame来控制容器大小，组件会智能适配。
///
/// 使用示例：
/// ```swift
/// // 基础用法
/// ImageErrorView()
///
/// // 显示错误文本
/// ImageErrorView(showText: true)
///
/// // 自定义错误信息
/// ImageErrorView(
///     showText: true,
///     errorText: "网络连接失败",
///     iconName: "wifi.slash"
/// )
/// ```
struct ImageErrorView: View {

    // MARK: - Types

    /// 预定义样式
    enum Style {
        case standard   // 标准红色错误样式
        case minimal    // 简约灰色样式
        case custom(backgroundColor: Color, iconColor: Color)  // 自定义颜色

        var backgroundColor: Color {
            switch self {
            case .standard: return Color.red.opacity(0.08)
            case .minimal: return Color.gray.opacity(0.08)
            case .custom(let backgroundColor, _): return backgroundColor
            }
        }

        var iconColor: Color {
            switch self {
            case .standard: return Color.red.opacity(0.6)
            case .minimal: return Color.gray.opacity(0.6)
            case .custom(_, let iconColor): return iconColor
            }
        }
    }
    
    // MARK: - Properties

    let style: Style
    let showText: Bool
    let errorText: String
    let iconName: String
    
    // MARK: - Initialization

    init(
        style: Style = .standard,
        showText: Bool = false,
        errorText: String = "图片加载失败",
        iconName: String = "exclamationmark.triangle"
    ) {
        self.style = style
        self.showText = showText
        self.errorText = errorText
        self.iconName = iconName
    }
    
    // MARK: - Body

    var body: some View {
        GeometryReader { geometry in
            let iconSize = autoIconSize(for: geometry.size)
            let shouldShowText = showText && iconSize >= 20
            let isSmallContainer = min(geometry.size.width, geometry.size.height) < 60

            Rectangle()
                .fill(backgroundFill(for: geometry.size))
                .overlay(
                    Group {
                        if shouldShowText {
                            VStack(spacing: isSmallContainer ? 2 : 6) {
                                Image(systemName: iconName)
                                    .font(.system(size: iconSize, weight: .medium))
                                    .foregroundColor(style.iconColor)

                                Text(errorText)
                                    .font(isSmallContainer ? .caption2.weight(.medium) : .caption.weight(.medium))
                                    .foregroundColor(style.iconColor)
                            }
                        } else {
                            Image(systemName: iconName)
                                .font(.system(size: iconSize, weight: .medium))
                                .foregroundColor(style.iconColor)
                        }
                    }
                )
        }
    }

    // MARK: - Private Methods

    private func autoIconSize(for geometry: CGSize) -> CGFloat {
        let minDimension = min(geometry.width, geometry.height)

        switch minDimension {
        case 0..<50: return 14
        case 50..<80: return 18
        default: return 24
        }
    }

    /// 智能背景填充 - 小容器使用渐变，大容器使用纯色
    private func backgroundFill(for geometry: CGSize) -> AnyShapeStyle {
        let minDimension = min(geometry.width, geometry.height)

        // 小容器（< 60px）使用渐变背景，增加视觉吸引力
        if minDimension < 60 {
            let baseColor = baseColorForStyle()
            return AnyShapeStyle(
                LinearGradient(
                    colors: [
                        baseColor.opacity(0.1),
                        baseColor.opacity(0.15)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        } else {
            // 大容器使用纯色背景
            return AnyShapeStyle(style.backgroundColor)
        }
    }

    /// 获取样式的基础颜色
    private func baseColorForStyle() -> Color {
        switch style {
        case .standard: return .red
        case .minimal: return .gray
        case .custom(_, let iconColor): return iconColor
        }
    }
}

// MARK: - Preview

#Preview("自动尺寸") {
    VStack(spacing: 20) {
        Text("自动尺寸演示")
            .font(.headline)

        HStack(spacing: 16) {
            ImageErrorView()
                .frame(width: 40, height: 40)
                .clipShape(RoundedRectangle(cornerRadius: 8))

            ImageErrorView()
                .frame(width: 70, height: 70)
                .clipShape(RoundedRectangle(cornerRadius: 8))

            ImageErrorView(showText: true)
                .frame(width: 120, height: 120)
                .clipShape(RoundedRectangle(cornerRadius: 8))
        }

        Text("自动根据容器大小选择图标尺寸")
            .font(.caption)
            .foregroundColor(.secondary)
    }
    .padding()
}

#Preview("不同样式") {
    HStack(spacing: 16) {
        ImageErrorView(style: .standard)
            .frame(width: 60, height: 60)
            .clipShape(RoundedRectangle(cornerRadius: 8))

        ImageErrorView(style: .minimal)
            .frame(width: 60, height: 60)
            .clipShape(RoundedRectangle(cornerRadius: 8))

        ImageErrorView(
            style: .custom(
                backgroundColor: Color.blue.opacity(0.06),
                iconColor: Color.blue.opacity(0.5)
            )
        )
        .frame(width: 60, height: 60)
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    .padding()
}

#Preview("小容器智能适配") {
    VStack(spacing: 20) {
        Text("小容器自动使用渐变背景")
            .font(.headline)

        HStack(spacing: 16) {
            ImageErrorView(style: .standard, showText: true)
                .frame(width: 45, height: 45)
                .clipShape(RoundedRectangle(cornerRadius: 6))

            ImageErrorView(style: .minimal, showText: true)
                .frame(width: 45, height: 45)
                .clipShape(RoundedRectangle(cornerRadius: 6))

            ImageErrorView(
                style: .custom(
                    backgroundColor: Color.blue.opacity(0.06),
                    iconColor: Color.blue.opacity(0.5)
                ),
                showText: true,
                errorText: "自定义"
            )
            .frame(width: 45, height: 45)
            .clipShape(RoundedRectangle(cornerRadius: 6))
        }

        Text("大容器使用纯色背景")
            .font(.headline)

        HStack(spacing: 16) {
            ImageErrorView(style: .standard, showText: true)
                .frame(width: 80, height: 80)
                .clipShape(RoundedRectangle(cornerRadius: 8))

            ImageErrorView(style: .minimal, showText: true)
                .frame(width: 80, height: 80)
                .clipShape(RoundedRectangle(cornerRadius: 8))

            ImageErrorView(
                style: .custom(
                    backgroundColor: Color.blue.opacity(0.06),
                    iconColor: Color.blue.opacity(0.5)
                ),
                showText: true,
                errorText: "自定义"
            )
            .frame(width: 80, height: 80)
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
    }
    .padding()
}
