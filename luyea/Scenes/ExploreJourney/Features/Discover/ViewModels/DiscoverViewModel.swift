import Foundation
import SwiftUI
import Combine

/// 发现页相关错误类型
enum DiscoverError: LocalizedError {
    case networkError
    case invalidData
    case noMoreData
    case refreshFailed
    case topicsLoadFailed(Error)
    case topicsNetworkUnavailable
    case topicsTimeout
    case topicsServerError
    case topicsDataCorrupted
    case destinationsLoadFailed(Error)
    case destinationsNetworkUnavailable
    case destinationsTimeout
    case destinationsServerError

    var errorDescription: String? {
        switch self {
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .invalidData:
            return "数据格式错误，请稍后重试"
        case .noMoreData:
            return "没有更多数据了"
        case .refreshFailed:
            return "刷新失败，请重试"
        case .topicsLoadFailed(let error):
            return "话题数据加载失败: \(error.localizedDescription)"
        case .topicsNetworkUnavailable:
            return "网络连接不可用，请检查网络设置"
        case .topicsTimeout:
            return "请求超时，请稍后重试"
        case .topicsServerError:
            return "服务器暂时不可用，请稍后重试"
        case .topicsDataCorrupted:
            return "数据格式错误，请联系客服"
        case .destinationsLoadFailed(let error):
            return "目的地数据加载失败: \(error.localizedDescription)"
        case .destinationsNetworkUnavailable:
            return "网络连接不可用，请检查网络设置"
        case .destinationsTimeout:
            return "请求超时，请稍后重试"
        case .destinationsServerError:
            return "服务器暂时不可用，请稍后重试"
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case .topicsNetworkUnavailable:
            return "请检查网络连接后重试"
        case .topicsTimeout, .topicsServerError:
            return "请稍后重试"
        case .topicsDataCorrupted:
            return "请联系客服"
        case .topicsLoadFailed:
            return "请重试或联系客服"
        default:
            return nil
        }
    }
}

/// 发现页数据查询参数
struct DiscoverQueryParams {
    let page: Int
    let pageSize: Int
    let topicIds: [String]
    let destinationId: String?
    let destinationType: DestinationType?
    let showLikedOnly: Bool
}

/// 发现页主视图模型，负责数据加载、刷新、分页等业务逻辑
/// 现在也负责话题数据的管理，统一管理所有发现页相关的数据和状态
@MainActor
final class DiscoverViewModel: ObservableObject {
    // MARK: - Published Properties

    // 发现内容相关
    @Published private(set) var discoverItems: [DiscoverItem] = []
    @Published private(set) var isLoading = false
    @Published private(set) var error: Error?
    @Published private(set) var isInitializing = true
    @Published private(set) var hasMoreData = true
    @Published private(set) var isTopicRefreshing = false  // 话题切换时的加载状态
    @Published private(set) var hasLoadedData = false  // 是否已经完成过数据加载
    @Published private(set) var isManualRefreshing = false  // 手动下拉刷新状态
    @Published var scrollToTopTrigger = UUID() // 滚动到顶部的触发器

    // 话题相关 - 从TopicViewModel合并过来
    @Published private(set) var topics: [Topic] = []
    @Published private(set) var isTopicsLoading = false
    @Published private(set) var isTopicsRefreshing = false
    @Published private(set) var topicsError: DiscoverError?
    @Published var topicsSearchText: String = "" {
        didSet {
            topicsSearchDebouncer.call {
                Task { @MainActor in
                    self.performTopicsSearch(self.topicsSearchText)
                }
            }
        }
    }
    @Published var selectedTopicIds: Set<String> = []
    
    // MARK: - Private Properties

    // 发现内容相关
    private var currentPage = 1
    private let pageSize = 20
    private var selectedDestination: Destination? = nil
    @Published var showLikedOnly: Bool = false

    // Service层依赖
    private let discoverService: DiscoverServiceProtocol
    private let topicService: TopicServiceProtocol
    private var cancellables = Set<AnyCancellable>()
    private var topicsLoadingTask: Task<Void, Never>?

    // 防抖器
    private let topicsSearchDebouncer = Debouncer(delay: DesignSystemConstants.Interaction.debounceDelay)
    private let topicSelectionDebouncer = Debouncer(delay: DesignSystemConstants.Interaction.fastDebounceDelay)
    
    /// 初始化并自动加载初始数据
    init(
        discoverService: DiscoverServiceProtocol = DiscoverService(),
        topicService: TopicServiceProtocol = TopicService()
    ) {
        self.discoverService = discoverService
        self.topicService = topicService

        setupTopicsSearchDebounce()

        Task {
            await refreshData()
            await fetchTopics()
        }

        Log.info("🎯 发现页视图模型初始化完成")
    }

    deinit {
        topicsLoadingTask?.cancel()
        // topicSelectionDebouncer 和 topicsSearchDebouncer 会在自己的 deinit 中自动取消任务
        Log.debug("🎯 发现页视图模型已销毁")
    }
    
    /// 刷新数据（初始加载/下拉刷新）
    func refreshData(isManual: Bool = false) async {
        // 防止重复刷新
        guard !isLoading else { return }

        if isManual {
            // 手动刷新：保持现有数据显示，避免闪动
            withAnimation(.easeInOut(duration: 0.2)) {
                isManualRefreshing = true
                // 不重置 hasLoadedData，保持现有数据显示
            }
        } else {
            // 话题切换或初始加载：显示骨架屏
            withAnimation(.easeInOut(duration: 0.2)) {
                isTopicRefreshing = true
                hasLoadedData = false
            }
        }

        if isInitializing {
            try? await Task.sleep(nanoseconds: 800_000_000)
        }

        currentPage = 1
        error = nil
        await loadData()

        // 确保数据加载完成后再更新状态
        await MainActor.run {
            withAnimation(.easeOut(duration: 0.3)) {
                isInitializing = false
                isTopicRefreshing = false
                isManualRefreshing = false
            }
        }
    }
    
    /// 加载更多数据（分页）
    func loadMoreData() async {
        guard !isLoading && hasMoreData else { return }
        currentPage += 1
        await loadData()
    }




    
    /// 更新选中的话题并刷新数据（带防抖功能）
    func updateSelectedTopics(_ topicIds: Set<String>, force: Bool = false) {
        // 添加调试日志
        Log.debug("🏷️ [DiscoverViewModel] 更新话题选择")
        Log.debug("🔄 旧选择: \(Array(selectedTopicIds))")
        Log.debug("🆕 新选择: \(Array(topicIds))")
        Log.debug("🔧 强制更新: \(force)")

        // 优化：避免不必要的更新
        if !force && selectedTopicIds == topicIds {
            Log.debug("⏭️ [DiscoverViewModel] 话题选择无变化，跳过更新")
            return
        }

        // 优化：智能滚动触发
        let shouldScrollToTop = force || (topicIds.isEmpty && !selectedTopicIds.isEmpty)
        if shouldScrollToTop {
            scrollToTopTrigger = UUID()
        }

        selectedTopicIds = topicIds
        Log.debug("✅ [DiscoverViewModel] 话题选择已更新: \(Array(selectedTopicIds))")

        // 优化：触发类似下拉刷新的效果
        triggerTopicRefreshEffect()

        // 使用统一的防抖工具
        topicSelectionDebouncer.call {
            Task { @MainActor in
                await self.refreshDataForTopicChange()
            }
        }
    }
    
    /// 话题变化时的数据刷新
    private func refreshDataForTopicChange() async {
        currentPage = 1
        error = nil

        // 优化：加载数据
        await loadData()

        // 优化：延迟关闭刷新状态，确保UI更新完成
        Task {
            try? await Task.sleep(nanoseconds: 200_000_000) // 200ms
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.isTopicRefreshing = false
                    self.hasLoadedData = true
                }
            }
        }
    }
    
    /// 更新选中目的地
    func updateSelectedDestination(_ destination: Destination?, showLikedOnly: Bool = false) {
        if selectedDestination?.id == destination?.id && self.showLikedOnly == showLikedOnly { return }

        // 切换目的地时，触发滚动和刷新效果
        scrollToTopTrigger = UUID()

        selectedDestination = destination
        self.showLikedOnly = showLikedOnly

        // 触发类似下拉刷新的效果
        triggerTopicRefreshEffect()

        Task {
            await refreshDataForDestinationChange()
        }
    }
    
    /// 新增：目的地变化时的数据刷新
    private func refreshDataForDestinationChange() async {
        currentPage = 1
        error = nil
        await loadData()
        // 延迟关闭刷新状态，确保数据更新完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeInOut(duration: 0.3)) {
                self.isTopicRefreshing = false
            }
        }
    }

    /// 触发话题/目的地刷新效果（模拟下拉刷新）
    private func triggerTopicRefreshEffect() {
        // 只有在已经有数据的情况下才显示刷新动画
        // 避免在初始加载时显示
        guard hasLoadedData else {
            withAnimation(.easeInOut(duration: 0.2)) {
                isTopicRefreshing = true
                hasLoadedData = false
            }
            return
        }

        withAnimation(.easeInOut(duration: 0.3)) {
            isTopicRefreshing = true
            hasLoadedData = false
        }

        // 触发滚动到顶部，模拟下拉刷新的视觉效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.scrollToTopTrigger = UUID()
        }
    }
    
    /// 加载数据（内部方法）
    private func loadData() async {
        isLoading = true
        error = nil

        // 添加调试日志
        let topicIdsArray = Array(selectedTopicIds)
        Log.debug("🔍 [DiscoverViewModel] 开始加载数据")
        Log.debug("📄 页码: \(currentPage), 页大小: \(pageSize)")
        Log.debug("🏷️ 选中话题IDs: \(topicIdsArray)")
        Log.debug("📍 选中目的地: \(selectedDestination?.name ?? "无")")
        Log.debug("❤️ 仅显示点赞: \(showLikedOnly)")

        do {
            let response = try await discoverService.fetchDiscoverItems(
                page: currentPage,
                pageSize: pageSize,
                topicIds: topicIdsArray,
                destinationId: selectedDestination?.id,
                destinationType: selectedDestination?.type.rawValue,
                showLikedOnly: showLikedOnly
            )
            await MainActor.run {
                if currentPage == 1 {
                    // 区分不同的刷新场景
                    if isTopicRefreshing {
                        // 话题切换：延迟更新，等待骨架屏显示完成
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                self.discoverItems = response.items
                                self.hasLoadedData = true
                            }
                        }
                        // 先更新其他状态，但不设置 hasLoadedData
                        hasMoreData = response.hasMore
                        isLoading = false
                    } else if isManualRefreshing {
                        // 手动下拉刷新：使用更精细的更新策略
                        // 先更新非UI状态
                        hasMoreData = response.hasMore
                        isLoading = false

                        // 延迟更新数据，确保刷新指示器有时间显示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            // 使用更温和的动画替换数据
                            withAnimation(.easeOut(duration: 0.2)) {
                                self.discoverItems = response.items
                            }
                        }
                    } else {
                        // 初始加载：使用弹簧动画
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            self.discoverItems = response.items
                        }
                        // 立即更新所有状态
                        hasMoreData = response.hasMore
                        isLoading = false
                        hasLoadedData = true
                    }
                } else {
                    // 分页加载：追加数据
                    // 先更新状态，确保UI状态一致
                    hasMoreData = response.hasMore
                    isLoading = false
                    hasLoadedData = true

                    // 然后追加数据，使用更温和的动画
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.discoverItems.append(contentsOf: response.items)
                    }
                }
            }
        } catch {
            await MainActor.run {
                self.error = error
                isLoading = false
                hasLoadedData = true  // 即使出错也标记为已加载，避免一直显示骨架屏
            }
            Log.error("❌ [DiscoverViewModel] 数据加载失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 话题管理方法 (从TopicViewModel合并过来)

    /// 计算属性：筛选后的话题列表
    var filteredTopics: [Topic] {
        var filtered = topics

        // 应用搜索筛选
        if !topicsSearchText.isEmpty {
            filtered = filtered.filter { topic in
                topic.name.localizedCaseInsensitiveContains(topicsSearchText)
            }
        }

        // 按排序权重和名称排序
        return filtered.sorted { lhs, rhs in
            if lhs.order != rhs.order {
                return lhs.order < rhs.order
            }
            return lhs.name < rhs.name
        }
    }

    /// 是否有话题数据
    var hasTopicsData: Bool {
        !topics.isEmpty
    }

    /// 是否显示话题空状态
    var showTopicsEmptyState: Bool {
        !isTopicsLoading && !hasTopicsData && topicsError == nil
    }

    /// 话题缓存是否有效（5分钟内）
    private var isTopicsCacheValid: Bool {
        return topicService.isTopicsCacheValid
    }

    /// 获取话题列表
    func fetchTopics() async {
        // 如果正在加载或缓存有效，则跳过
        if isTopicsLoading || (isTopicsCacheValid && !topics.isEmpty) {
            Log.debug("🏷️ 话题数据已缓存或正在加载，跳过请求")
            return
        }

        await loadTopics(forceRefresh: false)
    }

    /// 刷新话题数据
    func refreshTopics() async {
        await loadTopics(forceRefresh: true)
    }

    /// 搜索话题
    func searchTopics(with text: String) {
        topicsSearchText = text
    }

    /// 选择话题
    func selectTopic(_ topicId: String) {
        Log.debug("➕ [DiscoverViewModel] 选择话题: \(topicId)")
        selectedTopicIds.insert(topicId)
        updateSelectedTopics(selectedTopicIds, force: true)
    }

    /// 取消选择话题
    func deselectTopic(_ topicId: String) {
        Log.debug("➖ [DiscoverViewModel] 取消选择话题: \(topicId)")
        selectedTopicIds.remove(topicId)
        updateSelectedTopics(selectedTopicIds, force: true)
    }

    /// 切换话题选择状态
    func toggleTopicSelection(_ topicId: String) {
        Log.debug("🔄 [DiscoverViewModel] 切换话题选择: \(topicId)")
        if selectedTopicIds.contains(topicId) {
            Log.debug("📤 [DiscoverViewModel] 话题已选中，执行取消选择")
            deselectTopic(topicId)
        } else {
            Log.debug("📥 [DiscoverViewModel] 话题未选中，执行选择")
            selectTopic(topicId)
        }
    }

    /// 清空所有话题选择并重新加载数据
    func clearAllTopicSelections() {
        selectedTopicIds.removeAll()
        updateSelectedTopics(selectedTopicIds, force: true)
    }

    /// 清除话题错误状态
    func clearTopicsError() {
        topicsError = nil
    }

    /// 重试话题加载
    func retryTopicsLoading() {
        clearTopicsError()
        Task {
            await fetchTopics()
        }
    }

    // MARK: - 话题私有方法

    /// 设置话题搜索防抖
    private func setupTopicsSearchDebounce() {
        // 使用新的通用防抖工具替代 Combine 防抖
        // 在 topicsSearchText 的 didSet 中处理防抖逻辑
    }

    /// 执行话题搜索
    private func performTopicsSearch(_ text: String) {
        // 使用TopicService进行搜索
        let allTopics = topicService.getCachedTopics()
        let filteredTopics = topicService.searchTopics(query: text, in: allTopics)

        // 更新显示的话题列表
        topics = filteredTopics
    }

    /// 加载话题数据
    private func loadTopics(forceRefresh: Bool) async {
        // 取消之前的加载任务
        topicsLoadingTask?.cancel()

        // 设置加载状态
        if forceRefresh {
            isTopicsRefreshing = true
        } else {
            isTopicsLoading = true
        }
        topicsError = nil

        Log.info("🏷️ 开始加载话题数据")

        topicsLoadingTask = Task {
            do {
                let topics = try await topicService.fetchTopics(forceRefresh: forceRefresh)

                await MainActor.run {
                    self.topics = topics
                    Log.success("✅ 话题数据加载成功: \(topics.count) 个")
                }

            } catch {
                if !Task.isCancelled {
                    await handleTopicsError(error)
                }
            }

            // 更新加载状态
            await MainActor.run {
                self.isTopicsLoading = false
                self.isTopicsRefreshing = false
            }
        }

        await topicsLoadingTask?.value
    }

    /// 处理话题错误
    private func handleTopicsError(_ error: Error) async {
        let topicsError: DiscoverError

        if let networkError = error as? NetworkError {
            switch networkError {
            case .noInternetConnection:
                topicsError = .topicsNetworkUnavailable
            case .timeout:
                topicsError = .topicsTimeout
            case .serverError:
                topicsError = .topicsServerError
            default:
                topicsError = .topicsLoadFailed(error)
            }
        } else {
            topicsError = .topicsLoadFailed(error)
        }

        await MainActor.run {
            self.topicsError = topicsError
            Log.error("❌ 话题数据加载失败: \(topicsError.localizedDescription)")
        }
    }
}
