import SwiftUI

struct DestinationCityGridView: View {
    let province: String
    let cities: [String]
    let showProvinceTag: Bool
    @Binding var selectedLocation: String
    @Binding var isPresented: Bool
    var onSelect: ((String) -> Void)?

    private var allItems: [String] {
        if province.isEmpty {
            return cities
        } else {
            return [province] + cities
        }
    }

    var body: some View {
        let buttonHeight: CGFloat = 32
        let columns = Array(repeating: GridItem(.flexible(minimum: 90), spacing: 14), count: 3)

        LazyVGrid(columns: columns, spacing: 14) {
            ForEach(self.allItems.indices, id: \.self) { idx in
                let name = self.allItems[idx]
                ZStack(alignment: .topTrailing) {
                    Text(name)
                        .font(.system(size: 15, weight: selectedLocation == name ? .bold : (idx == 0 && !province.isEmpty ? .bold : .medium)))
                        .foregroundColor(selectedLocation == name ? .white : (idx == 0 && !province.isEmpty ? Color.accentColor : .primary))
                        .frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height: buttonHeight)
                        .background(selectedLocation == name ? Color.accentColor : Color(.systemBackground))
                        .cornerRadius(8)
                        .padding(.trailing, (idx == 0 && !province.isEmpty) ? 22 : 0)
                        .lineLimit(1)
                        .onTapGesture {
                            selectedLocation = name
                            onSelect?(name)
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                isPresented = false
                            }
                        }
                    if idx == 0 && !province.isEmpty && showProvinceTag {
                        Text("省")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(selectedLocation == name ? Color.blue : .white)
                            .padding(.horizontal, 5)
                            .padding(.vertical, 1)
                            .background(selectedLocation == name ? Color.white : Color.blue)
                            .cornerRadius(6)
                            .offset(x: -6, y: 6)
                    }
                }
            }
        }
        .padding(.top, 2)
    }
} 