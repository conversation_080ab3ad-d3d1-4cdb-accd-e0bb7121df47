import SwiftUI

struct ImageCarouselView: View {
    let imageUrls: [String]
    @Binding var currentPage: Int
    @Binding var isAutoScrolling: Bool
    let onImageTap: () -> Void
    let onZoomChanged: (Bool) -> Void
    
    // 自动轮播的时间间隔（秒）
    private let autoScrollInterval: TimeInterval = 3.0
    @State private var autoScrollTask: Task<Void, Never>? = nil
    
    var body: some View {
        GeometryReader { geometry in
            TabView(selection: $currentPage) {
                ForEach(0..<imageUrls.count, id: \.self) { index in
                    CachedAsyncImage(
                        url: URL(string: imageUrls[index])
                    ) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: geometry.size.width, height: 380)
                            .clipped()
                    } placeholder: {
                        Rectangle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: geometry.size.width, height: 380)
                            .overlay(
                                ProgressView()
                                    .tint(.gray)
                            )
                    } errorView: {
                        ImageErrorView(
                            style: .custom(
                                backgroundColor: Color.blue.opacity(0.06),
                                iconColor: Color.blue.opacity(0.5)
                            ),
                            showText: true,
                            errorText: "图片加载失败",
                            iconName: "photo"
                        )
                        .frame(width: geometry.size.width, height: 380)
                    }
                    .frame(width: geometry.size.width)
                    .tag(index)
                    .onTapGesture {
                        onImageTap()
                    }
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .onChange(of: currentPage) { oldValue, newValue in
                let validPage = max(0, min(newValue, imageUrls.count - 1))
                if validPage != newValue {
                    currentPage = validPage
                }
            }
        }
        .onAppear {
            if imageUrls.count > 1 {
                startAutoScroll()
            }
        }
        .onDisappear {
            stopAutoScroll()
        }
        .onChange(of: isAutoScrolling) { _, newValue in
            if newValue && imageUrls.count > 1 {
                startAutoScroll()
            } else {
                stopAutoScroll()
            }
        }
    }
    
    // MARK: - 自动滚动控制
    
    private func startAutoScroll() {
        stopAutoScroll()
        
        autoScrollTask = Task {
            while !Task.isCancelled {
                try? await Task.sleep(nanoseconds: UInt64(autoScrollInterval * 1_000_000_000))
                if !Task.isCancelled && isAutoScrolling {
                    withAnimation {
                        currentPage = (currentPage + 1) % imageUrls.count
                    }
                }
            }
        }
    }
    
    private func stopAutoScroll() {
        autoScrollTask?.cancel()
        autoScrollTask = nil
    }
}

// MARK: - 预览
#Preview {
    ImageCarouselView(
        imageUrls: [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
            "https://images.unsplash.com/photo-1469474968028-56623f02e42e",
            "https://images.unsplash.com/photo-1441974231531-c6227db76b6e"
        ],
        currentPage: .constant(0),
        isAutoScrolling: .constant(true),
        onImageTap: {},
        onZoomChanged: { _ in }
    )
    .frame(height: 380)
}
