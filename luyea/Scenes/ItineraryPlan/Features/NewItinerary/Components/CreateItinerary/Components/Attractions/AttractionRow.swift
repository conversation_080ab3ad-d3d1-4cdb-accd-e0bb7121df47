import SwiftUI

/// 景点搜索结果行组件
///
/// 功能特性：
/// - 简约设计风格
/// - 流畅的交互动画
/// - 符合app整体设计风格
/// - 支持选择状态切换
struct AttractionRow: View {
    
    // MARK: - Properties
    
    let attraction: AttractionModel
    let isSelected: Bool
    let onTap: () -> Void
    
    // MARK: - State
    
    @State private var isPressed = false
    
    // MARK: - Body
    
    var body: some View {
        Button(action: {
            // 使用异步操作，确保点击响应不被阻塞
            Task { @MainActor in
                onTap()
            }
        }) {
            HStack(spacing: 16) {
                // 景点图片
                attractionImage
                
                // 景点信息
                VStack(alignment: .leading, spacing: 6) {
                    // 景点名称
                    Text(attraction.name)
                        .font(.headline.weight(.semibold))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    // 地址信息
                    Text(attraction.fullAddress)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    // 景点类型和评分
                    HStack(spacing: 12) {
                        // 类型标签
                        Text(attraction.type)
                            .font(.caption.weight(.medium))
                            .foregroundColor(.blue)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.1))
                            .clipShape(Capsule())
                        
                        // 评分
                        if let rating = attraction.formattedRating {
                            HStack(spacing: 4) {
                                Image(systemName: "star.fill")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                                Text(rating)
                                    .font(.caption.weight(.medium))
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                    }
                }
                
                Spacer()
                
                // 选择状态指示器
                selectionIndicator
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                Rectangle()
                    .fill(isSelected ? Color.blue.opacity(0.05) : Color.clear)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
    }
    
    // MARK: - Private Views
    
    /// 景点图片
    private var attractionImage: some View {
        CachedAsyncImage(
            url: URL(string: attraction.imageUrl ?? "")
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            placeholderImage
        }
        .frame(width: 60, height: 60)
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    /// 占位图片
    private var placeholderImage: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.gray.opacity(0.15))
            .overlay(
                Image(systemName: attractionTypeIcon)
                    .font(.title3)
                    .foregroundColor(.gray.opacity(0.6))
            )
    }
    
    /// 选择状态指示器
    private var selectionIndicator: some View {
        Group {
            if isSelected {
                Image(systemName: "checkmark.circle.fill")
                    .font(.title3)
                    .foregroundColor(.blue)
                    .scaleEffect(1.1)
            } else {
                Image(systemName: "plus.circle")
                    .font(.title3)
                    .foregroundColor(.gray.opacity(0.6))
            }
        }
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
    
    /// 根据景点类型返回对应图标
    private var attractionTypeIcon: String {
        switch attraction.type {
        case "自然风光":
            return "mountain.2"
        case "历史文化":
            return "building.columns"
        case "地标建筑":
            return "building.2"
        case "主题乐园":
            return "gamecontroller"
        default:
            return "mappin"
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 0) {
        AttractionRow(
            attraction: AttractionModel.samples[0],
            isSelected: false,
            onTap: {}
        )
        
        Divider()
            .padding(.leading, 76)
        
        AttractionRow(
            attraction: AttractionModel.samples[1],
            isSelected: true,
            onTap: {}
        )
    }
    .background(Color(.systemBackground))
    .clipShape(RoundedRectangle(cornerRadius: 18))
    .padding()
    .background(Color(.systemGroupedBackground))
}
