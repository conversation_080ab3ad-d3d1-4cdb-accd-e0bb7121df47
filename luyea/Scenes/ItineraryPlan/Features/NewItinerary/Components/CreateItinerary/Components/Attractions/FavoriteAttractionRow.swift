import SwiftUI

/// 收藏地点行组件
///
/// 功能特性：
/// - 显示收藏地点的基本信息
/// - 支持选择状态显示
/// - 紧凑的行设计
/// - 符合app整体设计风格
struct FavoriteAttractionRow: View {
    
    // MARK: - Properties
    
    let attraction: AttractionModel
    let isSelected: Bool
    let onTap: () -> Void
    
    // MARK: - State
    
    @State private var isPressed = false
    
    // MARK: - Body
    
    var body: some View {
        Button(action: {
            // 使用异步操作，确保点击响应不被阻塞
            Task { @MainActor in
                onTap()
            }
        }) {
            HStack(spacing: 12) {
                // 景点图片
                attractionImage
                
                // 景点信息
                VStack(alignment: .leading, spacing: 4) {
                    // 景点名称
                    Text(attraction.name)
                        .font(.subheadline.weight(.medium))
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    // 地址信息
                    Text(attraction.fullAddress)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)

                    // 收藏时间
                    if let favoriteTime = attraction.formattedFavoriteDate {
                        Text(favoriteTime)
                            .font(.caption2)
                            .foregroundColor(.secondary.opacity(0.8))
                    }
                }
                
                Spacer()
                
                // 收藏标识和选择状态
                HStack(spacing: 8) {
                    // 收藏图标
                    Image(systemName: "heart.fill")
                        .font(.caption)
                        .foregroundColor(.red.opacity(0.7))
                    
                    // 选择状态指示器
                    selectionIndicator
                }
            }
            .padding(.horizontal, 4)
            .padding(.vertical, 8)
            .background(
                Rectangle()
                    .fill(isSelected ? Color.blue.opacity(0.05) : Color.clear)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
    }
    
    // MARK: - Private Views
    
    /// 景点图片
    private var attractionImage: some View {
        CachedAsyncImage(
            url: URL(string: attraction.imageUrl ?? "")
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            placeholderImage
        }
        .frame(width: 44, height: 44)
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    /// 占位图片
    private var placeholderImage: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color.gray.opacity(0.15))
            .overlay(
                Image(systemName: attractionTypeIcon)
                    .font(.caption)
                    .foregroundColor(.gray.opacity(0.6))
            )
    }
    
    /// 选择状态指示器
    private var selectionIndicator: some View {
        Group {
            if isSelected {
                Image(systemName: "checkmark.circle.fill")
                    .font(.subheadline)
                    .foregroundColor(.blue)
                    .scaleEffect(1.1)
            } else {
                Image(systemName: "plus.circle")
                    .font(.subheadline)
                    .foregroundColor(.gray.opacity(0.6))
            }
        }
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
    
    /// 根据景点类型返回对应图标
    private var attractionTypeIcon: String {
        switch attraction.type {
        case "自然风光":
            return "mountain.2"
        case "历史文化":
            return "building.columns"
        case "地标建筑":
            return "building.2"
        case "主题乐园":
            return "gamecontroller"
        default:
            return "mappin"
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 0) {
        FavoriteAttractionRow(
            attraction: AttractionModel.samples[0],
            isSelected: false,
            onTap: {}
        )
        
        Divider()
            .padding(.leading, 60)
        
        FavoriteAttractionRow(
            attraction: AttractionModel.samples[1],
            isSelected: true,
            onTap: {}
        )
        
        Divider()
            .padding(.leading, 60)
        
        FavoriteAttractionRow(
            attraction: AttractionModel.samples[2],
            isSelected: false,
            onTap: {}
        )
    }
    .background(Color(.systemBackground))
    .clipShape(RoundedRectangle(cornerRadius: 12))
    .padding()
    .background(Color(.systemGroupedBackground))
}
