import SwiftUI

/// 城市推荐选项卡片
///
/// 功能特性：
/// - 让用户选择是否推荐已选地点城市的其他信息
/// - 包含推荐类型选择（景点、美食、住宿等）
/// - 智能分析已选地点的城市信息
/// - 支持强制开启模式和智能Toast提示
/// - 现代化卡片设计
struct CityRecommendationCard: View {

    // MARK: - Properties

    @Binding var enableRecommendation: Bool
    @Binding var recommendationTypes: Set<RecommendationType>
    let selectedDestinations: [Destination]
    let selectedAttractions: [AttractionModel]
    let shouldForceEnable: Bool

    // MARK: - State

    @State private var isExpanded: Bool = false
    // 注意：Toast已移至全局管理，使用ToastManager.shared

    // MARK: - Constants

    private enum Constants {
        // Layout
        static let cardPadding: CGFloat = 20
        static let sectionSpacing: CGFloat = 16
        static let iconSpacing: CGFloat = 16
        static let contentSpacing: CGFloat = 4
        static let gridSpacing: CGFloat = 12

        // Animation
        static let animationDuration: TimeInterval = 0.3
        static let toggleAnimationDuration: TimeInterval = 0.2

        // Toast
        static let toastDurationStandard: TimeInterval = 3.0
        static let toastDurationExtended: TimeInterval = 4.0

        // Display Limits
        static let maxDisplayCities = 3
        static let maxDisplayTypes = 2
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: Constants.sectionSpacing) {
            mainToggleSection

            if enableRecommendation && !isRecommendationDisabled {
                recommendationOptionsSection
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .move(edge: .top)),
                        removal: .opacity.combined(with: .move(edge: .top))
                    ))
            }
        }
        .padding(Constants.cardPadding)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        .animation(.easeInOut(duration: Constants.animationDuration), value: enableRecommendation)
    }

    // MARK: - Private Views

    /// 推荐图标
    private var recommendationIcon: some View {
        Image(systemName: enableRecommendation ? "sparkles.rectangle.stack.fill" : "sparkles.rectangle.stack")
            .font(.title2)
            .foregroundColor(enableRecommendation ? .blue : .gray)
            .animation(.easeInOut(duration: Constants.toggleAnimationDuration), value: enableRecommendation)
    }
    
    /// 主开关区域
    private var mainToggleSection: some View {
        HStack(spacing: Constants.iconSpacing) {
            // 推荐图标
            recommendationIcon

            // 内容区域
            VStack(alignment: .leading, spacing: Constants.contentSpacing) {
                Text("为我推荐同城信息")
                    .font(.headline.weight(.medium))
                    .foregroundColor(.primary)

                Text(recommendationDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
            }
            
            Spacer()
            
            // 开关
            Toggle("", isOn: $enableRecommendation)
                .labelsHidden()
                .scaleEffect(0.9)
                .disabled(isRecommendationDisabled || shouldForceEnable)
                .opacity(isRecommendationDisabled ? 0.5 : (shouldForceEnable ? 0.8 : 1.0))
                .onTapGesture {
                    if isRecommendationDisabled {
                        handleDisabledToggleTap()
                    } else if shouldForceEnable {
                        handleForceEnabledToggleTap()
                    }
                }
        }
    }
    
    /// 推荐选项区域
    private var recommendationOptionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 分隔线
            Divider()
                .padding(.horizontal, -4)
            
            // 推荐偏好标题
            Text("推荐偏好")
                .font(.subheadline.weight(.medium))
                .foregroundColor(.primary)
            
            // 推荐类型选项
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2),
                      spacing: Constants.gridSpacing) {
                ForEach(RecommendationType.allCases, id: \.self) { type in
                    RecommendationTypeButtonView(
                        type: type,
                        isSelected: recommendationTypes.contains(type),
                        onTap: {
                            toggleRecommendationType(type)
                        }
                    )
                }
            }
            
            // 提示信息
            VStack(alignment: .leading, spacing: 8) {
                // 推荐内容提示
                if !recommendationTypes.isEmpty {
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "info.circle")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .frame(width: 12, height: 12)

                        Text(recommendationContentDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                }


            }
            .padding(.top, 4)
        }
    }
    
    // MARK: - Computed Properties

    /// 当前状态信息
    private var currentState: RecommendationState {
        RecommendationState(
            hasDestinations: !selectedDestinations.isEmpty,
            hasAttractions: !selectedAttractions.isEmpty,
            isEnabled: enableRecommendation,
            isForced: shouldForceEnable
        )
    }

    /// 是否应该禁用推荐功能
    private var isRecommendationDisabled: Bool {
        // 只有当没有任何城市类型的目的地时才禁用
        selectedDestinations.filter { $0.type == .city }.isEmpty
    }

    /// 推荐描述文本
    private var recommendationDescription: String {
        let state = currentState
        let cityDestinations = selectedDestinations.filter { $0.type == .city }
        let hasCities = !cityDestinations.isEmpty
        let hasAttractions = state.hasAttractions
        let hasNonCityDestinations = selectedDestinations.contains { $0.type != .city }

        if isRecommendationDisabled {
            // 推荐功能被禁用（没有城市）
            if !state.hasDestinations && !hasAttractions {
                // 什么都没选择
                return "添加目的地城市后，将为您提供个性化推荐"
            } else if !hasCities && hasAttractions {
                // 有景点但没有城市（用户可能删除了自动添加的城市）
                return "您已选择了景点，请添加目的地城市以启用推荐功能"
            } else if !hasCities && hasNonCityDestinations {
                // 有非城市类型的目的地但没有城市
                return "请添加目的地城市以启用推荐功能"
            } else {
                // 其他情况
                return "请添加目的地城市以开启推荐功能"
            }
        } else if hasCities {
            // 有目的地城市且功能可用（强制启用）
            let cityCount = cityDestinations.count
            let cityDisplayText = formatCityDisplayText(cityCount: cityCount)

            // 当有城市时，推荐功能必须启用且无法关闭
            return "为您推荐\(cityDisplayText)的同城信息，帮助发现更多精彩内容"
        } else {
            // 理论上不应该到达这里，但作为兜底
            return "选择目的地或景点后，为您提供个性化推荐"
        }
    }
    
    /// 城市名称列表
    private var cityNames: String {
        let allCities = Set(
            selectedDestinations.map(\.name) +
            selectedAttractions.map(\.city)
        )
        return formatDisplayList(Array(allCities), maxCount: Constants.maxDisplayCities, suffix: "个城市")
    }

    /// 已选择推荐类型的描述
    private var selectedTypesDescription: String {
        let typeNames = recommendationTypes.map(\.displayName)
        return formatDisplayList(typeNames, maxCount: Constants.maxDisplayTypes, suffix: "类信息")
    }

    /// 推荐内容描述文本
    private var recommendationContentDescription: String {
        _ = currentState
        let typeNames = recommendationTypes.map(\.displayName)
        let typesText = formatDisplayList(typeNames, maxCount: Constants.maxDisplayTypes, suffix: "类信息")

        // 检查是否有地点但没有对应的城市
        let hasOrphanedAttractions = hasAttractionsWithoutCorrespondingCities()

        if !selectedDestinations.isEmpty {
            // 有选择的目的地城市
            let cityCount = selectedDestinations.count
            let cityDisplayText = formatCityDisplayText(cityCount: cityCount)

            if hasOrphanedAttractions {
                return "将为您推荐\(cityDisplayText)的\(typesText)，并为您规划已添加的地点旅程"
            } else {
                return "将为您推荐\(cityDisplayText)的\(typesText)"
            }
        } else if !selectedAttractions.isEmpty {
            // 没有城市但有地点
            return "将为您规划已添加的地点旅程"
        } else {
            // 没有城市也没有地点
            return "选择目的地后将为您推荐\(typesText)"
        }
    }

    /// 格式化显示列表
    private func formatDisplayList(_ items: [String], maxCount: Int, suffix: String) -> String {
        guard !items.isEmpty else { return "" }

        if items.count > maxCount - 1 {
            let displayItems = Array(items.prefix(maxCount - 1))
            return displayItems.joined(separator: "、") + "等\(items.count)\(suffix)"
        } else {
            return items.joined(separator: "、")
        }
    }





    // MARK: - Private Methods

    /// 处理禁用状态下的开关点击
    private func handleDisabledToggleTap() {
        showToast(.disabled)
    }

    /// 处理强制开启状态下的开关点击
    private func handleForceEnabledToggleTap() {
        showToast(.forceEnabled)
    }

    /// 显示Toast消息
    private func showToast(_ type: ToastMessageType) {
        ToastManager.shared.show(type.message, duration: type.duration)
    }

    /// 切换推荐类型选择
    private func toggleRecommendationType(_ type: RecommendationType) {
        withAnimation(.easeInOut(duration: Constants.toggleAnimationDuration)) {
            if recommendationTypes.contains(type) {
                recommendationTypes.remove(type)
            } else {
                recommendationTypes.insert(type)
            }
        }
    }

    /// 格式化城市显示文本
    private func formatCityDisplayText(cityCount: Int) -> String {
        let cityDestinations = selectedDestinations.filter { $0.type == .city }
        let cityNames = cityDestinations.map(\.name)

        switch cityCount {
        case 0:
            return ""
        case 1:
            return cityNames.first ?? ""
        case 2:
            return "\(cityNames[0])、\(cityNames[1])等\(cityCount)个城市"
        case 3:
            return "\(cityNames[0])、\(cityNames[1])、\(cityNames[2])等\(cityCount)个城市"
        default:
            return "\(cityNames[0])、\(cityNames[1])等\(cityCount)个城市"
        }
    }

    /// 检查是否有地点但没有对应的城市
    private func hasAttractionsWithoutCorrespondingCities() -> Bool {
        let selectedCityNames = Set(selectedDestinations.map(\.name))
        let attractionCityNames = Set(selectedAttractions.map(\.city))

        // 如果有景点的城市不在已选择的目的地城市中，说明用户删除了城市但保留了地点
        return !attractionCityNames.isSubset(of: selectedCityNames) && !selectedAttractions.isEmpty
    }
}

// MARK: - Supporting Types

/// 推荐状态信息
private struct RecommendationState {
    let hasDestinations: Bool
    let hasAttractions: Bool
    let isEnabled: Bool
    let isForced: Bool
}

/// Toast消息类型
private enum ToastMessageType {
    case disabled
    case forceEnabled

    var message: String {
        switch self {
        case .disabled:
            return "推荐功能需要选择目的地城市"
        case .forceEnabled:
            return "您选择了目的地将为您自动开启推荐"
        }
    }

    var duration: TimeInterval {
        switch self {
        case .disabled:
            return 2.5
        case .forceEnabled:
            return 2.0
        }
    }
}

/// 推荐类型按钮
struct RecommendationTypeButtonView: View {
    let type: RecommendationType
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 8) {
                Image(systemName: type.iconName)
                    .font(.caption)
                    .foregroundColor(isSelected ? .white : .primary)
                
                Text(type.displayName)
                    .font(.caption.weight(.medium))
                    .foregroundColor(isSelected ? .white : .primary)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8, style: .continuous)
                    .fill(isSelected ? Color.blue : Color.gray.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// 推荐类型枚举
enum RecommendationType: String, CaseIterable {
    case attractions = "attractions"
    case restaurants = "restaurants"
    case hotels = "hotels"
    case shopping = "shopping"
    case entertainment = "entertainment"
    case culture = "culture"
    
    var displayName: String {
        switch self {
        case .attractions:
            return "景点"
        case .restaurants:
            return "美食"
        case .hotels:
            return "住宿"
        case .shopping:
            return "购物"
        case .entertainment:
            return "娱乐"
        case .culture:
            return "文化"
        }
    }
    
    var iconName: String {
        switch self {
        case .attractions:
            return "mountain.2"
        case .restaurants:
            return "fork.knife"
        case .hotels:
            return "bed.double"
        case .shopping:
            return "bag"
        case .entertainment:
            return "gamecontroller"
        case .culture:
            return "building.columns"
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        CityRecommendationCard(
            enableRecommendation: .constant(true),
            recommendationTypes: .constant([.attractions, .restaurants]),
            selectedDestinations: Destination.samples.filter { $0.type == .city }.prefix(2).map { $0 },
            selectedAttractions: AttractionModel.samples.prefix(1).map { $0 },
            shouldForceEnable: false
        )

        CityRecommendationCard(
            enableRecommendation: .constant(false),
            recommendationTypes: .constant([]),
            selectedDestinations: [],
            selectedAttractions: [],
            shouldForceEnable: false
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
    // 全局Toast无需环境对象
}
