import SwiftUI

/// 偏好选择按钮，无状态组件
struct PreferenceButtonView: View {
    let preference: TravelPreference
    let isSelected: Bool
    let action: () -> Void
    
    // MARK: - Body
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Text(preference.emoji)
                    .font(.subheadline)
                Text(preference.name)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 15)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(isSelected ? Color.primary : Color.clear, lineWidth: 1.5)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.primary.opacity(0.05), radius: 4, x: 0, y: 2)
            )
            .foregroundColor(.primary)
        }
    }
}

#Preview {
    PreferenceButtonView(
        preference: TravelPreference.classicRoute,
        isSelected: true,
        action: {}
    )
}
