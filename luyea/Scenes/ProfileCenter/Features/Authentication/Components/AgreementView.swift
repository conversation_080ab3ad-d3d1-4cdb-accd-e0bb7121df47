import SwiftUI

/// 协议同意组件
/// 
/// 职责：
/// - 显示用户协议和隐私政策同意选项
/// - 处理协议链接点击事件
/// - 管理同意状态
struct AgreementView: View {
    
    // MARK: - Properties
    
    @Binding var isAccepted: Bool
    let onUserAgreementTap: () -> Void
    let onPrivacyPolicyTap: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        HStack(alignment: .center, spacing: 8) {
            // 复选框
            checkboxButton

            // 协议文本
            agreementText
        }
        .animation(.easeInOut(duration: DesignSystemConstants.Animation.standard), value: isAccepted)
    }
    
    // MARK: - Subviews
    
    private var checkboxButton: some View {
        Button(action: {
            withAnimation(.spring(response: DesignSystemConstants.Animation.Spring.response, dampingFraction: DesignSystemConstants.Animation.Spring.damping)) {
                isAccepted.toggle()
            }
        }) {
            Image(systemName: isAccepted ? "checkmark.square.fill" : "square")
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(isAccepted ? Color.accentColor : Color.secondary)
                .frame(width: 20, height: 20)
        }
        .buttonStyle(.plain)
    }
    
    private var agreementText: some View {
        HStack(spacing: 0) {
            Text(AuthenticationConstants.Text.agreementPrefix)
                .font(.caption2)
                .foregroundColor(.secondary)

            Button(action: onUserAgreementTap) {
                Text(AuthenticationConstants.Text.userAgreement)
                    .font(.caption2)
                    .foregroundColor(.accentColor)
                    .underline()
            }
            .buttonStyle(.plain)

            Text(AuthenticationConstants.Text.agreementConnector)
                .font(.caption2)
                .foregroundColor(.secondary)

            Button(action: onPrivacyPolicyTap) {
                Text(AuthenticationConstants.Text.privacyPolicy)
                    .font(.caption2)
                    .foregroundColor(.accentColor)
                    .underline()
            }
            .buttonStyle(.plain)
        }
        .fixedSize()
    }
}

// MARK: - Preview

#Preview("协议同意组件") {
    VStack(spacing: 32) {
        // 未同意状态
        AgreementView(
            isAccepted: .constant(false),
            onUserAgreementTap: {
                print("点击用户协议")
            },
            onPrivacyPolicyTap: {
                print("点击隐私政策")
            }
        )
        
        // 已同意状态
        AgreementView(
            isAccepted: .constant(true),
            onUserAgreementTap: {
                print("点击用户协议")
            },
            onPrivacyPolicyTap: {
                print("点击隐私政策")
            }
        )
    }
    .padding()
    .appBackground()
}
