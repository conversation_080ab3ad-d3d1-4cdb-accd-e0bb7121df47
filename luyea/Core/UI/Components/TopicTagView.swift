import SwiftUI

/// 全局可复用的话题标签组件
struct TopicTagView: View {
    
    /// 标签的样式与行为
    enum Style {
        /// **展示型**：如卡片上的悬浮标签，不可点击。
        case display
        
        /// **可选择型**：如抽屉中的标签，可点击切换状态。
        case selectable(isSelected: Bool)
        
        /// **可移除型**：如已选列表中的标签，带移除按钮。
        case removable(onRemove: () -> Void)
    }
    
    let topic: Topic
    let style: Style
    
    var body: some View {
        switch style {
        case .display:
            displayView
        case .selectable(let isSelected):
            selectableButton(isSelected: isSelected)
        case .removable(let onRemove):
            removableView(onRemove: onRemove)
        }
    }
    
    // MARK: - Subviews
    
    /// 展示型视图
    private var displayView: some View {
        Text("#\(topic.name)")
            .font(.system(size: 11, weight: .medium))
            .foregroundColor(.white)
            .padding(.vertical, 4)
            .padding(.horizontal, 8)
            .background(Color.primary.opacity(0.5))
            .cornerRadius(6)
    }
    
    /// 可选择型视图
    private func selectableButton(isSelected: Bool) -> some View {
        Text("#\(topic.name)")
            .font(.system(size: 12, weight: isSelected ? .medium : .regular))
            .foregroundColor(isSelected ? .white : .primary.opacity(0.8))
            .frame(maxWidth: .infinity)
            .frame(height: 28)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(isSelected ? Color.blue : Color(.systemGray6))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(isSelected ? Color.blue.opacity(0.5) : Color.clear, lineWidth: 1)
            )
    }
    
    /// 可移除型视图
    private func removableView(onRemove: @escaping () -> Void) -> some View {
        HStack(spacing: 6) {
            Text("#\(topic.name)")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.blue)
            
            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.blue.opacity(0.8))
                    .frame(width: 16, height: 16)
                    .background(Circle().fill(Color.blue.opacity(0.1)))
            }
            .buttonStyle(ScaleButtonStyle(scale: 0.85))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 5)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.blue.opacity(0.08))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.blue.opacity(0.2), lineWidth: 0.5)
        )
    }
}

// MARK: - Preview
#if DEBUG
struct TopicTagView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            TopicTagView(
                topic: Topic(id: "1", name: "展示型", order: 1),
                style: .display
            )
            
            TopicTagView(
                topic: Topic(id: "2", name: "未选中", order: 2),
                style: .selectable(isSelected: false)
            )
            
            TopicTagView(
                topic: Topic(id: "3", name: "已选中", order: 3),
                style: .selectable(isSelected: true)
            )
            
            TopicTagView(
                topic: Topic(id: "4", name: "可移除", order: 4),
                style: .removable(onRemove: {})
            )
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
}
#endif 