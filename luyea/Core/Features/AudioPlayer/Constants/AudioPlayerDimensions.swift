import Foundation
import UIKit

/// 音频播放器尺寸常量
///
/// 定义音频播放器的各种尺寸参数，确保整个应用中的一致性。
/// 这些常量被播放器组件、手势处理器、位置管理器等多个模块使用。
enum AudioPlayerDimensions {
    
    // MARK: - 基础尺寸
    
    /// 收缩状态播放器宽度
    static let collapsedWidth: CGFloat = 44
    
    /// 收缩状态播放器高度
    static let collapsedHeight: CGFloat = 44
    
    /// 展开状态播放器最大宽度
    static let expandedMaxWidth: CGFloat = 280
    
    /// 展开状态播放器高度
    static let expandedHeight: CGFloat = 44
    
    /// 展开状态播放器最小宽度
    static let expandedMinWidth: CGFloat = 200
    
    // MARK: - 安全边距
    
    /// 播放器与屏幕边缘的安全边距
    static let safetyMargin: CGFloat = 8
    
    /// 播放器与其他UI元素的最小间距
    static let minimumClearance: CGFloat = 12
    
    // MARK: - 图标尺寸
    
    /// 收缩状态图标尺寸
    static let collapsedIconSize: CGFloat = 16
    
    /// 展开状态图标尺寸
    static let expandedIconSize: CGFloat = 32
    
    /// 按钮图标尺寸
    static let buttonIconSize: CGFloat = 24
    
    // MARK: - 圆角半径
    
    /// 收缩状态圆角半径
    static let collapsedCornerRadius: CGFloat = 22
    
    /// 展开状态圆角半径
    static let expandedCornerRadius: CGFloat = 22
    
    // MARK: - 阴影参数
    
    /// 阴影半径
    static let shadowRadius: CGFloat = 8
    
    /// 阴影偏移
    static let shadowOffset: CGSize = CGSize(width: 0, height: 4)
    
    /// 阴影透明度
    static let shadowOpacity: Double = 0.2
    
    // MARK: - 边框参数
    
    /// 边框宽度
    static let borderWidth: CGFloat = 0.5
    
    // MARK: - 内边距
    
    /// 展开状态水平内边距
    static let expandedHorizontalPadding: CGFloat = 16
    
    /// 展开状态垂直内边距
    static let expandedVerticalPadding: CGFloat = 8
    
    /// 元素间距
    static let elementSpacing: CGFloat = 12
    
    // MARK: - 计算属性
    
    /// 收缩状态播放器的中心半径（用于碰撞检测）
    static var collapsedRadius: CGFloat {
        return max(collapsedWidth, collapsedHeight) / 2
    }
    
    /// 展开状态播放器的中心半径（用于碰撞检测）
    static var expandedRadius: CGFloat {
        return expandedHeight / 2
    }
    
    /// 播放器的最大尺寸（用于边界计算）
    static var maxSize: CGSize {
        return CGSize(
            width: max(collapsedWidth, expandedMaxWidth),
            height: max(collapsedHeight, expandedHeight)
        )
    }
    
    /// 播放器的最小尺寸
    static var minSize: CGSize {
        return CGSize(
            width: min(collapsedWidth, expandedMinWidth),
            height: min(collapsedHeight, expandedHeight)
        )
    }
    
    // MARK: - 辅助方法
    
    /// 获取指定状态下的播放器尺寸
    /// - Parameter isExpanded: 是否为展开状态
    /// - Returns: 播放器尺寸
    static func size(isExpanded: Bool) -> CGSize {
        if isExpanded {
            return CGSize(width: expandedMaxWidth, height: expandedHeight)
        } else {
            return CGSize(width: collapsedWidth, height: collapsedHeight)
        }
    }
    
    /// 获取指定状态下的圆角半径
    /// - Parameter isExpanded: 是否为展开状态
    /// - Returns: 圆角半径
    static func cornerRadius(isExpanded: Bool) -> CGFloat {
        return isExpanded ? expandedCornerRadius : collapsedCornerRadius
    }
    
    /// 获取指定状态下的图标尺寸
    /// - Parameter isExpanded: 是否为展开状态
    /// - Returns: 图标尺寸
    static func iconSize(isExpanded: Bool) -> CGFloat {
        return isExpanded ? expandedIconSize : collapsedIconSize
    }
    
    /// 检查两个播放器是否可能发生碰撞
    /// - Parameters:
    ///   - position1: 第一个播放器位置
    ///   - position2: 第二个播放器位置
    ///   - isExpanded1: 第一个播放器是否展开
    ///   - isExpanded2: 第二个播放器是否展开
    /// - Returns: 是否可能碰撞
    static func wouldCollide(
        position1: CGPoint,
        position2: CGPoint,
        isExpanded1: Bool = false,
        isExpanded2: Bool = false
    ) -> Bool {
        let radius1 = isExpanded1 ? expandedRadius : collapsedRadius
        let radius2 = isExpanded2 ? expandedRadius : collapsedRadius
        let minDistance = radius1 + radius2 + minimumClearance
        
        let distance = sqrt(
            pow(position1.x - position2.x, 2) + 
            pow(position1.y - position2.y, 2)
        )
        
        return distance < minDistance
    }
}

// MARK: - 调试支持

extension AudioPlayerDimensions {

    /// 获取所有尺寸信息的调试描述
    static var debugDescription: String {
        return """
        音频播放器尺寸常量:
        - 收缩状态: \(collapsedWidth) x \(collapsedHeight)
        - 展开状态: \(expandedMaxWidth) x \(expandedHeight)
        - 安全边距: \(safetyMargin)
        - 最小间距: \(minimumClearance)
        - 收缩圆角: \(collapsedCornerRadius)
        - 展开圆角: \(expandedCornerRadius)
        - 阴影半径: \(shadowRadius)
        - 边框宽度: \(borderWidth)
        """
    }
}
