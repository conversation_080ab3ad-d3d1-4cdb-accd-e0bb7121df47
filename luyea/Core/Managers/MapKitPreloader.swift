import Foundation
import MapKit
import SwiftUI

/// MapKit预加载管理器
///
/// 在应用启动时预初始化MapKit框架，减少首次使用地图时的延迟。
/// 这是一个全局单例，在AppDelegate或App启动时调用。
@MainActor
final class MapKitPreloader: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = MapKitPreloader()
    
    // MARK: - Properties
    
    /// 是否已预加载
    @Published private(set) var isPreloaded = false
    
    /// 预加载开始时间
    private var preloadStartTime: Date?
    
    // MARK: - Initialization
    
    private init() {
        Log.info("🗺️ MapKit预加载管理器初始化")
    }
    
    // MARK: - Public Methods
    
    /// 预加载MapKit框架
    /// 
    /// 在应用启动时调用，通过创建一个隐藏的地图视图来触发MapKit框架加载。
    /// 这个操作在后台进行，不会影响主界面的显示。
    func preloadMapKit() {
        guard !isPreloaded else {
            Log.debug("🗺️ MapKit已预加载，跳过")
            return
        }
        
        preloadStartTime = Date()
        Log.info("🗺️ 开始预加载MapKit框架")
        
        Task.detached(priority: .utility) {
            await self.performPreload()
        }
    }
    
    /// 获取预加载耗时
    var preloadDuration: TimeInterval? {
        guard let startTime = preloadStartTime, isPreloaded else { return nil }
        return Date().timeIntervalSince(startTime)
    }
    
    // MARK: - Private Methods
    
    /// 执行预加载
    private func performPreload() async {
        do {
            // 创建一个最小化的地图配置来触发框架加载
            let _ = MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074), // 北京
                span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
            )
            
            // 在后台创建地图相关对象，触发框架加载
            let _ = MKMapView()
            let _ = MKPointAnnotation()
            
            // 模拟地图初始化过程
            try await Task.sleep(for: .milliseconds(100))
            
            await MainActor.run {
                isPreloaded = true
                if let duration = preloadDuration {
                    Log.info("🗺️ MapKit预加载完成，耗时: \(String(format: "%.2f", duration))秒")
                } else {
                    Log.info("🗺️ MapKit预加载完成")
                }
            }
            
        } catch {
            Log.error("🗺️ MapKit预加载失败: \(error)")
            await MainActor.run {
                isPreloaded = false
            }
        }
    }
}

// MARK: - App Integration

extension MapKitPreloader {
    
    /// 在App启动时调用的便捷方法
    static func initializeOnAppLaunch() {
        Task {
            MapKitPreloader.shared.preloadMapKit()
        }
    }
}
