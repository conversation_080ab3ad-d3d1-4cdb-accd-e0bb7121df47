import SwiftUI

/// 数字输入文本框
///
/// 专门用于数字输入的文本框组件，提供以下功能：
/// - 只允许输入数字
/// - 自动格式化显示
/// - 范围限制
/// - 清空按钮
/// - 输入验证
struct NumericTextField: View {
    
    // MARK: - Properties
    
    @Binding var value: String
    let placeholder: String
    let prefix: String?
    let suffix: String?
    let maxValue: Double?
    let allowDecimal: Bool
    let maxDecimalPlaces: Int
    
    // MARK: - State
    
    @State private var isEditing: Bool = false
    @FocusState private var isFocused: Bool
    
    // MARK: - Initialization
    
    /// 初始化数字输入框
    /// - Parameters:
    ///   - value: 绑定的值
    ///   - placeholder: 占位符文本
    ///   - prefix: 前缀（如 "¥"）
    ///   - suffix: 后缀（如 "元"）
    ///   - maxValue: 最大值限制
    ///   - allowDecimal: 是否允许小数
    ///   - maxDecimalPlaces: 最大小数位数
    init(
        value: Binding<String>,
        placeholder: String = "请输入数字",
        prefix: String? = nil,
        suffix: String? = nil,
        maxValue: Double? = nil,
        allowDecimal: Bool = false,
        maxDecimalPlaces: Int = 2
    ) {
        self._value = value
        self.placeholder = placeholder
        self.prefix = prefix
        self.suffix = suffix
        self.maxValue = maxValue
        self.allowDecimal = allowDecimal
        self.maxDecimalPlaces = maxDecimalPlaces
    }
    
    // MARK: - Body
    
    var body: some View {
        HStack(spacing: 8) {
            // 前缀
            if let prefix = prefix {
                Text(prefix)
                    .font(.body.weight(.medium))
                    .foregroundColor(.primary)
            }
            
            // 输入框
            TextField(placeholder, text: $value)
                .font(.body)
                .keyboardType(allowDecimal ? .decimalPad : .numberPad)
                .textFieldStyle(PlainTextFieldStyle())
                .focused($isFocused)
                .onChange(of: value) { _, newValue in
                    validateAndFormatInput(newValue)
                }
                .onChange(of: isFocused) { _, focused in
                    isEditing = focused
                }
            
            // 后缀
            if let suffix = suffix {
                Text(suffix)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            // 清空按钮
            if !value.isEmpty && isEditing {
                Button(action: { value = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.gray)
                        .frame(width: 20, height: 20)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - Private Methods
    
    /// 验证和格式化输入
    private func validateAndFormatInput(_ input: String) {
        var filtered = input
        
        if allowDecimal {
            // 允许小数：只保留数字和一个小数点
            let components = input.components(separatedBy: ".")
            if components.count > 2 {
                // 多个小数点，只保留第一个
                filtered = components[0] + "." + components[1...].joined()
            }
            
            // 过滤非数字字符（除了小数点）
            filtered = String(filtered.filter { $0.isNumber || $0 == "." })
            
            // 限制小数位数
            if let dotIndex = filtered.firstIndex(of: ".") {
                let decimalPart = filtered[filtered.index(after: dotIndex)...]
                if decimalPart.count > maxDecimalPlaces {
                    let endIndex = filtered.index(dotIndex, offsetBy: maxDecimalPlaces + 1)
                    filtered = String(filtered[..<endIndex])
                }
            }
        } else {
            // 不允许小数：只保留数字
            filtered = String(input.filter { $0.isNumber })
        }
        
        // 检查最大值限制
        if let maxValue = maxValue, let numericValue = Double(filtered) {
            if numericValue > maxValue {
                filtered = String(Int(maxValue))
            }
        }
        
        // 更新值（如果有变化）
        if filtered != input {
            value = filtered
        }
    }
}

// MARK: - 预设样式

extension NumericTextField {
    
    /// 货币输入样式
    static func currency(
        value: Binding<String>,
        placeholder: String = "如：5000"
    ) -> NumericTextField {
        NumericTextField(
            value: value,
            placeholder: placeholder,
            prefix: "¥",
            maxValue: 999999999,
            allowDecimal: false
        )
    }
    
    /// 价格输入样式（支持小数）
    static func price(
        value: Binding<String>,
        placeholder: String = "如：99.99"
    ) -> NumericTextField {
        NumericTextField(
            value: value,
            placeholder: placeholder,
            prefix: "¥",
            maxValue: 999999999,
            allowDecimal: true,
            maxDecimalPlaces: 2
        )
    }
    
    /// 数量输入样式
    static func quantity(
        value: Binding<String>,
        placeholder: String = "数量",
        maxValue: Double = 999999
    ) -> NumericTextField {
        NumericTextField(
            value: value,
            placeholder: placeholder,
            maxValue: maxValue,
            allowDecimal: false
        )
    }
    
    /// 百分比输入样式
    static func percentage(
        value: Binding<String>,
        placeholder: String = "如：85"
    ) -> NumericTextField {
        NumericTextField(
            value: value,
            placeholder: placeholder,
            suffix: "%",
            maxValue: 100,
            allowDecimal: true,
            maxDecimalPlaces: 1
        )
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // 货币输入
        VStack(alignment: .leading) {
            Text("预算金额")
                .font(.headline)
            
            NumericTextField.currency(
                value: .constant("5000"),
                placeholder: "请输入预算"
            )
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.default))
        }
        
        // 价格输入
        VStack(alignment: .leading) {
            Text("商品价格")
                .font(.headline)
            
            NumericTextField.price(
                value: .constant("99.99"),
                placeholder: "请输入价格"
            )
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.default))
        }
        
        // 数量输入
        VStack(alignment: .leading) {
            Text("购买数量")
                .font(.headline)
            
            NumericTextField.quantity(
                value: .constant("10"),
                placeholder: "请输入数量"
            )
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.default))
        }
        
        Spacer()
    }
    .padding()
}
