import SwiftUI

/// 发现详情页内容视图
///
/// 展示发现内容的详细信息，包括图片轮播、内容信息、互动按钮和评论区。
/// 遵循MVVM架构，通过ViewModel处理业务逻辑。
struct DiscoverDetailContentView: View {

    // MARK: - Properties

    /// 发现项目数据
    let item: DiscoverItem

    /// 环境变量：页面关闭
    @Environment(\.dismiss) private var dismiss

    /// 视图模型
    @StateObject private var viewModel: DiscoverDetailViewModel

    // MARK: - Initialization

    /// 初始化详情页视图
    /// - Parameter item: 发现项目数据
    init(item: DiscoverItem) {
        self.item = item
        self._viewModel = StateObject(wrappedValue: DiscoverDetailViewModel(item: item))
    }

    var body: some View {
        ZStack(alignment: .topLeading) {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 顶部轮播图
                    ZStack(alignment: .bottom) {
                        ImageCarouselView(
                            imageUrls: item.imageUrls,
                            currentPage: $viewModel.currentPage,
                            isAutoScrolling: $viewModel.isAutoScrolling,
                            onImageTap: {
                                viewModel.showFullScreenImageViewer()
                            },
                            onZoomChanged: { isZoomed in
                                viewModel.handleZoomChanged(isZoomed)
                            }
                        )

                        // 底部覆盖层
                        HStack {
                            AuthorInfoView(
                                avatarUrl: item.userAvatarUrl,
                                username: item.username,
                                onTap: {
                                    viewModel.showAuthorProfile()
                                }
                            )

                            Spacer()

                            PageIndicatorView(
                                totalPages: item.imageUrls.count,
                                currentPage: viewModel.currentPage
                            )
                        }
                        .padding(.horizontal, 16)
                        .padding(.bottom, 16)
                    }
                    .frame(height: 380)
                    .ignoresSafeArea()

                    // 内容信息
                    ContentInfoView(
                        title: item.title,
                        description: item.description,
                        location: item.location ?? "",
                        tags: ["旅行", "摄影", "美食"] // 这里可以从item中获取
                    )

                    // 互动按钮
                    InteractionButtonsView(
                        isLiked: $viewModel.isLiked,
                        likeCount: viewModel.likeCount,
                        commentCount: viewModel.commentCount,
                        shareCount: 23,
                        onLike: {
                            viewModel.handleLike()
                        },
                        onComment: {
                            viewModel.showCommentInput = true
                        },
                        onShare: {
                            viewModel.handleShare()
                        }
                    )

                    // 评论区
                    VStack(alignment: .leading, spacing: 12) {
                        Text("评论")
                            .font(.headline)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 16)

                        ForEach(item.comments) { comment in
                            CommentView(comment: comment)
                                .padding(.horizontal, 16)
                        }

                        // 评论输入提示
                        Button(action: { viewModel.showCommentInput = true }) {
                            HStack {
                                Circle()
                                    .fill(Color.gray.opacity(0.2))
                                    .frame(width: 32, height: 32)
                                    .overlay(
                                        Text("我")
                                            .font(.caption)
                                            .foregroundColor(.gray)
                                    )

                                Text("说点什么...")
                                    .foregroundColor(.gray)
                                    .font(.subheadline)

                                Spacer()
                            }
                            .padding(12)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(20)
                        }
                        .padding(.horizontal, 16)
                    }
                }
            }
            .ignoresSafeArea(edges: .top)
            .onAppear {
                if viewModel.currentPage >= item.imageUrls.count {
                    viewModel.currentPage = 0
                }
            }

            // 顶部导航按钮
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 40, height: 40)
                        .background(Color.black.opacity(0.3))
                        .clipShape(Circle())
                }

                Spacer()

                Button(action: { viewModel.handleLike() }) {
                    HStack(spacing: 4) {
                        Image(systemName: viewModel.isLiked ? "heart.fill" : "heart")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(viewModel.isLiked ? .red : .white)
                        Text("\(viewModel.likeCount)")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .frame(height: 40)
                    .padding(.horizontal, 12)
                    .background(Color.black.opacity(0.3))
                    .clipShape(Capsule())
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)
        }
        .hideTabBar()
        .navigationBarHidden(true)
        .overlay {
            // 评论输入组件
            VStack {
                Spacer()
                CommentInputView(
                    commentText: $viewModel.commentText,
                    isVisible: $viewModel.showCommentInput,
                    onSubmit: {
                        viewModel.handleCommentSubmission()
                    }
                )
            }
        }
        .overlay {
            if viewModel.showFullScreenViewer {
                FullScreenImageViewerView(
                    imageUrls: item.imageUrls,
                    currentPage: $viewModel.currentPage,
                    isPresented: $viewModel.showFullScreenViewer,
                    onZoomChanged: { isZoomed in
                        viewModel.handleZoomChanged(isZoomed)
                    }
                )
                .transition(.asymmetric(
                    insertion: .scale(scale: 0.3, anchor: .center)
                        .combined(with: .opacity),
                    removal: .scale(scale: 0.3, anchor: .center)
                        .combined(with: .opacity)
                ))
                .zIndex(1000)
            }
        }
        .sheet(isPresented: $viewModel.isAuthorProfilePresented) {
            UserProfileView(
                username: item.username,
                avatarUrl: item.userAvatarUrl,
                isCurrentUser: false,
                contentTags: [
                    "美食探店", "城市漫步", "历史文化",
                    "自然风光", "艺术展览", "特色民宿",
                    "主题乐园", "购物指南"
                ]
            )
        }
    }
}



// MARK: - Preview

#Preview {
    DiscoverDetailContentView(
        item: DiscoverItem(
            imageUrls: [
                "https://c-ssl.dtstatic.com/uploads/blog/202408/14/9WSP7q6eh8wwMP6.thumb.1000_0.jpg",
                "https://img2.baidu.com/it/u=3563687558,836722079&fm=253&fmt=auto?w=606&h=1216"
            ],
            title: "示例标题",
            username: "用户名",
            userAvatarUrl: "https://img1.baidu.com/it/u=1747081318,2650263390&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",
            likes: 100,
            description: "这是一段示例描述文本，展示了详情页的内容布局。",
            comments: [
                Comment(username: "用户1", content: "这是一条评论"),
                Comment(username: "用户2", content: "这是另一条评论")
            ]
        )
    )
}
