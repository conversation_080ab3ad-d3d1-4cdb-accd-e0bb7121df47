import Foundation
import SwiftUI
import Combine

/// 景点搜索服务常量
private enum AttractionSearchConstants {
    /// 热门搜索关键词
    static let popularQueries = ["西湖", "故宫", "长城", "外滩", "天安门", "颐和园"]
}

/// 景点搜索管理器
///
/// 管理景点搜索功能，提供以下特性：
/// - 防抖搜索，减少不必要的搜索请求
/// - 智能缓存，避免重复搜索
/// - 异步处理，不阻塞主线程
/// - 结果预加载，提升响应速度
/// - 内存管理，防止缓存过大
@MainActor
final class AttractionSearchManager: ObservableObject {

    // MARK: - Singleton

    static let shared = AttractionSearchManager()

    // MARK: - Published Properties

    /// 搜索结果
    @Published var searchResults: [AttractionModel] = []

    /// 是否正在搜索
    @Published var isSearching: Bool = false

    /// 热门搜索关键词
    @Published var popularQueries: [String] = AttractionSearchConstants.popularQueries

    // MARK: - Private Properties

    /// 搜索缓存
    private var searchCache: [String: [AttractionModel]] = [:]

    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()

    /// 搜索防抖器（统一管理搜索任务和防抖）
    private let searchDebouncer = Debouncer(delay: DesignSystemConstants.Interaction.debounceDelay)

    /// 缓存大小限制
    private let maxCacheSize = 50

    // MARK: - Initialization

    private init() {
        setupObservation()
        preloadPopularResults()
        Log.info("🔍 景点搜索管理器初始化完成")
    }

    deinit {
        // searchDebouncer 会在自己的 deinit 中自动取消任务
        Log.debug("🔍 景点搜索管理器已销毁")
    }

    // MARK: - Public Methods

    /// 搜索景点
    /// - Parameter query: 搜索关键词
    func search(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)

        // 空查询时清空结果
        guard !trimmedQuery.isEmpty else {
            clearResults()
            return
        }

        // 检查缓存
        if let cachedResults = searchCache[trimmedQuery] {
            Log.debug("🔍 使用缓存结果: \(trimmedQuery)")
            updateResults(cachedResults)
            return
        }

        // 使用统一的防抖工具（自动处理任务取消）
        searchDebouncer.call {
            Task { @MainActor in
                await self.performSearchInternal(query: trimmedQuery, updateUI: true)
            }
        }
    }

    /// 清空搜索结果
    func clearResults() {
        searchDebouncer.cancel()

        DispatchQueue.main.async {
            self.searchResults.removeAll()
            self.isSearching = false
        }

        Log.debug("🔍 清空搜索结果")
    }

    /// 清空缓存
    func clearCache() {
        searchCache.removeAll()
        Log.debug("🔍 清空搜索缓存")
    }

    /// 预加载热门搜索结果
    func preloadPopularResults() {
        Task {
            for query in popularQueries.prefix(3) {
                await performSearchInternal(query: query, updateUI: false)
            }
            Log.debug("🔍 热门搜索结果预加载完成")
        }
    }

    // MARK: - Private Methods

    /// 设置观察
    private func setupObservation() {
        // 监听内存警告
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                self?.handleMemoryWarning()
            }
            .store(in: &cancellables)
    }



    /// 内部搜索实现
    private func performSearchInternal(query: String, updateUI: Bool) async {
        if updateUI {
            await MainActor.run {
                isSearching = true
            }
        }

        Log.debug("🔍 开始搜索: \(query)")

        do {
            // 模拟网络搜索延迟
            try await Task.sleep(nanoseconds: 200_000_000) // 0.2秒

            // 检查任务是否被取消
            try Task.checkCancellation()

            // 执行搜索
            let results = performActualSearch(query: query)

            // 缓存结果
            cacheResults(query: query, results: results)

            if updateUI {
                await MainActor.run {
                    updateResults(results)
                }
            }

            Log.debug("🔍 搜索完成: \(query), 结果数量: \(results.count)")

        } catch {
            if updateUI {
                await MainActor.run {
                    isSearching = false
                }
            }

            if !(error is CancellationError) {
                Log.error("🔍 搜索失败: \(query), 错误: \(error)")
            }
        }
    }

    /// 执行实际搜索
    private func performActualSearch(query: String) -> [AttractionModel] {
        return AttractionModel.samples.filter { attraction in
            attraction.name.localizedCaseInsensitiveContains(query) ||
            attraction.city.localizedCaseInsensitiveContains(query) ||
            attraction.tags.joined().localizedCaseInsensitiveContains(query)
        }
    }

    /// 更新搜索结果
    private func updateResults(_ results: [AttractionModel]) {
        searchResults = results
        isSearching = false
    }

    /// 缓存搜索结果
    private func cacheResults(query: String, results: [AttractionModel]) {
        // 限制缓存大小
        if searchCache.count >= maxCacheSize {
            // 移除最旧的缓存项
            let oldestKey = searchCache.keys.first
            if let key = oldestKey {
                searchCache.removeValue(forKey: key)
            }
        }

        searchCache[query] = results
        Log.debug("🔍 缓存搜索结果: \(query)")
    }

    /// 取消当前搜索
    private func cancelCurrentSearch() {
        searchDebouncer.cancel()
    }

    /// 处理内存警告
    private func handleMemoryWarning() {
        // 清空一半的缓存
        let keysToRemove = Array(searchCache.keys.prefix(searchCache.count / 2))
        keysToRemove.forEach { searchCache.removeValue(forKey: $0) }

        Log.debug("🔍 内存警告，清理缓存")
    }
}

// MARK: - Extensions

extension AttractionSearchManager {

    /// 获取搜索建议
    func getSearchSuggestions(for query: String) -> [String] {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else {
            return popularQueries
        }

        // 从热门搜索中筛选匹配的建议
        let suggestions = popularQueries.filter {
            $0.localizedCaseInsensitiveContains(trimmedQuery)
        }

        return suggestions.isEmpty ? popularQueries.prefix(3).map { $0 } : suggestions
    }

    /// 添加到热门搜索
    func addToPopularQueries(_ query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty && !popularQueries.contains(trimmedQuery) else {
            return
        }

        popularQueries.insert(trimmedQuery, at: 0)

        // 限制热门搜索数量
        if popularQueries.count > 10 {
            popularQueries = Array(popularQueries.prefix(10))
        }

        Log.debug("🔍 添加到热门搜索: \(trimmedQuery)")
    }
}
