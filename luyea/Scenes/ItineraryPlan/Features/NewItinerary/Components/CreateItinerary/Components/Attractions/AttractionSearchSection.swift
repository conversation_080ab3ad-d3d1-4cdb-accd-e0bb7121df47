import Foundation
import SwiftUI

/// 景点搜索区域组件
///
/// 功能特性：
/// - 景点搜索功能
/// - 收藏地点展示
/// - 搜索结果展示
/// - 景点选择管理
struct AttractionSearchSection: View {
    
    // MARK: - Properties

    @Binding var selectedAttractions: [AttractionModel]
    @Binding var favoriteAttractions: [AttractionModel]
    @Binding var showFavoritesList: Bool
    let onAttractionToggled: ((AttractionModel) -> Void)?
    let onAttractionAddedFromSearch: ((AttractionModel) -> Void)?
    
    // MARK: - State

    @State private var searchText: String = ""
    @State private var showSearchResults: Bool = false

    // MARK: - Dependencies

    @StateObject private var searchManager = AttractionSearchManager.shared
    
    // MARK: - Body
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            HStack {
                Text("添加想去的地点")
                    .font(.title2.weight(.bold))
                    .foregroundColor(.primary)

                Text("可选")
                    .font(.caption.weight(.medium))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.secondary.opacity(0.1))
                    .clipShape(Capsule())

                Spacer()
            }

            // 搜索卡片
            VStack(spacing: 0) {
                attractionSearchField

                if showSearchResults && !searchManager.searchResults.isEmpty {
                    searchResultsList
                } else if searchText.isEmpty {
                    // 默认显示收藏地点
                    favoriteAttractionsSection
                }
            }
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
    }
    
    // MARK: - Private Views
    
    /// 景点搜索输入框
    private var attractionSearchField: some View {
        HStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.title3)
                .foregroundColor(.gray)

            TextField("搜索地点名称（可选），如：西湖、东京塔、银座", text: $searchText)
                .font(.body)
                .onChange(of: searchText) { _, newValue in
                    searchManager.search(query: newValue)
                    showSearchResults = !newValue.isEmpty
                }

            if searchManager.isSearching {
                ProgressView()
                    .scaleEffect(0.8)
            } else if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                    searchManager.clearResults()
                    showSearchResults = false
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(20)
    }

    /// 收藏地点展示区域
    private var favoriteAttractionsSection: some View {
        VStack(spacing: 0) {
            Divider()

            VStack(spacing: 12) {
                // 标题
                HStack {
                    Image(systemName: "heart.fill")
                        .font(.caption)
                        .foregroundColor(.red)

                    Text("收藏的地点")
                        .font(.subheadline.weight(.medium))
                        .foregroundColor(.primary)

                    Spacer()

                    if favoriteAttractions.count > 3 {
                        Button("查看全部") {
                            showFavoritesList = true
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 16)

                // 收藏地点列表
                if favoriteAttractions.isEmpty {
                    emptyFavoritesHint
                } else {
                    favoriteAttractionsList
                }
            }
            .padding(.bottom, 16)
        }
    }

    /// 收藏地点列表
    private var favoriteAttractionsList: some View {
        VStack(spacing: 0) {
            ForEach(Array(favoriteAttractions.prefix(3).enumerated()), id: \.offset) { index, attraction in
                FavoriteAttractionRow(
                    attraction: attraction,
                    isSelected: selectedAttractions.contains(attraction),
                    onTap: {
                        handleAttractionSelection(attraction)
                    }
                )

                if index < min(2, favoriteAttractions.count - 1) {
                    Divider()
                        .padding(.leading, 60)
                }
            }
        }
        .padding(.horizontal, 16)
    }

    /// 空收藏提示
    private var emptyFavoritesHint: some View {
        VStack(spacing: 8) {
            Image(systemName: "heart")
                .font(.title3)
                .foregroundColor(.gray.opacity(0.5))

            Text("暂无收藏的地点")
                .font(.subheadline)
                .foregroundColor(.secondary)

            Text("我们会根据您选择的目的地为您推荐热门地点")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .padding(.vertical, 20)
        .padding(.horizontal, 16)
    }

    /// 搜索结果列表
    private var searchResultsList: some View {
        VStack(spacing: 0) {
            Divider()

            VStack(spacing: 0) {
                ForEach(searchManager.searchResults.indices, id: \.self) { index in
                    let attraction = searchManager.searchResults[index]

                    AttractionRow(
                        attraction: attraction,
                        isSelected: selectedAttractions.contains(attraction),
                        onTap: {
                            handleAttractionSelection(attraction)
                        }
                    )

                    if index < searchManager.searchResults.count - 1 {
                        Divider()
                            .padding(.leading, 76)
                    }
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Private Methods
    


    /// 处理景点选择
    private func handleAttractionSelection(_ attraction: AttractionModel) {
        // 检查是否是添加操作（景点不在已选择列表中）
        let isAddingAttraction = !selectedAttractions.contains(where: { $0.id == attraction.id })

        // 通过回调让ViewModel处理切换逻辑
        // 这样可以确保自动添加城市的逻辑被正确执行
        onAttractionToggled?(attraction)

        // 如果是添加操作，触发滚动回调
        if isAddingAttraction {
            onAttractionAddedFromSearch?(attraction)
        }

        // 清空搜索
        searchText = ""
        searchManager.clearResults()
        showSearchResults = false
    }
}

// MARK: - Preview

#Preview {
    AttractionSearchSection(
        selectedAttractions: .constant([]),
        favoriteAttractions: .constant(Array(AttractionModel.samples.prefix(3))),
        showFavoritesList: .constant(false),
        onAttractionToggled: { attraction in
            // 景点切换回调处理
        },
        onAttractionAddedFromSearch: { attraction in
            // 从搜索添加景点回调处理
        }
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
