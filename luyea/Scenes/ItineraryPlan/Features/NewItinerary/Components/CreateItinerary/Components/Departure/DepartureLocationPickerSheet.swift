import SwiftUI

/// 出发地点选择弹窗
///
/// 功能特性：
/// - 搜索地点功能
/// - 当前位置快速选择
/// - 热门城市推荐
/// - 历史选择记录
///
/// 重构说明：
/// - 拆分为独立的子组件，提高可维护性
/// - 使用数据提供者管理城市数据
/// - 优化搜索性能和用户体验
struct DepartureLocationPickerSheet: View {
    
    // MARK: - Properties
    
    @Binding var selectedLocation: String
    @Binding var isPresented: Bool
    let currentLocation: String
    
    // MARK: - State

    @State private var searchText: String = ""
    @State private var searchResults: [String] = []
    @State private var isSearching = false
    @StateObject private var searchDebouncer = Debouncer(delay: DesignSystemConstants.Interaction.debounceDelay)

    // MARK: - Computed Properties

    /// 当前位置是否可用
    private var isCurrentLocationAvailable: Bool {
        return !currentLocation.contains("正在获取") &&
               !currentLocation.contains("无法获取") &&
               !currentLocation.contains("未知位置") &&
               currentLocation != "正在获取当前位置..."
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 现代化顶部栏
            modernHeaderSection

            // 主要内容区域
            ZStack {
                // 简约背景
                Color(.systemGroupedBackground)
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // 搜索区域
                    modernSearchSection

                    // 内容区域
                    ScrollView {
                        LazyVStack(spacing: 24) {
                            // 当前位置
                            modernCurrentLocationSection

                            // 热门城市
                            modernPopularCitiesSection

                            // 搜索结果
                            if !searchResults.isEmpty {
                                modernSearchResultsSection
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 16)
                        .padding(.bottom, 32)
                    }
                }
            }
        }
        .presentationDetents([.height(600), .large])
        .presentationDragIndicator(.visible)
        .presentationCornerRadius(20)
    }
    
    // MARK: - Modern UI Components

    /// 现代化顶部栏
    private var modernHeaderSection: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            RoundedRectangle(cornerRadius: 2.5)
                .fill(Color(.systemGray4))
                .frame(width: 36, height: 5)
                .padding(.top, 8)
                .padding(.bottom, 16)

            // 标题和关闭按钮
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("选择出发地点")
                        .font(.title2.weight(.bold))
                        .foregroundColor(.primary)

                    Text("请选择您的出发位置")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button(action: { isPresented = false }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.secondary)
                        .background(Color(.systemBackground))
                        .clipShape(Circle())
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 16)
        }
        .background(Color(.systemBackground))
    }

    /// 现代化搜索区域
    private var modernSearchSection: some View {
        VStack(spacing: 0) {
            HStack(spacing: 12) {
                Image(systemName: "magnifyingglass")
                    .font(.title3)
                    .foregroundColor(.secondary)

                TextField("搜索城市或地区", text: $searchText)
                    .font(.body)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onChange(of: searchText) { _, newValue in
                        searchDebouncer.call {
                            Task { @MainActor in
                                self.performSearchInternal(query: newValue)
                            }
                        }
                    }

                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .padding(.horizontal, 20)
            .padding(.bottom, 8)

            // 分隔线
            Rectangle()
                .fill(Color(.systemGray5))
                .frame(height: 0.5)
                .padding(.top, 8)
        }
        .background(Color(.systemBackground))
    }

    /// 现代化当前位置区域
    private var modernCurrentLocationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "location.fill")
                    .font(.title3)
                    .foregroundColor(isCurrentLocationAvailable ? .blue : .gray)

                Text("当前位置")
                    .font(.headline.weight(.semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            Button(action: {
                if isCurrentLocationAvailable {
                    selectLocation(currentLocation)
                }
            }) {
                HStack(spacing: 16) {
                    ZStack {
                        Circle()
                            .fill(isCurrentLocationAvailable ? Color.blue.opacity(0.1) : Color.gray.opacity(0.1))
                            .frame(width: 44, height: 44)

                        Image(systemName: isCurrentLocationAvailable ? "location.fill" : "location.slash")
                            .font(.title3)
                            .foregroundColor(isCurrentLocationAvailable ? .blue : .gray)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(currentLocation)
                            .font(.body.weight(.medium))
                            .foregroundColor(isCurrentLocationAvailable ? .primary : .secondary)
                            .lineLimit(1)

                        Text(isCurrentLocationAvailable ? "使用当前位置" : "位置信息不可用")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    if isCurrentLocationAvailable {
                        Image(systemName: "chevron.right")
                            .font(.caption.weight(.semibold))
                            .foregroundColor(.secondary)
                    } else {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.caption.weight(.semibold))
                            .foregroundColor(.orange)
                    }
                }
                .padding(16)
                .background(isCurrentLocationAvailable ? Color(.systemBackground) : Color(.systemGray6))
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .shadow(color: .black.opacity(isCurrentLocationAvailable ? 0.05 : 0.02), radius: isCurrentLocationAvailable ? 8 : 4, x: 0, y: 2)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(!isCurrentLocationAvailable)
        }
    }

    /// 现代化热门城市区域
    private var modernPopularCitiesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "star.fill")
                    .font(.title3)
                    .foregroundColor(.orange)

                Text("热门城市")
                    .font(.headline.weight(.semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12)
            ], spacing: 12) {
                ForEach(LocationDataService.popularCities, id: \.self) { city in
                    modernCityButton(city: city)
                }
            }
        }
    }

    /// 现代化城市按钮
    private func modernCityButton(city: String) -> some View {
        Button(action: { selectLocation(city) }) {
            Text(city)
                .font(.subheadline.weight(.medium))
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color(.systemBackground))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 现代化搜索结果区域
    private var modernSearchResultsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .font(.title3)
                    .foregroundColor(.green)

                Text("搜索结果")
                    .font(.headline.weight(.semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            LazyVStack(spacing: 8) {
                ForEach(searchResults, id: \.self) { city in
                    modernSearchResultRow(city: city)
                }
            }
        }
    }

    /// 现代化搜索结果行
    private func modernSearchResultRow(city: String) -> some View {
        Button(action: { selectLocation(city) }) {
            HStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(Color.green.opacity(0.1))
                        .frame(width: 36, height: 36)

                    Image(systemName: "location")
                        .font(.subheadline)
                        .foregroundColor(.green)
                }

                Text(city)
                    .font(.body.weight(.medium))
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption.weight(.semibold))
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Legacy Views (Deprecated)

    /// 搜索结果区域
    private var searchResultsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .font(.title3)
                    .foregroundColor(.green)
                
                Text("搜索结果")
                    .font(.headline.weight(.medium))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                ForEach(searchResults, id: \.self) { city in
                    searchResultRow(city: city)
                }
            }
        }
    }
    
    /// 搜索结果行
    private func searchResultRow(city: String) -> some View {
        Button(action: {
            selectLocation(city)
        }) {
            HStack(spacing: 12) {
                Image(systemName: "location")
                    .font(.subheadline)
                    .foregroundColor(.gray)

                Text(city)
                    .font(.body)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption.weight(.semibold))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Private Methods

    /// 选择位置
    private func selectLocation(_ location: String) {
        selectedLocation = location
        isPresented = false
    }

    /// 执行搜索（带防抖）
    private func performSearch(query: String) {
        searchDebouncer.call {
            Task { @MainActor in
                self.performSearchInternal(query: query)
            }
        }
    }

    /// 内部搜索实现（不带防抖）
    private func performSearchInternal(query: String) {
        guard !query.isEmpty else {
            searchResults = []
            return
        }

        isSearching = true

        // 直接执行搜索，不需要额外延迟
        searchResults = LocationDataService.getSearchSuggestions(query: query)
        isSearching = false
    }
}

// MARK: - Preview

#Preview {
    DepartureLocationPickerSheet(
        selectedLocation: .constant(""),
        isPresented: .constant(true),
        currentLocation: "北京市"
    )
}
