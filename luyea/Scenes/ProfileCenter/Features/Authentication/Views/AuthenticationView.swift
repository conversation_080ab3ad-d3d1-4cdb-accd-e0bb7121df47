import SwiftUI

struct AuthenticationView: View {
    @StateObject private var viewModel = AuthenticationViewModel()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            AuthenticationContentView(viewModel: viewModel)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("关闭") {
                            dismiss()
                        }
                        .foregroundColor(.secondary)
                    }
                }
        }
        .onChange(of: viewModel.authState) { _, newState in
            if case .authenticated = newState {
                dismiss()
            }
        }
    }
}

// MARK: - Preview

#Preview("Authentication视图") {
    AuthenticationView()
}
