import Foundation
import CoreLocation
import Combine

/// 高性能位置管理器，采用饿汉式加载策略
///
/// 优化特性：
/// - 饿汉式加载，应用启动时立即初始化Core Location框架
/// - 共享实例避免重复初始化
/// - 智能缓存和权限管理
/// - 自动停止定位节省电量
/// - 零延迟使用体验
class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {

    // MARK: - Shared Instance

    /// 共享实例，采用饿汉式加载策略
    /// 在应用启动时立即初始化，确保使用时零延迟
    static let shared: LocationManager = {
        let instance = LocationManager()
        Log.info("📍 LocationManager共享实例初始化完成（饿汉式加载）")
        return instance
    }()

    /// 预加载方法，确保在应用启动时就完成初始化
    /// 调用此方法可以在应用启动早期就预热Core Location框架
    @discardableResult
    static func preload() -> LocationManager {
        Log.info("📍 LocationManager预加载开始")
        let startTime = CFAbsoluteTimeGetCurrent()

        // 访问shared实例，触发饿汉式加载
        let instance = shared

        let loadTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
        Log.success("📍 LocationManager预加载完成，耗时: \(String(format: "%.2f", loadTime))ms")

        return instance
    }

    // MARK: - Properties

    /// 饿汉式加载的位置管理器，在初始化时立即创建
    private let locationManager: CLLocationManager

    @Published var location: CLLocation? = nil
    @Published var authorizationStatus: CLAuthorizationStatus

    /// 位置缓存，避免重复请求
    private var cachedLocation: CLLocation?
    private var lastLocationUpdate: Date?
    private let cacheValidDuration: TimeInterval = 300 // 5分钟缓存

    /// 定位状态管理
    private var isLocationUpdating = false
    private var locationUpdateTimer: Timer?

    // MARK: - Initialization

    override init() {
        // 饿汉式加载：立即创建和配置CLLocationManager
        self.locationManager = CLLocationManager()
        self.authorizationStatus = self.locationManager.authorizationStatus

        super.init()

        // 立即配置位置管理器
        setupLocationManager()

        Log.info("📍 LocationManager初始化完成（饿汉式加载，Core Location框架已预热）")
    }

    /// 配置位置管理器
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters

        // 预热Core Location框架：通过访问属性来初始化框架，但不立即请求位置
        // 这样可以避免在没有权限时产生错误，同时完成框架初始化
        _ = locationManager.location
        _ = locationManager.authorizationStatus

        Log.debug("📍 CLLocationManager配置完成，Core Location框架已预热")
    }
    
    // MARK: - Public Methods

    /// 智能请求位置权限，支持缓存和性能优化
    func requestLocationPermission() {
        // 更新当前权限状态
        authorizationStatus = locationManager.authorizationStatus

        // 检查是否有有效的缓存位置
        if let cached = getCachedLocationIfValid() {
            Log.debug("📍 使用缓存位置，跳过定位请求")
            self.location = cached
            return
        }

        switch authorizationStatus {
        case .notDetermined:
            Log.info("📍 请求位置权限")
            locationManager.requestWhenInUseAuthorization()
        case .authorizedWhenInUse, .authorizedAlways:
            Log.debug("📍 权限已授权，开始定位")
            startLocationUpdates()
        case .denied, .restricted:
            Log.warning("📍 位置权限被拒绝或受限")
        @unknown default:
            Log.warning("📍 未知的位置权限状态")
        }
    }

    /// 异步请求位置权限 - 避免阻塞UI
    func requestLocationPermission() async {
        await Task { @MainActor in
            requestLocationPermission()
        }.value
    }

    /// 获取当前位置（优先使用缓存）
    func getCurrentLocation() -> CLLocation? {
        return getCachedLocationIfValid() ?? location
    }

    /// 强制刷新位置（忽略缓存）
    func forceRefreshLocation() {
        invalidateCache()
        requestLocationPermission()
    }

    // MARK: - Private Methods

    /// 获取有效的缓存位置
    private func getCachedLocationIfValid() -> CLLocation? {
        guard let cached = cachedLocation,
              let lastUpdate = lastLocationUpdate,
              Date().timeIntervalSince(lastUpdate) < cacheValidDuration else {
            return nil
        }
        return cached
    }

    /// 清除位置缓存
    private func invalidateCache() {
        cachedLocation = nil
        lastLocationUpdate = nil
        Log.debug("📍 位置缓存已清除")
    }

    /// 开始位置更新（带防重复机制）
    private func startLocationUpdates() {
        guard authorizationStatus == .authorizedWhenInUse || authorizationStatus == .authorizedAlways else {
            Log.warning("📍 位置权限不足，无法开始定位")
            return
        }

        guard !isLocationUpdating else {
            Log.debug("📍 位置更新已在进行中，跳过重复请求")
            return
        }

        isLocationUpdating = true
        locationManager.startUpdatingLocation()
        Log.info("📍 开始位置更新")

        // 设置超时机制，避免长时间定位
        locationUpdateTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: false) { [weak self] _ in
            self?.stopLocationUpdates()
            Log.warning("📍 定位超时，自动停止")
        }
    }

    /// 停止位置更新
    private func stopLocationUpdates() {
        guard isLocationUpdating else { return }

        isLocationUpdating = false
        locationManager.stopUpdatingLocation()
        locationUpdateTimer?.invalidate()
        locationUpdateTimer = nil
        Log.debug("📍 位置更新已停止")
    }

    // MARK: - CLLocationManagerDelegate

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        Log.info("📍 位置权限状态变更: \(authorizationStatusString(status))")

        DispatchQueue.main.async { [weak self] in
            self?.authorizationStatus = status

            // 权限获得后自动开始定位
            if status == .authorizedWhenInUse || status == .authorizedAlways {
                self?.startLocationUpdates()
            } else if status == .denied || status == .restricted {
                self?.stopLocationUpdates()
                Log.warning("📍 位置权限被拒绝，停止定位服务")
            }
        }
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let newLocation = locations.last else { return }

        // 过滤无效位置
        guard newLocation.horizontalAccuracy < 100 && newLocation.horizontalAccuracy > 0 else {
            Log.debug("📍 位置精度不足，继续定位")
            return
        }

        Log.success("📍 获取到有效位置: \(newLocation.coordinate)")

        DispatchQueue.main.async { [weak self] in
            self?.location = newLocation
            self?.cachedLocation = newLocation
            self?.lastLocationUpdate = Date()

            // 获取到有效位置后停止定位，节省电量
            self?.stopLocationUpdates()
        }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        Log.error("📍 定位失败: \(error.localizedDescription)")

        DispatchQueue.main.async { [weak self] in
            self?.stopLocationUpdates()

            // 根据错误类型进行不同处理
            if let clError = error as? CLError {
                switch clError.code {
                case .denied:
                    Log.warning("📍 位置权限被拒绝")
                case .network:
                    Log.warning("📍 网络错误，稍后重试")
                case .locationUnknown:
                    Log.warning("📍 无法确定位置，使用默认位置")
                default:
                    Log.error("📍 其他定位错误: \(clError.localizedDescription)")
                }
            }
        }
    }

    // MARK: - Helper Methods

    /// 获取权限状态的字符串描述
    private func authorizationStatusString(_ status: CLAuthorizationStatus) -> String {
        switch status {
        case .notDetermined: return "未确定"
        case .restricted: return "受限"
        case .denied: return "拒绝"
        case .authorizedAlways: return "始终允许"
        case .authorizedWhenInUse: return "使用时允许"
        @unknown default: return "未知状态"
        }
    }

    /// 清理资源
    deinit {
        stopLocationUpdates()
        Log.debug("📍 LocationManager已释放")
    }
}