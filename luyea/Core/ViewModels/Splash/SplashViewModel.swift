import SwiftUI
import Combine

/// 启动页视图模型
///
/// 负责管理启动页的显示逻辑、倒计时控制和配置管理。
/// 支持多种内容类型和用户交互（跳过、点击等）。
@MainActor
final class SplashViewModel: ObservableObject {
    // MARK: - Published Properties

    /// 剩余显示时间（秒）
    @Published var duration: Int = 1

    /// 当前内容类型
    @Published var currentContentType: SplashContentType = .native

    /// 是否显示启动页
    @Published var isShowingSplash: Bool = true

    /// 是否显示跳过按钮
    @Published var showSkipButton: Bool = false

    /// 是否正在加载配置
    @Published var isLoadingConfig: Bool = false

    /// 配置加载错误
    @Published var configError: Error?

    // MARK: - Private Properties

    /// 启动数据服务
    private let launchService: LaunchDataServiceProtocol

    /// 倒计时任务
    private var countdownTask: Task<Void, Never>?

    /// 跳过按钮显示任务
    private var skipButtonTask: Task<Void, Never>?

    /// 原始配置持续时间
    private var originalDuration: Int = 1

    /// 是否已手动跳过
    private var hasManuallySkipped: Bool = false

    /// 取消令牌集合
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Computed Properties

    /// 当前启动页配置
    var currentConfig: SplashConfig {
        launchService.splashConfig
    }

    /// 是否可以跳过
    var canSkip: Bool {
        currentConfig.allowSkip && showSkipButton
    }

    /// 进度百分比（0.0 - 1.0）
    var progress: Double {
        guard originalDuration > 0 else { return 1.0 }
        return 1.0 - Double(duration) / Double(originalDuration)
    }

    // MARK: - Initialization

    /// 初始化方法
    ///
    /// - Parameter launchService: 启动数据服务，默认使用共享实例
    init(launchService: LaunchDataServiceProtocol = LaunchDataService.shared) {
        self.launchService = launchService

        setupObservers()

        Log.info("🚀 启动页视图模型初始化完成")
    }

    deinit {
        // 清理资源
        countdownTask?.cancel()
        skipButtonTask?.cancel()
        Log.debug("🚀 启动页视图模型已销毁")
    }

    // MARK: - Public Methods

    /// 设置启动页配置
    ///
    /// 从启动数据服务获取配置并应用到视图模型。
    func setupSplashConfig() {
        isLoadingConfig = true
        configError = nil

        let config = currentConfig

        // 验证配置有效性
        guard config.isValid && config.isEffective && !config.isExpired else {
            Log.warning("⚠️ 启动页配置无效，使用默认配置")
            applyConfig(SplashConfig.default)
            return
        }

        applyConfig(config)

        Log.info("🚀 启动页配置设置完成: \(config.contentType) - \(config.duration)秒")
    }

    /// 开始倒计时
    ///
    /// 启动倒计时逻辑，包括跳过按钮的显示控制。
    func startCountdown() {
        // 取消之前的任务
        countdownTask?.cancel()
        skipButtonTask?.cancel()

        hasManuallySkipped = false

        Log.info("⏰ 开始启动页倒计时: \(duration)秒")

        // 启动倒计时任务
        countdownTask = Task {
            while duration > 0 && !hasManuallySkipped {
                try? await Task.sleep(for: .seconds(1))

                if !Task.isCancelled && !hasManuallySkipped {
                    duration -= 1
                }
            }

            // 倒计时结束，隐藏启动页
            if !hasManuallySkipped {
                await hideSplash()
            }
        }

        // 启动跳过按钮显示任务
        if let skipDelay = currentConfig.skipButtonDelay, currentConfig.allowSkip {
            skipButtonTask = Task {
                try? await Task.sleep(for: .seconds(skipDelay))

                if !Task.isCancelled && !hasManuallySkipped {
                    showSkipButton = true
                    Log.debug("⏭️ 显示跳过按钮")
                }
            }
        }
    }

    /// 手动跳过启动页
    func skipSplash() {
        guard canSkip else {
            Log.warning("⚠️ 当前不允许跳过启动页")
            return
        }

        hasManuallySkipped = true
        Log.info("⏭️ 用户手动跳过启动页")

        Task {
            await hideSplash()
        }
    }

    /// 处理启动页点击事件
    func handleSplashTap() {
        guard let clickUrl = currentConfig.clickUrl,
              !clickUrl.isEmpty,
              let url = URL(string: clickUrl) else {
            Log.debug("👆 启动页点击，但无跳转URL")
            return
        }

        Log.info("👆 启动页点击，跳转到: \(clickUrl)")

        // 这里可以添加跳转逻辑
        // 例如：打开浏览器、跳转到应用内页面等
        UIApplication.shared.open(url)
    }

    /// 重新加载配置
    func reloadConfig() {
        setupSplashConfig()
    }

    // MARK: - Private Methods

    /// 设置观察者
    private func setupObservers() {
        // 监听启动配置变化通知
        NotificationCenter.default.addObserver(
            forName: .splashConfigDidChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleConfigChange()
            }
        }

        // 监听应用进入前台通知
        NotificationCenter.default.addObserver(
            forName: UIApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleAppDidBecomeActive()
            }
        }
    }

    /// 应用配置
    private func applyConfig(_ config: SplashConfig) {
        duration = config.duration
        originalDuration = config.duration
        currentContentType = config.contentType
        showSkipButton = false
        isLoadingConfig = false

        Log.debug("⚙️ 应用启动页配置: \(config)")
    }

    /// 隐藏启动页
    private func hideSplash() async {
        await MainActor.run {
            withAnimation(.easeInOut(duration: 0.5)) {
                isShowingSplash = false
            }

            // 清理任务
            countdownTask?.cancel()
            skipButtonTask?.cancel()

            Log.success("✅ 启动页已隐藏")
        }
    }

    /// 处理配置变化
    private func handleConfigChange() {
        Log.debug("🔄 启动页配置发生变化")
        // 如果启动页仍在显示，可以选择是否重新应用配置
        if isShowingSplash && !hasManuallySkipped {
            setupSplashConfig()
        }
    }

    /// 处理应用进入前台
    private func handleAppDidBecomeActive() {
        Log.debug("📱 应用进入前台")
        // 如果启动页仍在显示，可以检查配置是否需要更新
        if isShowingSplash && !hasManuallySkipped {
            let config = currentConfig
            if config.isExpired {
                Log.warning("⚠️ 启动页配置已过期，切换到默认配置")
                applyConfig(SplashConfig.default)
            }
        }
    }
}

// MARK: - 通知说明
// splashConfigDidChange 通知已在 LaunchDataService.swift 中定义
