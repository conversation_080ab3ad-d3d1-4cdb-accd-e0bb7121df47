import Foundation
import SwiftUI

/// 行程计划模块常量定义
enum ItineraryPlanConstants {

    // MARK: - 核心文本常量（仅保留重复使用的）
    enum Text {
        static let noItinerariesTitle = "还没有行程计划"
        static let noItinerariesSubtitle = "开始规划你的第一次旅行吧"
        static let loadingError = "加载失败"
        static let retryAction = "重试"
    }

    // MARK: - 核心布局常量（仅保留设计系统级别的）
    enum Layout {
        static let cardCornerRadius: CGFloat = 18
    }

    // MARK: - 核心动画常量（仅保留复杂配置）
    enum Animation {
        static let cardExpansion: SwiftUI.Animation = .spring(response: 0.45, dampingFraction: 0.85)
    }

    // MARK: - 状态颜色（业务逻辑相关）
    enum StatusColors {
        static let inProgress = Color.blue
        static let completed = Color.gray
        static let upcoming = Color.green
    }



    // MARK: - 业务配置
    enum Config {
        static let cacheRefreshInterval: TimeInterval = 300 // 5分钟

        // 注意：搜索防抖延迟已迁移到全局配置：
        // - 使用 DesignSystemConstants.Interaction.debounceDelay
    }
}