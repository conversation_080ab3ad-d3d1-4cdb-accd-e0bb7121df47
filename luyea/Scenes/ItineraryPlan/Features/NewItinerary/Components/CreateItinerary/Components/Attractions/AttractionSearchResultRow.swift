import SwiftUI

/// 景点搜索结果行组件
///
/// 功能特性：
/// - 显示景点基本信息
/// - 支持选择状态显示
/// - 点击选择交互
/// - 优雅的视觉设计
struct AttractionSearchResultRow: View {
    
    // MARK: - Properties
    
    let attraction: AttractionModel
    let isSelected: Bool
    let onTap: () -> Void
    
    // MARK: - Body
    
    var body: some View {
        Button(action: {
            // 使用异步操作，确保点击响应不被阻塞
            Task { @MainActor in
                onTap()
            }
        }) {
            HStack(spacing: 12) {
                // 景点图片
                attractionImage
                
                // 景点信息
                VStack(alignment: .leading, spacing: 4) {
                    // 景点名称
                    Text(attraction.name)
                        .font(.subheadline.weight(.semibold))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    // 地址信息
                    Text(attraction.fullAddress)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    // 景点类型和评分
                    HStack(spacing: 8) {
                        Text(attraction.type)
                            .font(.caption)
                            .foregroundColor(.blue)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.1))
                            .clipShape(Capsule())
                        
                        if let rating = attraction.formattedRating {
                            HStack(spacing: 2) {
                                Image(systemName: "star.fill")
                                    .font(.caption2)
                                    .foregroundColor(.orange)
                                Text(rating)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                    }
                }
                
                Spacer()
                
                // 选择状态指示器
                selectionIndicator
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.blue.opacity(0.1) : Color.clear)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isSelected ? Color.blue.opacity(0.3) : Color.clear, lineWidth: 1)
        )
    }
    
    // MARK: - Private Views
    
    /// 景点图片
    private var attractionImage: some View {
        CachedAsyncImage(
            url: URL(string: attraction.imageUrl ?? "")
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Color.gray.opacity(0.2)
                .overlay(
                    ProgressView()
                        .scaleEffect(0.7)
                        .tint(.gray)
                )
        } errorView: {
            Color.gray.opacity(0.2)
                .overlay(
                    Image(systemName: "photo")
                        .foregroundColor(.gray)
                        .font(.caption)
                )
        }
        .frame(width: 50, height: 50)
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    /// 选择状态指示器
    private var selectionIndicator: some View {
        Group {
            if isSelected {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.blue)
                    .font(.title3)
            } else {
                Image(systemName: "plus.circle")
                    .foregroundColor(.gray)
                    .font(.title3)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 8) {
        AttractionSearchResultRow(
            attraction: AttractionModel.samples[0],
            isSelected: false,
            onTap: {}
        )
        
        AttractionSearchResultRow(
            attraction: AttractionModel.samples[1],
            isSelected: true,
            onTap: {}
        )
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
