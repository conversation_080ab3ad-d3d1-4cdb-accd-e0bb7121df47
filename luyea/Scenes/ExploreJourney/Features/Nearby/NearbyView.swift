import SwiftUI

/// 周边功能主视图
///
/// 负责管理周边页面的所有UI组件和交互逻辑。
/// 与发现功能采用对等的架构设计，保持一致性。
struct NearbyView: View {

    // MARK: - ViewModels

    /// 旅行偏好视图模型
    @ObservedObject var preferenceViewModel: TravelPreferenceViewModel

    /// 探索旅程主视图模型（用于状态协调，与发现功能对等）
    @ObservedObject var exploreViewModel: ExploreJourneyViewModel

    // MARK: - Bindings

    /// 选中的周边内容项（用于导航，待实现）
    @Binding var selectedNearbyItem: String? // 暂时使用String，后续会替换为NearbyItem

    // MARK: - State

    /// 内部状态管理（预留，与发现功能对等）
    @State private var internalState: NearbyInternalState = .idle

    // MARK: - Initialization

    init(
        preferenceViewModel: TravelPreferenceViewModel,
        exploreViewModel: ExploreJourneyViewModel,
        selectedNearbyItem: Binding<String?>
    ) {
        self.preferenceViewModel = preferenceViewModel
        self.exploreViewModel = exploreViewModel
        self._selectedNearbyItem = selectedNearbyItem
    }

    // MARK: - Body

    var body: some View {
        // 主要内容区域（与发现功能采用相同的结构模式）
        mainContent
            .coordinateSpace(name: "NearbyView")
    }

    // MARK: - Content Components

    /// 主要内容区域
    private var mainContent: some View {
        TravelPreferenceView()
    }
}

// MARK: - Supporting Types

/// 周边功能内部状态（与发现功能对等的状态管理）
private enum NearbyInternalState {
    case idle
    case loading
    case loaded
    case error
}

// MARK: - Preview

#Preview {
    NearbyView(
        preferenceViewModel: TravelPreferenceViewModel(),
        exploreViewModel: ExploreJourneyViewModel(),
        selectedNearbyItem: .constant(nil)
    )
}
