import Foundation

/// 启动页面内容类型
///
/// 定义启动页面可以显示的不同内容类型，支持原生、图片和广告三种模式。
enum SplashContentType: String, Codable, CaseIterable {
    /// 原生启动页（使用应用内置的启动图）
    case native = "native"
    /// 图片启动页（显示远程图片）
    case image = "image"
    /// 广告启动页（显示广告内容）
    case ad = "ad"

    /// 内容类型显示名称
    var displayName: String {
        switch self {
        case .native:
            return "原生启动页"
        case .image:
            return "图片启动页"
        case .ad:
            return "广告启动页"
        }
    }

    /// 是否需要网络加载
    var requiresNetworking: Bool {
        switch self {
        case .native:
            return false
        case .image, .ad:
            return true
        }
    }
}

/// 启动页面配置数据模型
///
/// 包含启动页面的所有配置信息，支持多种内容类型和自定义参数。
/// 用于控制应用启动时的展示内容和行为。
struct SplashConfig: Codable, Equatable {
    // MARK: - Properties

    /// 启动页面持续时间（秒）
    let duration: Int

    /// 启动页面内容类型
    let contentType: SplashContentType

    /// 启动页面图片URL（当 contentType 为 image 时使用）
    let imageUrl: String?

    /// 启动页面广告ID（当 contentType 为 ad 时使用）
    let adId: String?

    /// 跳过按钮显示延迟时间（秒）
    let skipButtonDelay: Int?

    /// 是否允许跳过
    let allowSkip: Bool

    /// 点击跳转URL（可选）
    let clickUrl: String?

    /// 配置版本号（用于缓存管理）
    let version: String?

    /// 配置生效时间
    let effectiveTime: Date?

    /// 配置过期时间
    let expirationTime: Date?

    /// 额外参数（用于扩展）
    let extraParams: [String: String]?

    // MARK: - Computed Properties

    /// 是否为有效配置
    var isValid: Bool {
        // 检查持续时间是否合理
        guard duration > 0 && duration <= 10 else { return false }

        // 检查内容类型相关的必需参数
        switch contentType {
        case .native:
            return true
        case .image:
            return imageUrl != nil && !imageUrl!.isEmpty
        case .ad:
            return adId != nil && !adId!.isEmpty
        }
    }

    /// 是否已过期
    var isExpired: Bool {
        guard let expirationTime = expirationTime else { return false }
        return Date() > expirationTime
    }

    /// 是否已生效
    var isEffective: Bool {
        guard let effectiveTime = effectiveTime else { return true }
        return Date() >= effectiveTime
    }

    /// 实际显示时长（考虑跳过按钮）
    var actualDuration: Int {
        if allowSkip, let skipDelay = skipButtonDelay {
            return min(duration, skipDelay)
        }
        return duration
    }

    // MARK: - Initialization

    /// 完整初始化方法
    init(
        duration: Int,
        contentType: SplashContentType,
        imageUrl: String? = nil,
        adId: String? = nil,
        skipButtonDelay: Int? = nil,
        allowSkip: Bool = true,
        clickUrl: String? = nil,
        version: String? = nil,
        effectiveTime: Date? = nil,
        expirationTime: Date? = nil,
        extraParams: [String: String]? = nil
    ) {
        self.duration = max(1, min(10, duration)) // 限制在1-10秒之间
        self.contentType = contentType
        self.imageUrl = imageUrl
        self.adId = adId
        self.skipButtonDelay = skipButtonDelay
        self.allowSkip = allowSkip
        self.clickUrl = clickUrl
        self.version = version
        self.effectiveTime = effectiveTime
        self.expirationTime = expirationTime
        self.extraParams = extraParams
    }

    // MARK: - Static Factory Methods

    /// 默认配置
    static let `default` = SplashConfig(
        duration: 1,
        contentType: .native,
        allowSkip: false,
        version: "1.0.0"
    )

    /// 创建图片启动页配置
    ///
    /// - Parameters:
    ///   - imageUrl: 图片URL
    ///   - duration: 持续时间
    ///   - allowSkip: 是否允许跳过
    /// - Returns: 图片启动页配置
    static func imageConfig(
        imageUrl: String,
        duration: Int = 3,
        allowSkip: Bool = true
    ) -> SplashConfig {
        SplashConfig(
            duration: duration,
            contentType: .image,
            imageUrl: imageUrl,
            skipButtonDelay: 2,
            allowSkip: allowSkip
        )
    }

    /// 创建广告启动页配置
    ///
    /// - Parameters:
    ///   - adId: 广告ID
    ///   - duration: 持续时间
    ///   - clickUrl: 点击跳转URL
    /// - Returns: 广告启动页配置
    static func adConfig(
        adId: String,
        duration: Int = 5,
        clickUrl: String? = nil
    ) -> SplashConfig {
        SplashConfig(
            duration: duration,
            contentType: .ad,
            adId: adId,
            skipButtonDelay: 3,
            allowSkip: true,
            clickUrl: clickUrl
        )
    }
}

// MARK: - 编码解码扩展

extension SplashConfig {
    /// 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case duration
        case contentType = "content_type"
        case imageUrl = "image_url"
        case adId = "ad_id"
        case skipButtonDelay = "skip_button_delay"
        case allowSkip = "allow_skip"
        case clickUrl = "click_url"
        case version
        case effectiveTime = "effective_time"
        case expirationTime = "expiration_time"
        case extraParams = "extra_params"
    }

    /// 自定义解码初始化方法
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        duration = try container.decode(Int.self, forKey: .duration)
        contentType = try container.decode(SplashContentType.self, forKey: .contentType)
        imageUrl = try container.decodeIfPresent(String.self, forKey: .imageUrl)
        adId = try container.decodeIfPresent(String.self, forKey: .adId)
        skipButtonDelay = try container.decodeIfPresent(Int.self, forKey: .skipButtonDelay)
        allowSkip = try container.decodeIfPresent(Bool.self, forKey: .allowSkip) ?? true
        clickUrl = try container.decodeIfPresent(String.self, forKey: .clickUrl)
        version = try container.decodeIfPresent(String.self, forKey: .version)

        // 处理日期解码
        if let effectiveTimeString = try container.decodeIfPresent(String.self, forKey: .effectiveTime) {
            effectiveTime = ISO8601DateFormatter().date(from: effectiveTimeString)
        } else {
            effectiveTime = nil
        }

        if let expirationTimeString = try container.decodeIfPresent(String.self, forKey: .expirationTime) {
            expirationTime = ISO8601DateFormatter().date(from: expirationTimeString)
        } else {
            expirationTime = nil
        }

        extraParams = try container.decodeIfPresent([String: String].self, forKey: .extraParams)
    }

    /// 自定义编码方法
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(duration, forKey: .duration)
        try container.encode(contentType, forKey: .contentType)
        try container.encodeIfPresent(imageUrl, forKey: .imageUrl)
        try container.encodeIfPresent(adId, forKey: .adId)
        try container.encodeIfPresent(skipButtonDelay, forKey: .skipButtonDelay)
        try container.encode(allowSkip, forKey: .allowSkip)
        try container.encodeIfPresent(clickUrl, forKey: .clickUrl)
        try container.encodeIfPresent(version, forKey: .version)

        // 处理日期编码
        if let effectiveTime = effectiveTime {
            try container.encode(ISO8601DateFormatter().string(from: effectiveTime), forKey: .effectiveTime)
        }

        if let expirationTime = expirationTime {
            try container.encode(ISO8601DateFormatter().string(from: expirationTime), forKey: .expirationTime)
        }

        try container.encodeIfPresent(extraParams, forKey: .extraParams)
    }
}
