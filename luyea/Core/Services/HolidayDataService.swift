import Foundation

/// 节假日数据服务
///
/// 负责管理和提供节假日数据，包括中国传统节日、法定节假日、西方节日和二十四节气。
/// 采用懒加载和缓存机制，提高性能和用户体验。
final class HolidayDataService {
    /// 单例实例
    static let shared = HolidayDataService()
    
    /// 节假日缓存
    private var holidayCache: [Int: [Holiday]] = [:]
    
    /// 缓存队列
    private let cacheQueue = DispatchQueue(label: "com.luyea.holiday.cache", qos: .utility)
    
    /// 私有初始化方法，确保单例模式
    private init() {}
    
    // MARK: - Public Methods
    
    /// 获取指定年份的所有节假日
    ///
    /// - Parameter year: 年份
    /// - Returns: 节假日数组，按日期排序
    func getHolidays(for year: Int) -> [Holiday] {
        return cacheQueue.sync {
            if let cachedHolidays = holidayCache[year] {
                return cachedHolidays
            }
            
            let holidays = generateHolidays(for: year)
            holidayCache[year] = holidays
            return holidays
        }
    }
    
    /// 获取最近的多个节假日
    ///
    /// - Parameters:
    ///   - count: 需要获取的节假日数量
    ///   - from: 起始日期，默认为当前日期
    /// - Returns: 未来的节假日数组
    func getNextHolidays(count: Int, from date: Date = Date()) -> [Holiday] {
        let calendar = Calendar.current
        let currentYear = calendar.component(.year, from: date)
        
        var allHolidays: [Holiday] = []

        allHolidays.append(contentsOf: getHolidays(for: currentYear))
        allHolidays.append(contentsOf: getHolidays(for: currentYear + 1))

        let futureHolidays = allHolidays.filter { $0.date > date }
        
        return Array(futureHolidays.prefix(count))
    }
    
    /// 获取最近的周末日期
    ///
    /// - Parameter from: 起始日期，默认为当前日期
    /// - Returns: 最近的周六日期
    func getNextWeekend(from date: Date = Date()) -> Date? {
        let calendar = Calendar.current
        
        // 获取今天是星期几（1=周日，7=周六）
        let weekday = calendar.component(.weekday, from: date)
        
        // 计算到下个周六的天数
        let daysToSaturday = (7 - weekday + 7) % 7
        let adjustedDays = daysToSaturday == 0 ? 7 : daysToSaturday
        
        return calendar.date(byAdding: .day, value: adjustedDays, to: date)
    }
    
    /// 清理缓存
    func clearCache() {
        cacheQueue.async {
            self.holidayCache.removeAll()
        }
    }
    
    // MARK: - Private Methods
    
    /// 生成指定年份的所有节假日
    private func generateHolidays(for year: Int) -> [Holiday] {
        var holidays: [Holiday] = []
        
        // 添加固定日期节假日
        holidays.append(contentsOf: getFixedHolidays(for: year))
        
        // 添加农历节假日
        holidays.append(contentsOf: getLunarHolidays(for: year))
        
        // 添加西方节日
        holidays.append(contentsOf: getWesternHolidays(for: year))
        
        // 添加二十四节气
        holidays.append(contentsOf: getSolarTerms(for: year))
        
        // 按日期排序
        return holidays.sorted { $0.date < $1.date }
    }
    
    /// 获取固定日期的节假日
    private func getFixedHolidays(for year: Int) -> [Holiday] {
        let calendar = Calendar.current
        var holidays: [Holiday] = []
        
        let fixedHolidays: [(String, Int, Int, HolidayType)] = [
            ("元旦", 1, 1, .legal),
            ("情人节", 2, 14, .western),
            ("妇女节", 3, 8, .commemorative),
            ("植树节", 3, 12, .commemorative),
            ("愚人节", 4, 1, .western),
            ("劳动节", 5, 1, .legal),
            ("青年节", 5, 4, .commemorative),
            ("儿童节", 6, 1, .commemorative),
            ("建党节", 7, 1, .commemorative),
            ("建军节", 8, 1, .commemorative),
            ("教师节", 9, 10, .commemorative),
            ("国庆节", 10, 1, .legal),
            ("万圣节", 10, 31, .western),
            ("光棍节", 11, 11, .modern),
            ("圣诞节", 12, 25, .western)
        ]
        
        for (name, month, day, type) in fixedHolidays {
            if let date = calendar.date(from: DateComponents(year: year, month: month, day: day)) {
                holidays.append(Holiday(name: name, date: date, type: type))
            }
        }
        
        return holidays
    }
    
    /// 获取农历节假日
    private func getLunarHolidays(for year: Int) -> [Holiday] {
        var holidays: [Holiday] = []
        
        if let springFestival = getSpringFestival(for: year) {
            holidays.append(Holiday(name: "春节", date: springFestival, type: .traditional))
            
            // 元宵节（春节后14天）
            if let lanternFestival = Calendar.current.date(byAdding: .day, value: 14, to: springFestival) {
                holidays.append(Holiday(name: "元宵节", date: lanternFestival, type: .traditional))
            }
        }
        
        if let dragonBoat = getDragonBoatFestival(for: year) {
            holidays.append(Holiday(name: "端午节", date: dragonBoat, type: .traditional))
        }
        
        if let qixi = getQixiFestival(for: year) {
            holidays.append(Holiday(name: "七夕节", date: qixi, type: .traditional))
        }
        
        if let midAutumn = getMidAutumnFestival(for: year) {
            holidays.append(Holiday(name: "中秋节", date: midAutumn, type: .traditional))
        }
        
        if let doubleNinth = getDoubleNinthFestival(for: year) {
            holidays.append(Holiday(name: "重阳节", date: doubleNinth, type: .traditional))
        }
        
        return holidays
    }
    
    /// 获取西方节日
    private func getWesternHolidays(for year: Int) -> [Holiday] {
        var holidays: [Holiday] = []
        
        if let mothersDay = getMothersDay(for: year) {
            holidays.append(Holiday(name: "母亲节", date: mothersDay, type: .western))
        }
        
        if let fathersDay = getFathersDay(for: year) {
            holidays.append(Holiday(name: "父亲节", date: fathersDay, type: .western))
        }
        
        if let thanksgiving = getThanksgiving(for: year) {
            holidays.append(Holiday(name: "感恩节", date: thanksgiving, type: .western))
        }
        
        return holidays
    }
    
    /// 获取二十四节气
    private func getSolarTerms(for year: Int) -> [Holiday] {
        var holidays: [Holiday] = []
        
        if let springEquinox = getSpringEquinox(for: year) {
            holidays.append(Holiday(name: "春分", date: springEquinox, type: .solarTerm))
        }
        
        if let summerSolstice = getSummerSolstice(for: year) {
            holidays.append(Holiday(name: "夏至", date: summerSolstice, type: .solarTerm))
        }
        
        if let autumnEquinox = getAutumnEquinox(for: year) {
            holidays.append(Holiday(name: "秋分", date: autumnEquinox, type: .solarTerm))
        }
        
        if let winterSolstice = getWinterSolstice(for: year) {
            holidays.append(Holiday(name: "冬至", date: winterSolstice, type: .solarTerm))
        }
        
        if let qingming = getQingmingFestival(for: year) {
            holidays.append(Holiday(name: "清明节", date: qingming, type: .solarTerm))
        }
        
        return holidays
    }
}

// MARK: - Holiday Model

/// 节假日模型
struct Holiday: Identifiable, Equatable {
    let id = UUID()
    let name: String
    let date: Date
    let type: HolidayType
    
    /// 格式化显示的日期字符串
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M月d日"
        return formatter.string(from: date)
    }
}

/// 节假日类型
enum HolidayType: String, CaseIterable {
    case legal = "法定节假日"        // 法定节假日
    case traditional = "传统节日"    // 中国传统节日
    case western = "西方节日"       // 西方节日
    case commemorative = "纪念日"   // 纪念性节日
    case modern = "现代节日"        // 现代节日
    case solarTerm = "节气"         // 二十四节气
    
    /// 节假日类型的显示颜色
    var color: String {
        switch self {
        case .legal: return "red"
        case .traditional: return "orange"
        case .western: return "blue"
        case .commemorative: return "green"
        case .modern: return "purple"
        case .solarTerm: return "brown"
        }
    }
}

// MARK: - Private Holiday Calculation Methods

extension HolidayDataService {
    /// 获取春节日期（农历正月初一）
    private func getSpringFestival(for year: Int) -> Date? {
        let calendar = Calendar.current
        let springFestivalDates = [
            2024: (2, 10),   // 甲辰龙年
            2025: (1, 29),   // 乙巳蛇年
            2026: (2, 17),   // 丙午马年
            2027: (2, 6),    // 丁未羊年
            2028: (1, 26),   // 戊申猴年
            2029: (2, 13),   // 己酉鸡年
            2030: (2, 3),    // 庚戌狗年
            2031: (1, 23),   // 辛亥猪年
            2032: (2, 11),   // 壬子鼠年
            2033: (1, 31),   // 癸丑牛年
            2034: (2, 19),   // 甲寅虎年
            2035: (2, 8)     // 乙卯兔年
        ]

        if let (month, day) = springFestivalDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取端午节日期（农历五月初五）
    private func getDragonBoatFestival(for year: Int) -> Date? {
        let calendar = Calendar.current
        let dragonBoatDates = [
            2024: (6, 10),
            2025: (5, 31),
            2026: (6, 19),
            2027: (6, 9),
            2028: (5, 28),
            2029: (6, 16),
            2030: (6, 5),
            2031: (6, 24),
            2032: (6, 11),
            2033: (6, 1),
            2034: (6, 20),
            2035: (6, 9)
        ]

        if let (month, day) = dragonBoatDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取七夕节日期（农历七月初七）
    private func getQixiFestival(for year: Int) -> Date? {
        let calendar = Calendar.current
        let qixiDates = [
            2024: (8, 10),
            2025: (8, 29),
            2026: (8, 19),
            2027: (8, 8),
            2028: (8, 26),
            2029: (8, 16),
            2030: (8, 5),
            2031: (8, 24),
            2032: (8, 12),
            2033: (8, 2),
            2034: (8, 21),
            2035: (8, 11)
        ]

        if let (month, day) = qixiDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取中秋节日期（农历八月十五）
    private func getMidAutumnFestival(for year: Int) -> Date? {
        let calendar = Calendar.current
        let midAutumnDates = [
            2024: (9, 17),
            2025: (10, 6),
            2026: (9, 25),
            2027: (9, 15),
            2028: (10, 3),
            2029: (9, 22),
            2030: (9, 12),
            2031: (10, 1),
            2032: (9, 19),
            2033: (9, 8),
            2034: (9, 28),
            2035: (9, 17)
        ]

        if let (month, day) = midAutumnDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取重阳节日期（农历九月初九）
    private func getDoubleNinthFestival(for year: Int) -> Date? {
        let calendar = Calendar.current
        let doubleNinthDates = [
            2024: (10, 11),
            2025: (10, 29),
            2026: (10, 19),
            2027: (10, 9),
            2028: (10, 26),
            2029: (10, 16),
            2030: (10, 6),
            2031: (10, 25),
            2032: (10, 13),
            2033: (10, 2),
            2034: (10, 22),
            2035: (10, 11)
        ]

        if let (month, day) = doubleNinthDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取母亲节（5月第二个周日）
    private func getMothersDay(for year: Int) -> Date? {
        let calendar = Calendar.current

        guard let may1 = calendar.date(from: DateComponents(year: year, month: 5, day: 1)) else {
            return nil
        }

        let weekday = calendar.component(.weekday, from: may1)
        let daysToFirstSunday = (8 - weekday) % 7

        guard let firstSunday = calendar.date(byAdding: .day, value: daysToFirstSunday, to: may1) else {
            return nil
        }

        return calendar.date(byAdding: .day, value: 7, to: firstSunday)
    }

    /// 获取父亲节（6月第三个周日）
    private func getFathersDay(for year: Int) -> Date? {
        let calendar = Calendar.current

        guard let june1 = calendar.date(from: DateComponents(year: year, month: 6, day: 1)) else {
            return nil
        }

        let weekday = calendar.component(.weekday, from: june1)
        let daysToFirstSunday = (8 - weekday) % 7

        guard let firstSunday = calendar.date(byAdding: .day, value: daysToFirstSunday, to: june1) else {
            return nil
        }

        return calendar.date(byAdding: .day, value: 14, to: firstSunday)
    }

    /// 获取感恩节（11月第四个周四）
    private func getThanksgiving(for year: Int) -> Date? {
        let calendar = Calendar.current

        guard let november1 = calendar.date(from: DateComponents(year: year, month: 11, day: 1)) else {
            return nil
        }

        let weekday = calendar.component(.weekday, from: november1)
        let daysToFirstThursday = (5 - weekday + 7) % 7

        guard let firstThursday = calendar.date(byAdding: .day, value: daysToFirstThursday, to: november1) else {
            return nil
        }

        return calendar.date(byAdding: .day, value: 21, to: firstThursday)
    }

    /// 获取清明节日期（二十四节气之一）
    private func getQingmingFestival(for year: Int) -> Date? {
        let calendar = Calendar.current
        let qingmingDates = [
            2024: (4, 4),
            2025: (4, 4),
            2026: (4, 5),
            2027: (4, 5),
            2028: (4, 4),
            2029: (4, 4),
            2030: (4, 5),
            2031: (4, 5),
            2032: (4, 4),
            2033: (4, 4),
            2034: (4, 5),
            2035: (4, 5)
        ]

        if let (month, day) = qingmingDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取春分日期
    private func getSpringEquinox(for year: Int) -> Date? {
        let calendar = Calendar.current
        let springEquinoxDates = [
            2024: (3, 20),
            2025: (3, 20),
            2026: (3, 20),
            2027: (3, 20),
            2028: (3, 20),
            2029: (3, 20),
            2030: (3, 20),
            2031: (3, 20),
            2032: (3, 20),
            2033: (3, 20),
            2034: (3, 20),
            2035: (3, 20)
        ]

        if let (month, day) = springEquinoxDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取夏至日期
    private func getSummerSolstice(for year: Int) -> Date? {
        let calendar = Calendar.current
        let summerSolsticeDates = [
            2024: (6, 21),
            2025: (6, 21),
            2026: (6, 21),
            2027: (6, 21),
            2028: (6, 20),
            2029: (6, 21),
            2030: (6, 21),
            2031: (6, 21),
            2032: (6, 20),
            2033: (6, 21),
            2034: (6, 21),
            2035: (6, 21)
        ]

        if let (month, day) = summerSolsticeDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取秋分日期
    private func getAutumnEquinox(for year: Int) -> Date? {
        let calendar = Calendar.current
        let autumnEquinoxDates = [
            2024: (9, 22),
            2025: (9, 23),
            2026: (9, 23),
            2027: (9, 23),
            2028: (9, 22),
            2029: (9, 23),
            2030: (9, 23),
            2031: (9, 23),
            2032: (9, 22),
            2033: (9, 23),
            2034: (9, 23),
            2035: (9, 23)
        ]

        if let (month, day) = autumnEquinoxDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }

    /// 获取冬至日期
    private func getWinterSolstice(for year: Int) -> Date? {
        let calendar = Calendar.current
        let winterSolsticeDates = [
            2024: (12, 21),
            2025: (12, 21),
            2026: (12, 21),
            2027: (12, 22),
            2028: (12, 21),
            2029: (12, 21),
            2030: (12, 21),
            2031: (12, 22),
            2032: (12, 21),
            2033: (12, 21),
            2034: (12, 21),
            2035: (12, 22)
        ]

        if let (month, day) = winterSolsticeDates[year] {
            return calendar.date(from: DateComponents(year: year, month: month, day: day))
        }
        return nil
    }
}
