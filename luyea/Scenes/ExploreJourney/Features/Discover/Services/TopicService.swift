import Foundation
import Combine

/// 话题服务协议
protocol TopicServiceProtocol {
    /// 获取话题列表
    func fetchTopics(forceRefresh: Bool) async throws -> [Topic]

    /// 搜索话题
    func searchTopics(query: String, in topics: [Topic]) -> [Topic]

    /// 获取缓存的话题数据
    func getCachedTopics() -> [Topic]

    /// 验证话题缓存是否有效
    var isTopicsCacheValid: Bool { get }
}

/// 话题服务实现
final class TopicService: TopicServiceProtocol {
    
    // MARK: - Dependencies
    
    private let networkService: NetworkServiceProtocol
    
    // MARK: - Cache Properties
    
    /// 话题缓存
    private var cachedTopics: [Topic] = []
    
    /// 最后更新时间
    private var lastTopicsUpdateTime: Date?
    
    /// 缓存有效期（10分钟）
    private let cacheValidDuration: TimeInterval = 600
    
    // MARK: - Computed Properties
    
    /// 话题缓存是否有效
    var isTopicsCacheValid: Bool {
        guard let lastUpdate = lastTopicsUpdateTime else { return false }
        return Date().timeIntervalSince(lastUpdate) < cacheValidDuration
    }
    
    // MARK: - Initialization
    
    init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }
    
    // MARK: - Public Methods
    
    /// 获取话题列表
    func fetchTopics(forceRefresh: Bool = false) async throws -> [Topic] {
        // 如果不强制刷新且缓存有效，返回缓存数据
        if !forceRefresh && isTopicsCacheValid && !cachedTopics.isEmpty {
            Log.debug("🏷️ [TopicService] 使用缓存的话题数据: \(cachedTopics.count) 个")
            return cachedTopics
        }
        
        Log.info("🏷️ [TopicService] 开始加载话题数据")
        
        do {
            let topics: [Topic] = try await networkService.request(
                .get(APIPaths.topicList)
            )
            
            // 更新缓存
            cachedTopics = topics
            lastTopicsUpdateTime = Date()
            
            Log.success("✅ [TopicService] 话题数据加载成功: \(topics.count) 个")
            return topics
            
        } catch {
            // 如果网络请求失败且有缓存数据，返回缓存数据
            if !cachedTopics.isEmpty {
                Log.warning("⚠️ [TopicService] 网络请求失败，使用缓存数据: \(error.localizedDescription)")
                return cachedTopics
            }
            
            // 处理具体的网络错误
            let topicError = mapNetworkError(error)
            Log.error("❌ [TopicService] 话题数据加载失败: \(topicError.localizedDescription)")
            throw topicError
        }
    }
    
    /// 搜索话题
    func searchTopics(query: String, in topics: [Topic]) -> [Topic] {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 如果搜索词为空，返回所有话题
        guard !trimmedQuery.isEmpty else {
            return topics
        }
        
        let lowercasedQuery = trimmedQuery.lowercased()
        
        // 按名称搜索（Topic模型没有description属性）
        let filteredTopics = topics.filter { topic in
            topic.name.lowercased().contains(lowercasedQuery)
        }
        
        Log.debug("🔍 [TopicService] 话题搜索结果: \(filteredTopics.count)/\(topics.count) 个匹配 '\(trimmedQuery)'")
        return filteredTopics
    }
    
    /// 刷新话题缓存
    func refreshTopics() async throws -> [Topic] {
        return try await fetchTopics(forceRefresh: true)
    }
    
    /// 清除话题缓存
    func clearCache() {
        cachedTopics.removeAll()
        lastTopicsUpdateTime = nil
        Log.debug("🗑️ [TopicService] 话题缓存已清除")
    }
    
    /// 获取缓存的话题数据（不发起网络请求）
    func getCachedTopics() -> [Topic] {
        return cachedTopics
    }
    
    // MARK: - Private Methods
    
    /// 映射网络错误到话题错误
    private func mapNetworkError(_ error: Error) -> DiscoverError {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .noInternetConnection:
                return .topicsNetworkUnavailable
            case .timeout:
                return .topicsTimeout
            case .serverError:
                return .topicsServerError
            default:
                return .topicsLoadFailed(error)
            }
        } else {
            return .topicsLoadFailed(error)
        }
    }
}

// MARK: - Topic Service Extensions
// 扩展功能已移除，保持Service专注于核心功能

// MARK: - Singleton Support (Optional)

extension TopicService {
    /// 单例实例（可选，根据项目需要）
    static let shared = TopicService()
}
