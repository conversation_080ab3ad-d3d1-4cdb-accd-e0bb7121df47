import SwiftUI

/// 用户个人主页视图 - Profile功能模块的核心视图
///
/// 专用于展示用户个人主页信息，支持作者模式和个人模式两种展示方式。
/// 遵循四层架构规范，作为Profile功能的主要视图组件。
///
/// 功能特性:
/// - 双模式支持：作者模式和个人模式
/// - 响应式布局：适配不同屏幕尺寸
/// - 标签筛选：支持按内容标签筛选作品
/// - 无状态子组件：所有子组件都是无状态的
/// - 动画交互：流畅的展开收起动画
///
/// 使用模式:
/// 1. **作者模式**：显示其他用户的主页，用于浏览他人作品
/// 2. **个人模式**：显示当前用户的主页，支持编辑和管理
///
/// 架构职责:
/// - 🎯 状态管理：管理页面级别的状态和数据
/// - 🧩 组件协调：协调各子组件的交互和数据传递
/// - 📱 用户交互：处理用户操作和页面导航
/// - 🎨 布局管理：控制整体布局和视觉效果
///
/// - Parameters:
///   - username: 用户显示名称
///   - avatarUrl: 用户头像URL
///   - isCurrentUser: 是否为当前用户（个人模式）
///   - contentTags: 内容标签列表，用于筛选展示
///
/// - Note: 子组件全部无状态化，便于复用和测试
/// - Warning: 大量内容加载时注意性能优化，使用懒加载
public struct UserProfileView: View {
    // MARK: - 属性
    @Environment(\.dismiss) private var dismiss
    @State private var isHeaderVisible = true
    @State private var selectedTag: String?
    @State private var isTagsExpanded = false
    @State private var isEditingProfile = false
    @State private var posts: [ProfileModels.UserDiscoverItem] = []
    
    let username: String
    let avatarUrl: String
    let isCurrentUser: Bool
    let contentTags: [String]
    

    
    // MARK: - 初始化方法
    public init(
        username: String,
        avatarUrl: String,
        isCurrentUser: Bool,
        contentTags: [String]
    ) {
        self.username = username
        self.avatarUrl = avatarUrl
        self.isCurrentUser = isCurrentUser
        self.contentTags = contentTags
    }
    
    // MARK: - 视图
    public var body: some View {
        TabBarAwareScrollView {
            LazyVStack(spacing: 0, pinnedViews: [.sectionHeaders]) {
                // 头部区域
                Section {
                    if isHeaderVisible {
                        VStack(spacing: 0) {
                            // 头像和基本信息
                            UserProfileHeaderView(
                                username: username,
                                avatarUrl: avatarUrl,
                                isCurrentUser: isCurrentUser,
                                onEditProfile: { isEditingProfile = true }
                            )
                            .padding(.bottom, 12)
                            
                            // 统计数据
                            UserProfileStatsView()
                                .padding(.bottom, 16)
                        }
                        .background(
                            GeometryReader { geometry in
                                Color.clear.preference(
                                    key: ScrollOffsetPreferenceKey.self,
                                    value: geometry.frame(in: .named("scrollView")).minY
                                )
                            }
                        )
                    }
                }
                
                // 内容区域
                Section {
                    if posts.isEmpty {
                        UserProfileEmptyView(selectedTag: selectedTag)
                            .padding(.top, 24)
                    } else {
                        LazyVStack(spacing: 16) {
                            ForEach(posts) { post in
                                UserDiscoverCardView(item: post)
                            }
                        }
                        .padding(.horizontal, DesignSystemConstants.Spacing.standard)
                        .padding(.vertical, 16)
                    }
                } header: {
                    // 标签栏
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text("\(username)的发现")
                                .font(.headline)
                                .foregroundColor(.primary)
                            if !posts.isEmpty {
                                Text("\(posts.count)")
                                    .font(.headline)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                        }
                        .padding(.horizontal, DesignSystemConstants.Spacing.standard)
                        .padding(.top, 12)
                        .padding(.bottom, 6)
                        
                        // 标签选择器
                        if !contentTags.isEmpty {
                            UserProfileTagsView(
                                contentTags: contentTags,
                                selectedTag: $selectedTag,
                                isTagsExpanded: $isTagsExpanded
                            )
                            .padding(.bottom, 4)
                        }
                    }
                    .background(Color(.systemBackground))
                }
            }
        }
        .coordinateSpace(name: "scrollView")
        .appBackground()
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar { toolbarContent }
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
            withAnimation(.easeInOut(duration: 0.25)) {
                isHeaderVisible = value > -100
            }
        }
        .onChange(of: selectedTag) { oldValue, newValue in
            loadPosts(for: newValue)
        }
        .onAppear {
            loadPosts(for: nil)
        }
    }
    
    // MARK: - 工具栏
    @ToolbarContentBuilder
    private var toolbarContent: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            Button(action: { dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 17, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(width: 32, height: 32)
            }
        }
        
        ToolbarItem(placement: .principal) {
            if !isHeaderVisible {
                Text("\(username)的发现")
                    .font(.headline)
                    .foregroundColor(.primary)
                    .transition(.opacity)
            }
        }
        
        ToolbarItem(placement: .navigationBarTrailing) {
            if !isHeaderVisible {
                CachedAsyncImage(
                    url: URL(string: avatarUrl)
                ) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .overlay(
                            Image(systemName: "person.circle.fill")
                                .font(.title3)
                                .foregroundColor(.gray.opacity(0.6))
                        )
                } errorView: {
                    Circle()
                        .fill(Color.red.opacity(0.1))
                        .overlay(
                            Image(systemName: "person.circle.fill")
                                .font(.title3)
                                .foregroundColor(.red.opacity(0.6))
                        )
                }
                .frame(width: 32, height: 32)
                .clipShape(Circle())
                .transition(.opacity)
            }
        }
    }
    
    // MARK: - 辅助方法
    private func loadPosts(for tag: String?) {
        // 模拟网络请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 测试数据
            let testPosts: [ProfileModels.UserDiscoverItem] = [
                // 美食探店
                ProfileModels.UserDiscoverItem(
                    id: "1",
                    title: "上海必打卡的米其林餐厅",
                    description: "探索上海最受欢迎的米其林餐厅，从传统本帮菜到创新料理，带你品味不一样的美食体验。",
                    coverImage: "https://picsum.photos/800/600?random=1",
                    tag: "美食探店",
                    likes: 512,
                    comments: 78,
                    createdAt: Date().addingTimeInterval(-3600 * 2), // 2小时前
                    location: "上海市静安区",
                    isLiked: true,
                    isCollected: false
                ),
                // 城市漫步
                ProfileModels.UserDiscoverItem(
                    id: "2",
                    title: "漫步外滩，感受百年上海",
                    description: "沿着外滩漫步，欣赏万国建筑博览，感受上海的历史与现代交融。",
                    coverImage: "https://picsum.photos/800/600?random=2",
                    tag: "城市漫步",
                    likes: 423,
                    comments: 56,
                    createdAt: Date().addingTimeInterval(-3600 * 24), // 1天前
                    location: "上海市黄浦区",
                    isLiked: false,
                    isCollected: true
                ),
                // 历史文化
                ProfileModels.UserDiscoverItem(
                    id: "3",
                    title: "探访上海博物馆，了解城市历史",
                    description: "走进上海博物馆，探索这座城市的文明发展历程，感受历史文化的魅力。",
                    coverImage: "https://picsum.photos/800/600?random=3",
                    tag: "历史文化",
                    likes: 356,
                    comments: 45,
                    createdAt: Date().addingTimeInterval(-3600 * 48), // 2天前
                    location: "上海市黄浦区",
                    isLiked: false,
                    isCollected: false
                ),
                // 自然风光
                ProfileModels.UserDiscoverItem(
                    id: "4",
                    title: "辰山植物园赏花记",
                    description: "春季赏花好去处，辰山植物园的樱花、郁金香竞相绽放，美不胜收。",
                    coverImage: "https://picsum.photos/800/600?random=4",
                    tag: "自然风光",
                    likes: 289,
                    comments: 34,
                    createdAt: Date().addingTimeInterval(-3600 * 72), // 3天前
                    location: "上海市松江区",
                    isLiked: true,
                    isCollected: true
                ),
                // 艺术展览
                ProfileModels.UserDiscoverItem(
                    id: "5",
                    title: "当代艺术博物馆特展",
                    description: "探索当代艺术的无限可能，感受艺术带来的视觉冲击与思考。",
                    coverImage: "https://picsum.photos/800/600?random=5",
                    tag: "艺术展览",
                    likes: 198,
                    comments: 23,
                    createdAt: Date().addingTimeInterval(-3600 * 96), // 4天前
                    location: "上海市黄浦区",
                    isLiked: false,
                    isCollected: false
                ),
                // 特色民宿
                ProfileModels.UserDiscoverItem(
                    id: "6",
                    title: "隐藏在弄堂里的精品民宿",
                    description: "体验老上海风情，入住特色民宿，感受不一样的住宿体验。",
                    coverImage: "https://picsum.photos/800/600?random=6",
                    tag: "特色民宿",
                    likes: 245,
                    comments: 31,
                    createdAt: Date().addingTimeInterval(-3600 * 120), // 5天前
                    location: "上海市徐汇区",
                    isLiked: true,
                    isCollected: false
                ),
                // 主题乐园
                ProfileModels.UserDiscoverItem(
                    id: "7",
                    title: "迪士尼乐园游玩攻略",
                    description: "最全的迪士尼游玩攻略，带你玩转上海迪士尼乐园。",
                    coverImage: "https://picsum.photos/800/600?random=7",
                    tag: "主题乐园",
                    likes: 367,
                    comments: 42,
                    createdAt: Date().addingTimeInterval(-3600 * 144), // 6天前
                    location: "上海市浦东新区",
                    isLiked: false,
                    isCollected: true
                ),
                // 购物指南
                ProfileModels.UserDiscoverItem(
                    id: "8",
                    title: "上海购物天堂指南",
                    description: "从奢侈品到特色小店，带你探索上海最值得逛的购物地。",
                    coverImage: "https://picsum.photos/800/600?random=8",
                    tag: "购物指南",
                    likes: 156,
                    comments: 28,
                    createdAt: Date().addingTimeInterval(-3600 * 168), // 7天前
                    location: "上海市静安区",
                    isLiked: false,
                    isCollected: false
                )
            ]
            
            // 根据标签筛选内容
            if let tag = tag {
                self.posts = testPosts.filter { $0.tag == tag }
            } else {
                self.posts = testPosts
            }
        }
    }
}

// MARK: - Internal Components

/// 滚动偏移量偏好键
private struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

#Preview("作者主页") {
    UserProfileView(
        username: "探索者",
        avatarUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop",
        isCurrentUser: false,
        contentTags: [
            "旅途规划", "网红打卡", "美食探店",
            "历史文化", "自然风光", "城市漫步",
            "特色民宿", "艺术展览", "主题乐园",
            "购物指南", "交通攻略", "季节限定"
        ]
    )
    .navigationBarHidden(true)

}

#Preview("个人主页") {
    NavigationStack {
        UserProfileView(
            username: "我的主页",
            avatarUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop",
            isCurrentUser: true,
            contentTags: [
                "旅途规划", "网红打卡", "美食探店",
                "历史文化", "自然风光", "城市漫步",
                "特色民宿", "艺术展览", "主题乐园",
                "购物指南", "交通攻略", "季节限定",
                "摄影圣地", "亲子游玩", "夜生活"
            ]
        )

    }
}

#Preview("新用户") {
    NavigationStack {
        UserProfileView(
            username: "新用户",
            avatarUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop",
            isCurrentUser: true,
            contentTags: []
        )

    }
}

#Preview("作者主页-暗色模式") {
    UserProfileView(
        username: "探索者",
        avatarUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop",
        isCurrentUser: false,
        contentTags: ["城市探索", "户外运动", "美食探店"]
    )
    .navigationBarHidden(true)

    .preferredColorScheme(.dark)
} 