import AVFoundation
import Combine
import Foundation
import MediaPlayer

/// 音频播放服务
///
/// 负责音频播放控制和与系统音频控制的同步。
/// 提供完整的音频播放功能，包括播放控制、状态监听和系统集成。
///
/// 核心功能：
/// - 音频播放控制（播放、暂停、停止）
/// - 系统音频控制集成
/// - 播放状态监听和同步
/// - 后台播放支持
/// - 音频会话管理
/// - 错误处理和恢复
///
/// 设计原则：
/// - 系统集成：与iOS音频系统深度集成
/// - 状态同步：确保UI和播放状态一致
/// - 错误处理：完善的错误处理和恢复机制
/// - 性能优化：高效的资源管理
@MainActor
final class AudioPlayerService: NSObject, ObservableObject {

  // MARK: - Published Properties

  /// 当前播放状态
  @Published private(set) var isPlaying: Bool = false

  /// 当前音频信息
  @Published private(set) var currentAudioInfo: AudioInfo?

  /// 播放进度（0.0 - 1.0）
  @Published private(set) var playbackProgress: Double = 0.0

  /// 当前播放时间
  @Published private(set) var currentTime: TimeInterval = 0.0

  /// 音频总时长
  @Published private(set) var duration: TimeInterval = 0.0

  /// 播放错误信息
  @Published private(set) var playbackError: AudioPlayerError?

  /// 是否正在加载
  @Published private(set) var isLoading: Bool = false

  // MARK: - Private Properties

  /// AVPlayer实例（主要播放器）
  private var avPlayer: AVPlayer?

  /// 播放器状态观察者
  private var playerItemObserver: NSKeyValueObservation?
  private var playerStatusObserver: NSKeyValueObservation?
  private var timeObserver: Any?

  /// 播放进度计时器
  private var progressTimer: Timer?

  /// Combine订阅集合
  private var cancellables = Set<AnyCancellable>()

  /// 音频会话
  private let audioSession = AVAudioSession.sharedInstance()

  /// 是否已配置音频会话
  private var isAudioSessionConfigured = false

  /// 播放状态变化主题
  private let playbackStateSubject = PassthroughSubject<PlaybackStateChange, Never>()

  // MARK: - Singleton

  /// 全局唯一实例
  static let shared = AudioPlayerService()

  // MARK: - Initialization

  /// 私有初始化方法
  private override init() {
    super.init()
    setupAudioSession()
    setupRemoteCommandCenter()
    setupNotificationObservers()
  }

  deinit {
    // 在 deinit 中直接清理，避免 actor 问题
    playerStatusObserver?.invalidate()
    playerItemObserver?.invalidate()
    if let timeObserver = timeObserver {
      avPlayer?.removeTimeObserver(timeObserver)
    }
    NotificationCenter.default.removeObserver(self)
  }

  // MARK: - Public Methods - 播放控制

  /// 播放音频文件（简化测试方法）
  func playAudioFile(audioURL: URL) {
    Task {
      let audioInfo = AudioInfo(
        id: UUID().uuidString,
        title: "测试音频",
        audioURL: audioURL
      )

      do {
        try await play(audioInfo: audioInfo)
      } catch {
        Log.error("❌ 播放失败: \(error)")
      }
    }
  }

  /// 播放音频
  /// - Parameter audioInfo: 音频信息
  func play(audioInfo: AudioInfo) async throws {
    // 设置加载状态
    isLoading = true
    playbackError = nil

    do {
      // 如果是新音频，需要重新加载
      if currentAudioInfo?.id != audioInfo.id {
        try await loadAudio(audioInfo)
      }

      // 设置当前音频信息
      currentAudioInfo = audioInfo

      // 开始播放
      try startPlayback()

      // 更新状态
      isPlaying = true
      isLoading = false

      // 启动进度计时器
      startProgressTimer()

      // 更新系统媒体信息
      updateNowPlayingInfo()

      // 发送状态变化通知
      notifyStateChange(.playbackStarted(audioInfo))

    } catch {
      isLoading = false
      let audioError = AudioPlayerError.audioPlaybackFailed(underlying: error)
      playbackError = audioError

      Log.error("❌ 音频播放失败: \(error)")
      notifyStateChange(.playbackFailed(audioError))

      throw audioError
    }
  }

  /// 暂停播放
  func pause() {
    guard isPlaying else { return }

    avPlayer?.pause()
    isPlaying = false

    stopProgressTimer()
    updateNowPlayingInfo()

    notifyStateChange(.playbackPaused)
  }

  /// 恢复播放
  func resume() throws {
    guard !isPlaying, avPlayer != nil else { return }

    try startPlayback()
    isPlaying = true

    startProgressTimer()
    updateNowPlayingInfo()

    notifyStateChange(.playbackResumed)
  }

  /// 停止播放
  func stop() {
    // 清理观察者
    cleanupPlayerObservers()

    avPlayer?.pause()
    avPlayer = nil

    isPlaying = false
    currentTime = 0.0
    playbackProgress = 0.0

    stopProgressTimer()
    clearNowPlayingInfo()

    notifyStateChange(.playbackStopped)
  }

  /// 切换播放/暂停状态
  func togglePlayPause() throws {
    if isPlaying {
      pause()
    } else {
      try resume()
    }
  }

  /// 跳转到指定时间
  /// - Parameter time: 目标时间
  func seek(to time: TimeInterval) {
    guard let avPlayer = avPlayer else { return }

    let constrainedTime = max(0, min(time, duration))
    let cmTime = CMTime(seconds: constrainedTime, preferredTimescale: 1000)

    avPlayer.seek(to: cmTime) { [weak self] completed in
      if completed {
        Task { @MainActor in
          self?.currentTime = constrainedTime
          self?.playbackProgress =
            self?.duration ?? 0 > 0 ? constrainedTime / (self?.duration ?? 1) : 0
          self?.updateNowPlayingInfo()
        }
      }
    }

  }

  // MARK: - Public Methods - 状态查询

  /// 获取播放状态变化的发布者
  /// - Returns: 播放状态变化的发布者
  func playbackStatePublisher() -> AnyPublisher<PlaybackStateChange, Never> {
    playbackStateSubject.eraseToAnyPublisher()
  }

  /// 获取当前播放状态
  var currentPlaybackState: PlaybackState {
    if isLoading {
      return .loading
    } else if isPlaying {
      return .playing
    } else if avPlayer != nil {
      return .paused
    } else {
      return .stopped
    }
  }

  /// 是否有可播放的音频
  var hasPlayableAudio: Bool {
    return avPlayer != nil
  }

  // MARK: - Private Methods - 音频加载

  /// 加载音频
  /// - Parameter audioInfo: 音频信息
  private func loadAudio(_ audioInfo: AudioInfo) async throws {
    // 停止当前播放
    stop()

    // 使用 AVPlayer 加载音频
    if let audioURL = audioInfo.audioURL {
      try await loadAudioFromURL(audioURL)
    } else {
      // 如果没有URL，创建模拟播放器
      try createMockPlayer(for: audioInfo)
    }

    // 更新时长信息
    duration = 180.0  // 默认时长，实际应从音频文件获取
    currentTime = 0.0
    playbackProgress = 0.0
  }

  /// 从URL加载音频
  /// - Parameter url: 音频URL
  private func loadAudioFromURL(_ url: URL) async throws {
    do {
      // 设置强化的音频会话（模拟器兼容性）
      try setupEnhancedAudioSession()

      // 创建 AVPlayerItem
      let playerItem = AVPlayerItem(url: url)

      // 创建 AVPlayer
      avPlayer = AVPlayer(playerItem: playerItem)
      avPlayer?.volume = 1.0

      // 设置播放器观察者
      setupPlayerObservers()

      // 等待播放器准备就绪
      try await waitForPlayerReady()

    } catch {
      Log.error("❌ AVPlayer 加载音频失败: \(error)")
      throw AudioPlayerError.audioPlaybackFailed(underlying: error)
    }
  }

  /// 创建模拟播放器（用于演示）
  /// - Parameter audioInfo: 音频信息
  private func createMockPlayer(for audioInfo: AudioInfo) throws {
    // 设置音频信息
    currentAudioInfo = audioInfo
    duration = 180.0  // 默认时长，实际应从音频文件获取
    currentTime = 0.0
    playbackProgress = 0.0

    // 创建一个空的 AVPlayer 用于模拟
    avPlayer = AVPlayer()
  }

  /// 设置强化的音频会话（模拟器兼容性优化）
  private func setupEnhancedAudioSession() throws {
    #if targetEnvironment(simulator)
      // 模拟器环境：尝试多种配置
      let configurations:
        [(AVAudioSession.Category, AVAudioSession.Mode, AVAudioSession.CategoryOptions)] = [
          // 配置 1: playAndRecord + defaultToSpeaker
          (.playAndRecord, .default, .defaultToSpeaker),

          // 配置 2: playback + mixWithOthers
          (.playback, .default, .mixWithOthers),

          // 配置 3: ambient
          (.ambient, .default, []),
        ]

      for (category, mode, options) in configurations {
        do {
          try audioSession.setCategory(category, mode: mode, options: options)
          try audioSession.setActive(true)
          return
        } catch {
          continue
        }
      }

      throw AudioPlayerError.audioPlaybackFailed(
        underlying: NSError(
          domain: "AudioPlayerService", code: -1,
          userInfo: [NSLocalizedDescriptionKey: "所有音频会话配置都失败"]))
    #else
      // 真机环境：使用标准配置
      try audioSession.setCategory(
        .playback, mode: .default, options: [.allowBluetooth, .allowBluetoothA2DP])
      try audioSession.setActive(true)
    #endif
  }

  /// 设置播放器观察者
  private func setupPlayerObservers() {
    guard let avPlayer = avPlayer else { return }

    // 清理之前的观察者
    cleanupPlayerObservers()

    // 观察播放器状态
    playerStatusObserver = avPlayer.observe(\.status, options: [.new]) { [weak self] player, _ in
      Task { @MainActor in
        self?.handlePlayerStatusChange(player.status)
      }
    }

    // 观察播放项状态
    if let playerItem = avPlayer.currentItem {
      playerItemObserver = playerItem.observe(\.status, options: [.new]) { [weak self] item, _ in
        Task { @MainActor in
          self?.handlePlayerItemStatusChange(item.status)
        }
      }
    }

    // 添加时间观察者
    let interval = CMTime(seconds: 0.1, preferredTimescale: 1000)
    timeObserver = avPlayer.addPeriodicTimeObserver(forInterval: interval, queue: .main) {
      [weak self] time in
      Task { @MainActor in
        self?.handleTimeUpdate(time)
      }
    }
  }

  /// 清理播放器观察者
  private func cleanupPlayerObservers() {
    playerStatusObserver?.invalidate()
    playerStatusObserver = nil

    playerItemObserver?.invalidate()
    playerItemObserver = nil

    if let timeObserver = timeObserver {
      avPlayer?.removeTimeObserver(timeObserver)
      self.timeObserver = nil
    }
  }

  /// 等待播放器准备就绪
  private func waitForPlayerReady() async throws {
    guard let avPlayer = avPlayer else {
      throw AudioPlayerError.invalidAudioInfo(reason: "没有加载音频文件")
    }

    // 如果播放器已经准备就绪，直接返回
    if avPlayer.status == .readyToPlay {
      return
    }

    // 等待播放器状态变为 readyToPlay
    return try await withCheckedThrowingContinuation { continuation in
      var isResumed = false
      var observer: NSKeyValueObservation?

      observer = avPlayer.observe(\.status, options: [.new]) { player, _ in
        guard !isResumed else { return }

        switch player.status {
        case .readyToPlay:
          isResumed = true
          observer?.invalidate()
          continuation.resume()
        case .failed:
          isResumed = true
          observer?.invalidate()
          if let error = player.error {
            continuation.resume(throwing: AudioPlayerError.audioPlaybackFailed(underlying: error))
          } else {
            continuation.resume(
              throwing: AudioPlayerError.audioPlaybackFailed(
                underlying: NSError(
                  domain: "AudioPlayerService", code: -1,
                  userInfo: [NSLocalizedDescriptionKey: "播放器加载失败"])))
          }
        case .unknown:
          break
        @unknown default:
          break
        }
      }

      // 设置超时
      DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
        guard !isResumed else { return }
        isResumed = true
        observer?.invalidate()
        continuation.resume(
          throwing: AudioPlayerError.audioPlaybackFailed(
            underlying: NSError(
              domain: "AudioPlayerService", code: -1,
              userInfo: [NSLocalizedDescriptionKey: "播放器加载超时"])))
      }
    }
  }

  /// 处理播放器状态变化
  private func handlePlayerStatusChange(_ status: AVPlayer.Status) {
    switch status {
    case .readyToPlay:
      break
    case .failed:
      Log.error("❌ AVPlayer 状态失败")
      if let error = avPlayer?.error {
        playbackError = AudioPlayerError.audioPlaybackFailed(underlying: error)
      }
    case .unknown:
      break
    @unknown default:
      Log.warning("⚠️ AVPlayer 未知状态")
    }
  }

  /// 处理播放项状态变化
  private func handlePlayerItemStatusChange(_ status: AVPlayerItem.Status) {
    switch status {
    case .readyToPlay:
      if let playerItem = avPlayer?.currentItem {
        let itemDuration = CMTimeGetSeconds(playerItem.duration)
        if itemDuration.isFinite && itemDuration > 0 {
          duration = itemDuration
        }
      }
    case .failed:
      Log.error("❌ AVPlayerItem 状态失败")
      if let error = avPlayer?.currentItem?.error {
        playbackError = AudioPlayerError.audioPlaybackFailed(underlying: error)
      }
    case .unknown:
      break
    @unknown default:
      Log.warning("⚠️ AVPlayerItem 未知状态")
    }
  }

  /// 处理时间更新
  private func handleTimeUpdate(_ time: CMTime) {
    let timeSeconds = CMTimeGetSeconds(time)
    if timeSeconds.isFinite {
      currentTime = timeSeconds
      playbackProgress = duration > 0 ? currentTime / duration : 0

      // 更新音频信息的当前时间
      if let audioInfo = currentAudioInfo {
        // 更新当前时间（简化版本，不再在模型中存储）
        currentAudioInfo = audioInfo
      }
    }
  }

  /// 创建WAV文件头
  /// - Parameters:
  ///   - dataSize: 音频数据大小
  ///   - sampleRate: 采样率
  /// - Returns: WAV文件头数据
  private func createWAVHeader(dataSize: Int, sampleRate: Int) -> Data {
    var header = Data()

    // RIFF头
    header.append("RIFF".data(using: .ascii)!)
    header.append(withUnsafeBytes(of: UInt32(36 + dataSize).littleEndian) { Data($0) })
    header.append("WAVE".data(using: .ascii)!)

    // fmt子块
    header.append("fmt ".data(using: .ascii)!)
    header.append(withUnsafeBytes(of: UInt32(16).littleEndian) { Data($0) })
    header.append(withUnsafeBytes(of: UInt16(1).littleEndian) { Data($0) })  // PCM
    header.append(withUnsafeBytes(of: UInt16(1).littleEndian) { Data($0) })  // 单声道
    header.append(withUnsafeBytes(of: UInt32(sampleRate).littleEndian) { Data($0) })
    header.append(withUnsafeBytes(of: UInt32(sampleRate * 2).littleEndian) { Data($0) })
    header.append(withUnsafeBytes(of: UInt16(2).littleEndian) { Data($0) })  // 块对齐
    header.append(withUnsafeBytes(of: UInt16(16).littleEndian) { Data($0) })  // 位深度

    // data子块
    header.append("data".data(using: .ascii)!)
    header.append(withUnsafeBytes(of: UInt32(dataSize).littleEndian) { Data($0) })

    return header
  }

  // MARK: - Private Methods - 播放控制

  /// 开始播放
  private func startPlayback() throws {
    guard currentAudioInfo != nil else {
      throw AudioPlayerError.invalidAudioInfo(reason: "没有加载音频文件")
    }

    if let avPlayer = avPlayer {
      // 激活音频会话
      try activateAudioSession()

      // 开始播放
      avPlayer.play()
    } else {
      // 模拟播放模式
      isPlaying = true
      startProgressTimer()
    }
  }

  /// 启动进度计时器
  private func startProgressTimer() {
    stopProgressTimer()

    progressTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
      Task { @MainActor in
        self?.updateProgress()
      }
    }
  }

  /// 停止进度计时器
  private func stopProgressTimer() {
    progressTimer?.invalidate()
    progressTimer = nil
  }

  /// 更新播放进度
  private func updateProgress() {
    guard let avPlayer = avPlayer else { return }

    let time = avPlayer.currentTime()
    let timeSeconds = CMTimeGetSeconds(time)

    if timeSeconds.isFinite {
      currentTime = timeSeconds
      playbackProgress = duration > 0 ? currentTime / duration : 0

      // 更新音频信息的当前时间
      if var audioInfo = currentAudioInfo {
        // 更新当前时间（简化版本，不再在模型中存储）
        currentAudioInfo = audioInfo
      }
    }
  }

  // MARK: - Private Methods - 系统集成

  /// 设置音频会话（基础配置）
  private func setupAudioSession() {
    guard !isAudioSessionConfigured else { return }

    do {
      // 基础音频会话配置
      #if targetEnvironment(simulator)
        // 模拟器环境：使用简化配置
        try audioSession.setCategory(.playback, mode: .default, options: [])
      #else
        // 真机环境：使用完整配置
        try audioSession.setCategory(
          .playback, mode: .default, options: [.allowBluetooth, .allowBluetoothA2DP])
      #endif

      isAudioSessionConfigured = true
    } catch {
      Log.error("❌ 基础音频会话配置失败: \(error)")
    }
  }

  /// 激活音频会话
  private func activateAudioSession() throws {
    do {
      try audioSession.setActive(true)
    } catch {
      Log.error("❌ 音频会话激活失败: \(error)")
      throw AudioPlayerError.audioPlaybackFailed(underlying: error)
    }
  }

  /// 设置远程控制中心
  private func setupRemoteCommandCenter() {
    let commandCenter = MPRemoteCommandCenter.shared()

    // 播放命令
    commandCenter.playCommand.addTarget { [weak self] _ in
      Task { @MainActor in
        do {
          try self?.resume()
          return MPRemoteCommandHandlerStatus.success
        } catch {
          return .commandFailed
        }
      }
      return .success
    }

    // 暂停命令
    commandCenter.pauseCommand.addTarget { [weak self] _ in
      Task { @MainActor in
        self?.pause()
      }
      return .success
    }

    // 切换播放/暂停命令
    commandCenter.togglePlayPauseCommand.addTarget { [weak self] _ in
      Task { @MainActor in
        do {
          try self?.togglePlayPause()
          return MPRemoteCommandHandlerStatus.success
        } catch {
          return .commandFailed
        }
      }
      return .success
    }

  }

  /// 设置通知观察者
  private func setupNotificationObservers() {
    // 音频会话中断通知
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleAudioSessionInterruption),
      name: AVAudioSession.interruptionNotification,
      object: audioSession
    )

    // 音频路由变化通知
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleAudioSessionRouteChange),
      name: AVAudioSession.routeChangeNotification,
      object: audioSession
    )

  }

  /// 更新正在播放信息
  private func updateNowPlayingInfo() {
    guard let audioInfo = currentAudioInfo else {
      clearNowPlayingInfo()
      return
    }

    var nowPlayingInfo: [String: Any] = [
      MPMediaItemPropertyTitle: audioInfo.title,
      MPMediaItemPropertyPlaybackDuration: duration,
      MPNowPlayingInfoPropertyElapsedPlaybackTime: currentTime,
      MPNowPlayingInfoPropertyPlaybackRate: isPlaying ? 1.0 : 0.0,
    ]

    // 简化版本：只设置标题，不设置艺术家和专辑信息

    MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
  }

  /// 清除正在播放信息
  private func clearNowPlayingInfo() {
    MPNowPlayingInfoCenter.default().nowPlayingInfo = nil
  }

  /// 发送状态变化通知
  /// - Parameter change: 状态变化事件
  private func notifyStateChange(_ change: PlaybackStateChange) {
    playbackStateSubject.send(change)
  }

  /// 清理资源
  private func cleanup() {
    stop()
    cleanupPlayerObservers()
    NotificationCenter.default.removeObserver(self)

    // 停用音频会话
    do {
      try audioSession.setActive(false)
    } catch {
      Log.error("❌ 音频会话停用失败: \(error)")
    }
  }

  // MARK: - Notification Handlers

  /// 处理音频会话中断
  @objc private func handleAudioSessionInterruption(_ notification: Notification) {
    guard let userInfo = notification.userInfo,
      let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
      let type = AVAudioSession.InterruptionType(rawValue: typeValue)
    else {
      return
    }

    switch type {
    case .began:
      if isPlaying {
        pause()
      }

    case .ended:
      if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
        let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
        if options.contains(.shouldResume) {
          do {
            try resume()
          } catch {
            Log.error("❌ 中断后恢复播放失败: \(error)")
          }
        }
      }

    @unknown default:
      Log.warning("⚠️ 未知的音频会话中断类型")
    }
  }

  /// 处理音频路由变化
  @objc private func handleAudioSessionRouteChange(_ notification: Notification) {
    guard let userInfo = notification.userInfo,
      let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
      let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue)
    else {
      return
    }

    switch reason {
    case .oldDeviceUnavailable:
      if isPlaying {
        pause()
      }

    case .newDeviceAvailable:
      break

    default:
      break
    }
  }
}

// MARK: - AVPlayer 播放完成处理

extension AudioPlayerService {

  /// 设置播放完成通知
  private func setupPlaybackEndNotification() {
    guard let avPlayer = avPlayer, let playerItem = avPlayer.currentItem else { return }

    NotificationCenter.default.addObserver(
      forName: .AVPlayerItemDidPlayToEndTime,
      object: playerItem,
      queue: .main
    ) { [weak self] _ in
      Task { @MainActor in
        self?.handlePlaybackCompletion()
      }
    }
  }

  /// 处理播放完成
  private func handlePlaybackCompletion() {
    isPlaying = false
    currentTime = 0.0
    playbackProgress = 0.0

    stopProgressTimer()
    clearNowPlayingInfo()

    notifyStateChange(.playbackCompleted)
  }
}

// MARK: - 播放状态枚举

/// 播放状态
enum PlaybackState {
  case stopped
  case loading
  case playing
  case paused

  var description: String {
    switch self {
    case .stopped: return "已停止"
    case .loading: return "加载中"
    case .playing: return "播放中"
    case .paused: return "已暂停"
    }
  }
}

// MARK: - 播放状态变化事件

/// 播放状态变化事件
enum PlaybackStateChange {
  case playbackStarted(AudioInfo)
  case playbackPaused
  case playbackResumed
  case playbackStopped
  case playbackCompleted
  case playbackFailed(AudioPlayerError)
  case progressUpdated(TimeInterval, TimeInterval)

  var description: String {
    switch self {
    case .playbackStarted(let audioInfo): return "开始播放: \(audioInfo.title)"
    case .playbackPaused: return "播放暂停"
    case .playbackResumed: return "播放恢复"
    case .playbackStopped: return "播放停止"
    case .playbackCompleted: return "播放完成"
    case .playbackFailed(let error): return "播放失败: \(error.localizedDescription)"
    case .progressUpdated(let current, let total): return "进度更新: \(current)/\(total)"
    }
  }
}
