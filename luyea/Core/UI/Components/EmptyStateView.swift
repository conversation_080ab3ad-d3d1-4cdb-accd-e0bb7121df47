import SwiftUI

/// 通用空状态视图，无状态组件
struct EmptyStateView: View {
    // MARK: - Body
    var body: some View {
        VStack {
            Spacer()
                .frame(height: 100)
            
            VStack(spacing: 24) {
                // 顶部装饰图案
                Image(systemName: "map.fill")
                    .font(.system(size: 60))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue.opacity(0.8), .blue.opacity(0.6)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .padding(.bottom, 8)
                
                // 主标题
                Text("暂无内容")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.primary)
                
                // 副标题
                Text("下拉刷新，发现更多精彩")
                    .font(.system(size: 15))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            Color(.systemGroupedBackground)
                .overlay(
                    // 背景装饰
                    ZStack {
                        Circle()
                            .fill(Color.blue.opacity(0.03))
                            .frame(width: 200, height: 200)
                            .offset(x: -100, y: -100)
                        Circle()
                            .fill(Color.blue.opacity(0.03))
                            .frame(width: 150, height: 150)
                            .offset(x: 120, y: 100)
                    }
                )
        )
    }
}

#Preview {
    EmptyStateView()
        .preferredColorScheme(.light)
} 
