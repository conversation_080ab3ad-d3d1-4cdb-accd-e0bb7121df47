import SwiftUI

/// 已选择景点卡片组件
///
/// 功能特性：
/// - 简约设计风格
/// - 流畅的交互动画
/// - 符合app整体设计风格
/// - 支持移除操作
struct AttractionCard: View {
    
    // MARK: - Properties
    
    let attraction: AttractionModel
    let onRemove: () -> Void
    
    // MARK: - State

    @State private var isPressed = false
    @State private var isRemoveButtonPressed = false
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 景点图片
            attractionImageSection
            
            // 景点信息
            attractionInfoSection
        }
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
        .shadow(color: Color.primary.opacity(0.05), radius: 8, x: 0, y: 2)
        .overlay(
            // 移除按钮
            removeButton,
            alignment: .topTrailing
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
    }
    
    // MARK: - Private Views
    
    /// 景点图片区域
    private var attractionImageSection: some View {
        CachedAsyncImage(
            url: URL(string: attraction.imageUrl ?? "")
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            placeholderImage
        } errorView: {
            errorPlaceholderImage
        }
        .frame(height: 100)
        .clipped()
    }
    
    /// 占位图片
    private var placeholderImage: some View {
        Rectangle()
            .fill(
                LinearGradient(
                    colors: [
                        Color.gray.opacity(0.1),
                        Color.gray.opacity(0.2)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                VStack(spacing: 8) {
                    Image(systemName: attractionTypeIcon)
                        .font(.title2)
                        .foregroundColor(.gray.opacity(0.6))

                    Text(attraction.type)
                        .font(.caption2.weight(.medium))
                        .foregroundColor(.gray.opacity(0.8))
                }
            )
    }

    /// 错误状态占位图片
    private var errorPlaceholderImage: some View {
        ImageErrorView(showText: true)
    }
    
    /// 景点信息区域
    private var attractionInfoSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 景点名称
            Text(attraction.name)
                .font(.subheadline.weight(.semibold))
                .foregroundColor(.primary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // 城市和评分
            HStack {
                // 城市信息
                HStack(spacing: 4) {
                    Image(systemName: "location.fill")
                        .font(.caption2)
                        .foregroundColor(.blue)
                    
                    Text(attraction.city)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                // 评分
                if let rating = attraction.formattedRating {
                    HStack(spacing: 2) {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.orange)
                        
                        Text(rating)
                            .font(.caption.weight(.medium))
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding(16)
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    /// 移除按钮
    private var removeButton: some View {
        Button(action: onRemove) {
            Image(systemName: "xmark.circle.fill")
                .font(.title3)
                .foregroundColor(.white)
                .background(
                    Circle()
                        .fill(Color.gray.opacity(0.8))
                        .frame(width: 24, height: 24)
                )
        }
        .offset(x: -8, y: 8)
        .opacity(0.9)
        .scaleEffect(isRemoveButtonPressed ? 0.85 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isRemoveButtonPressed)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isRemoveButtonPressed = pressing
        } perform: {}
    }
    
    /// 根据景点类型返回对应图标
    private var attractionTypeIcon: String {
        switch attraction.type {
        case "自然风光":
            return "mountain.2"
        case "历史文化":
            return "building.columns"
        case "地标建筑":
            return "building.2"
        case "主题乐园":
            return "gamecontroller"
        default:
            return "mappin"
        }
    }
}

// MARK: - Preview

#Preview {
    LazyVGrid(columns: [
        GridItem(.flexible(), spacing: 12),
        GridItem(.flexible(), spacing: 12)
    ], spacing: 16) {
        ForEach(AttractionModel.samples.prefix(4), id: \.id) { attraction in
            AttractionCard(
                attraction: attraction,
                onRemove: {}
            )
        }
    }
    .padding(20)
    .background(Color(.systemGroupedBackground))
}
