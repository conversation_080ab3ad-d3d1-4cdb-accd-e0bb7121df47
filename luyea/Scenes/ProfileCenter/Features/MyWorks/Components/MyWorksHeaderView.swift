import SwiftUI

/// 我的作品页面的头部视图
/// 专注于统计展示和用户引导
struct MyWorksHeaderView: View {
    let username: String

    var body: some View {
        VStack(spacing: 20) {
            // 欢迎信息和创作按钮
            HStack {
                VStack(alignment: .leading, spacing: 6) {
                    HStack(spacing: 8) {
                        Text("Hi, \(username)")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.primary)

                        // 创作者徽章
                        Image(systemName: "star.fill")
                            .font(.system(size: 12))
                            .foregroundColor(.orange)
                    }

                    Text("继续创作精彩内容，分享您的旅行故事")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 创作按钮
                Button(action: { /* 快速创作 */ }) {
                    HStack(spacing: 6) {
                        Image(systemName: "plus")
                            .font(.system(size: 14, weight: .medium))
                        Text("创作")
                            .font(.system(size: 14, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.blue, Color.purple]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(20)
                }
            }

            // 统计数据
            MyWorksStatsView()
        }
        .padding(.horizontal, DesignSystemConstants.Spacing.standard)
        .padding(.vertical, 20)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.03),
                    Color.purple.opacity(0.03)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

/// 作品统计数据视图 - 简洁统一设计
struct MyWorksStatsView: View {
    // 模拟统计数据
    private let stats = (
        totalWorks: 45,
        totalViews: 125600,
        totalLikes: 8934
    )

    var body: some View {
        HStack(spacing: 0) {
            statItem(
                title: "已发布",
                value: "\(stats.totalWorks)",
                color: .blue
            )

            Divider()
                .frame(height: 32)
                .padding(.horizontal, 20)

            statItem(
                title: "总浏览",
                value: stats.totalViews.formatCount(),
                color: .green
            )

            Divider()
                .frame(height: 32)
                .padding(.horizontal, 20)

            statItem(
                title: "获得喜欢",
                value: stats.totalLikes.formatCount(),
                color: .red
            )
        }
        .padding(.vertical, 20)
        .padding(.horizontal, 24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private func statItem(title: String, value: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Text(value)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(color)

            Text(title)
                .font(.system(size: 13))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }

}

// MARK: - 预览
#Preview("我的作品头部 - 重新设计") {
    MyWorksHeaderView(
        username: "高级知识质子"
    )
    .background(Color(.systemGroupedBackground))
}

#Preview("统计卡片") {
    MyWorksStatsView()
        .padding()
        .background(Color(.systemGroupedBackground))
}
