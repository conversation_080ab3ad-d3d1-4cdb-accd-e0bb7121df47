import SwiftUI
import Combine

/// 全局认证状态管理器
///
/// 负责管理整个应用的用户认证状态，提供统一的认证接口。
/// 类似于TabBarStateManager的设计模式，确保状态管理的一致性。
///
/// 核心功能：
/// - 管理用户登录状态和用户信息
/// - 提供登录界面的展示控制
/// - 处理认证相关的业务逻辑
/// - 支持待执行操作的队列管理
/// - 集成生物识别认证
/// - 自动token刷新机制
@MainActor
final class AuthenticationManager: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前认证状态（从ProfileCenter获取）
    @Published private(set) var authenticationState: AuthenticationModels.AuthState = .unauthenticated
    
    /// 是否显示登录界面
    @Published var isShowingLogin = false
    
    /// 是否正在处理认证操作
    @Published private(set) var isProcessing = false
    
    /// 当前错误信息
    @Published private(set) var currentError: AuthenticationModels.AuthError?
    
    // MARK: - Dependencies

    private let profileAuthService: AuthenticationService
    private let keychainService: KeychainService
    private let toastManager: ToastManager
    
    // MARK: - Private Properties
    
    /// 待执行的操作队列（使用弱引用避免内存泄漏）
    private var pendingActions: [() -> Void] = []

    /// 操作队列的最大容量，防止内存无限增长
    private let maxPendingActions = 10
    
    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()
    

    
    // MARK: - Singleton
    
    /// 全局唯一实例
    static let shared = AuthenticationManager()
    
    // MARK: - Initialization
    
    private init() {
        self.profileAuthService = AuthenticationService.shared
        self.keychainService = KeychainService.shared
        self.toastManager = ToastManager.shared

        setupNotificationObservers()
        restoreAuthenticationState()
    }

    deinit {
        // 清理资源（虽然是单例，但保持良好的编程习惯）
        cancellables.removeAll()
        pendingActions.removeAll()
    }
    
    // MARK: - Public Computed Properties
    
    /// 是否已认证
    var isAuthenticated: Bool {
        if case .authenticated = authenticationState {
            return true
        }
        return false
    }
    
    /// 当前用户信息
    var currentUser: AuthenticationModels.User? {
        if case .authenticated(let user) = authenticationState {
            return user
        }
        return nil
    }
    
    /// 是否正在处理认证相关操作
    var isAuthenticating: Bool {
        if case .authenticating = authenticationState {
            return true
        }
        return isProcessing
    }
    
    // MARK: - Public Methods
    
    /// 要求用户认证，如果未认证则显示登录界面
    /// - Parameter action: 认证成功后执行的操作
    func requireAuthentication(action: @escaping () -> Void = {}) {
        if isAuthenticated {
            action()
        } else {
            // 检查队列容量，防止内存无限增长
            if pendingActions.count < maxPendingActions {
                pendingActions.append(action)
            } else {
                Log.warning("🔐 [AuthenticationManager] Pending actions queue is full, dropping oldest action")
                pendingActions.removeFirst()
                pendingActions.append(action)
            }

            // 显示登录界面
            presentLogin()
        }
    }
    
    /// 显示登录界面
    func presentLogin() {
        isShowingLogin = true
    }

    /// 隐藏认证界面
    func dismissAuthentication() {
        isShowingLogin = false
    }
    
    /// 监听认证状态变化
    func observeAuthenticationState() {
        // 监听ProfileCenter的认证状态变化
        profileAuthService.$authState
            .sink { [weak self] authState in
                self?.handleAuthStateChange(authState)
            }
            .store(in: &cancellables)
    }
    
    /// 用户登出
    func logout() async {
        await performAuthenticationOperation {
            await self.profileAuthService.logout()

            // 清理待处理操作
            self.pendingActions.removeAll()

            // 显示成功提示
            self.toastManager.show(
                "已退出登录",
                style: .plain
            )
        }
    }
    
    /// 刷新用户信息
    func refreshUserInfo() async {
        // 委托给ProfileCenter的认证服务处理
        // 这里可以添加额外的全局状态同步逻辑
    }
    
    /// 清除当前错误
    func clearError() {
        currentError = nil
    }
}

// MARK: - Private Methods

private extension AuthenticationManager {

    /// 执行认证操作的通用方法
    func performAuthenticationOperation(_ operation: @escaping () async throws -> Void) async {
        // 防止重复操作
        guard !isProcessing else {
            Log.warning("🔐 [AuthenticationManager] Authentication operation already in progress")
            return
        }

        isProcessing = true
        currentError = nil

        do {
            try await operation()
        } catch {
            Log.error("🔐 [AuthenticationManager] Authentication operation failed: \(error)")
            currentError = error as? AuthenticationModels.AuthError
            handleAuthenticationError(error)
        }

        isProcessing = false
    }

    /// 处理认证状态变化
    func handleAuthStateChange(_ authState: AuthenticationModels.AuthState) {
        Log.debug("🔐 [AuthenticationManager] Auth state changed to: \(authState)")

        // 更新Published状态，触发UI刷新
        self.authenticationState = authState

        switch authState {
        case .authenticated(let user):
            Log.debug("🔐 [AuthenticationManager] User authenticated: \(user.displayName)")
            // 用户登录成功，执行待处理的操作
            executePendingActions()
            dismissAuthentication()

            // 发送登录成功通知
            NotificationCenter.default.post(
                name: Notification.Name("luyea.auth.user_did_login"),
                object: user
            )

        case .unauthenticated:
            Log.debug("🔐 [AuthenticationManager] User unauthenticated")
            // 用户登出，清理待处理操作
            pendingActions.removeAll()

            // 发送登出通知
            NotificationCenter.default.post(
                name: Notification.Name("luyea.auth.user_did_logout"),
                object: nil
            )

        case .authenticating:
            Log.debug("🔐 [AuthenticationManager] User authenticating")
            // 认证中状态
            break

        case .error(let error):
            Log.debug("🔐 [AuthenticationManager] Auth error: \(error)")
            // 处理认证错误
            handleProfileAuthError(error)
        }
    }

    /// 处理ProfileCenter认证错误
    func handleProfileAuthError(_ error: AuthenticationModels.AuthError) {
        let message: String
        switch error {
        case .invalidPhoneNumber:
            message = "手机号格式不正确"
        case .invalidVerificationCode:
            message = "验证码错误"
        case .codeSendFailed:
            message = "验证码发送失败"
        case .loginFailed(let description):
            message = "登录失败：\(description)"
        case .networkError:
            message = "网络连接失败，请检查网络设置"
        case .unknownError:
            message = "未知错误，请重试"
        }

        toastManager.show(message, style: .error)
    }

    /// 处理认证错误（通用）
    func handleAuthenticationError(_ error: Error) {
        toastManager.show(
            error.localizedDescription,
            style: .error
        )
    }
    
    /// 执行待处理的操作
    func executePendingActions() {
        let actions = pendingActions
        pendingActions.removeAll()

        Log.debug("🔐 [AuthenticationManager] Executing \(actions.count) pending actions")

        for (index, action) in actions.enumerated() {
            action()
            Log.debug("🔐 [AuthenticationManager] Executed pending action \(index)")
        }
    }
    
    /// 恢复认证状态
    func restoreAuthenticationState() {
        // 同步初始状态
        self.authenticationState = profileAuthService.authState

        // ProfileCenter的AuthenticationService会自动处理状态恢复
        // 这里只需要开始观察状态变化
        observeAuthenticationState()
    }
    

    
    /// 设置通知观察者
    func setupNotificationObservers() {
        // 监听应用进入前台
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.refreshUserInfo()
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - Environment Extension

/// AuthenticationManager的Environment扩展
struct AuthenticationManagerKey: EnvironmentKey {
    @MainActor
    static var defaultValue: AuthenticationManager {
        AuthenticationManager.shared
    }
}

extension EnvironmentValues {
    var authenticationManager: AuthenticationManager {
        get { self[AuthenticationManagerKey.self] }
        set { self[AuthenticationManagerKey.self] = newValue }
    }
}


