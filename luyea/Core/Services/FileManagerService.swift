import Foundation

/// 文件管理服务错误类型
enum FileManagerError: Error, LocalizedError {
    case fileNotFound(String)
    case writePermissionDenied(String)
    case readPermissionDenied(String)
    case diskSpaceInsufficient
    case invalidFileName(String)
    case directoryCreationFailed(String)

    var errorDescription: String? {
        switch self {
        case .fileNotFound(let fileName):
            return "文件未找到: \(fileName)"
        case .writePermissionDenied(let fileName):
            return "写入权限被拒绝: \(fileName)"
        case .readPermissionDenied(let fileName):
            return "读取权限被拒绝: \(fileName)"
        case .diskSpaceInsufficient:
            return "磁盘空间不足"
        case .invalidFileName(let fileName):
            return "无效的文件名: \(fileName)"
        case .directoryCreationFailed(let path):
            return "目录创建失败: \(path)"
        }
    }
}

/// 文件管理服务协议，便于依赖注入和单元测试
protocol FileManagerServiceProtocol {
    var documentsDirectory: URL { get }
    var cachesDirectory: URL { get }
    var temporaryDirectory: URL { get }

    func readData(fileName: String) throws -> Data
    func writeData(_ data: Data, fileName: String) throws
    func fileExists(fileName: String) -> Bool
    func deleteFile(fileName: String) throws
    func createDirectory(name: String) throws -> URL
    func getFileSize(fileName: String) throws -> Int64
    func getDirectorySize(directoryName: String) throws -> Int64
    func cleanupTemporaryFiles() throws
}

/// 文件管理服务实现类
///
/// 提供安全、高效的文件操作功能，包括读写、删除、目录管理等。
/// 支持多种目录类型，具备完善的错误处理和权限检查。
final class FileManagerService: FileManagerServiceProtocol {
    /// 单例实例
    static let shared = FileManagerService()

    /// 系统文件管理器
    private let fileManager = FileManager.default

    /// 文件操作队列
    private let fileQueue = DispatchQueue(label: "com.luyea.filemanager", qos: .utility)

    /// 私有初始化方法，确保单例模式
    private init() {
        Log.info("📁 文件管理服务初始化完成")
    }

    // MARK: - Directory Properties

    /// 文档目录（用户数据存储）
    var documentsDirectory: URL {
        return fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    }

    /// 缓存目录（临时数据存储）
    var cachesDirectory: URL {
        return fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
    }

    /// 临时目录（临时文件存储）
    var temporaryDirectory: URL {
        return fileManager.temporaryDirectory
    }

    // MARK: - File Operations

    /// 读取文件数据
    ///
    /// - Parameter fileName: 文件名（相对于文档目录）
    /// - Returns: 文件内容数据
    /// - Throws: FileManagerError 相关错误
    func readData(fileName: String) throws -> Data {
        guard isValidFileName(fileName) else {
            throw FileManagerError.invalidFileName(fileName)
        }

        let fileURL = documentsDirectory.appendingPathComponent(fileName)

        guard fileManager.fileExists(atPath: fileURL.path) else {
            throw FileManagerError.fileNotFound(fileName)
        }

        do {
            let data = try Data(contentsOf: fileURL)
            Log.debug("📖 读取文件成功: \(fileName) (\(data.count) bytes)")
            return data
        } catch {
            Log.error("❌ 读取文件失败: \(fileName) - \(error)")
            throw FileManagerError.readPermissionDenied(fileName)
        }
    }

    /// 写入文件数据
    ///
    /// - Parameters:
    ///   - data: 要写入的数据
    ///   - fileName: 文件名（相对于文档目录）
    /// - Throws: FileManagerError 相关错误
    func writeData(_ data: Data, fileName: String) throws {
        guard isValidFileName(fileName) else {
            throw FileManagerError.invalidFileName(fileName)
        }

        // 检查磁盘空间
        try checkDiskSpace(requiredBytes: Int64(data.count))

        let fileURL = documentsDirectory.appendingPathComponent(fileName)

        // 确保父目录存在
        let parentDirectory = fileURL.deletingLastPathComponent()
        if !fileManager.fileExists(atPath: parentDirectory.path) {
            try fileManager.createDirectory(at: parentDirectory, withIntermediateDirectories: true)
        }

        do {
            try data.write(to: fileURL, options: .atomic)
            Log.debug("💾 写入文件成功: \(fileName) (\(data.count) bytes)")
        } catch {
            Log.error("❌ 写入文件失败: \(fileName) - \(error)")
            throw FileManagerError.writePermissionDenied(fileName)
        }
    }

    /// 检查文件是否存在
    ///
    /// - Parameter fileName: 文件名（相对于文档目录）
    /// - Returns: 文件是否存在
    func fileExists(fileName: String) -> Bool {
        let fileURL = documentsDirectory.appendingPathComponent(fileName)
        return fileManager.fileExists(atPath: fileURL.path)
    }

    /// 删除文件
    ///
    /// - Parameter fileName: 文件名（相对于文档目录）
    /// - Throws: FileManagerError 相关错误
    func deleteFile(fileName: String) throws {
        guard isValidFileName(fileName) else {
            throw FileManagerError.invalidFileName(fileName)
        }

        let fileURL = documentsDirectory.appendingPathComponent(fileName)

        guard fileManager.fileExists(atPath: fileURL.path) else {
            throw FileManagerError.fileNotFound(fileName)
        }

        do {
            try fileManager.removeItem(at: fileURL)
            Log.debug("🗑️ 删除文件成功: \(fileName)")
        } catch {
            Log.error("❌ 删除文件失败: \(fileName) - \(error)")
            throw error
        }
    }

    /// 创建目录
    ///
    /// - Parameter name: 目录名（相对于文档目录）
    /// - Returns: 创建的目录URL
    /// - Throws: FileManagerError 相关错误
    func createDirectory(name: String) throws -> URL {
        guard isValidFileName(name) else {
            throw FileManagerError.invalidFileName(name)
        }

        let directoryURL = documentsDirectory.appendingPathComponent(name)

        if !fileManager.fileExists(atPath: directoryURL.path) {
            do {
                try fileManager.createDirectory(at: directoryURL, withIntermediateDirectories: true)
                Log.debug("📁 创建目录成功: \(name)")
            } catch {
                Log.error("❌ 创建目录失败: \(name) - \(error)")
                throw FileManagerError.directoryCreationFailed(name)
            }
        }

        return directoryURL
    }

    /// 获取文件大小
    ///
    /// - Parameter fileName: 文件名（相对于文档目录）
    /// - Returns: 文件大小（字节）
    /// - Throws: FileManagerError 相关错误
    func getFileSize(fileName: String) throws -> Int64 {
        let fileURL = documentsDirectory.appendingPathComponent(fileName)

        guard fileManager.fileExists(atPath: fileURL.path) else {
            throw FileManagerError.fileNotFound(fileName)
        }

        do {
            let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            Log.error("❌ 获取文件大小失败: \(fileName) - \(error)")
            throw error
        }
    }

    /// 获取目录大小
    ///
    /// - Parameter directoryName: 目录名（相对于文档目录）
    /// - Returns: 目录总大小（字节）
    /// - Throws: FileManagerError 相关错误
    func getDirectorySize(directoryName: String) throws -> Int64 {
        let directoryURL = documentsDirectory.appendingPathComponent(directoryName)

        guard fileManager.fileExists(atPath: directoryURL.path) else {
            throw FileManagerError.fileNotFound(directoryName)
        }

        var totalSize: Int64 = 0

        do {
            let contents = try fileManager.contentsOfDirectory(at: directoryURL, includingPropertiesForKeys: [.fileSizeKey])

            for fileURL in contents {
                let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
                if let fileSize = attributes[.size] as? Int64 {
                    totalSize += fileSize
                }
            }

            return totalSize
        } catch {
            Log.error("❌ 获取目录大小失败: \(directoryName) - \(error)")
            throw error
        }
    }

    /// 清理临时文件
    ///
    /// 删除临时目录中的所有文件，释放存储空间。
    ///
    /// - Throws: FileManagerError 相关错误
    func cleanupTemporaryFiles() throws {
        do {
            let tempContents = try fileManager.contentsOfDirectory(at: temporaryDirectory, includingPropertiesForKeys: nil)

            for fileURL in tempContents {
                try fileManager.removeItem(at: fileURL)
            }

            Log.info("🧹 临时文件清理完成，删除了 \(tempContents.count) 个文件")
        } catch {
            Log.error("❌ 临时文件清理失败: \(error)")
            throw error
        }
    }

    // MARK: - Private Methods

    /// 验证文件名是否有效
    ///
    /// - Parameter fileName: 要验证的文件名
    /// - Returns: 文件名是否有效
    private func isValidFileName(_ fileName: String) -> Bool {
        // 检查文件名是否为空
        guard !fileName.isEmpty else { return false }

        // 检查文件名长度
        guard fileName.count <= 255 else { return false }

        // 检查是否包含非法字符
        let invalidCharacters = CharacterSet(charactersIn: "/<>:\"|?*")
        guard fileName.rangeOfCharacter(from: invalidCharacters) == nil else { return false }

        // 检查是否为系统保留名称
        let reservedNames = [".", "..", "CON", "PRN", "AUX", "NUL"]
        guard !reservedNames.contains(fileName.uppercased()) else { return false }

        return true
    }

    /// 检查磁盘空间是否足够
    ///
    /// - Parameter requiredBytes: 需要的字节数
    /// - Throws: FileManagerError.diskSpaceInsufficient
    private func checkDiskSpace(requiredBytes: Int64) throws {
        do {
            let attributes = try fileManager.attributesOfFileSystem(forPath: documentsDirectory.path)
            if let freeSpace = attributes[.systemFreeSize] as? Int64 {
                let bufferSpace: Int64 = 10 * 1024 * 1024
                if freeSpace < (requiredBytes + bufferSpace) {
                    Log.warning("⚠️ 磁盘空间不足: 需要\(requiredBytes)字节，可用\(freeSpace)字节")
                    throw FileManagerError.diskSpaceInsufficient
                }
            }
        } catch {
            Log.warning("⚠️ 无法检查磁盘空间: \(error)")
        }
    }
}