import SwiftUI

/// MyWorks模块编辑历史查看视图
///
/// 专门用于展示MyWorks作品的编辑历史记录，遵循项目命名规范。
/// 提供时间线式的编辑记录展示，支持多种编辑操作类型的可视化。
///
/// 功能特性:
/// - 时间线式编辑历史展示
/// - 支持多种编辑操作类型
/// - 加载状态和空状态处理
/// - 响应式布局设计
///
/// 架构职责:
/// - 📝 历史展示：展示作品的完整编辑历史
/// - 🎨 可视化：提供直观的时间线界面
/// - 📱 交互体验：支持流畅的查看体验
///
/// - Parameters:
///   - workId: 作品ID，用于加载对应的编辑历史
///
/// - Note: 遵循MyWorks模块命名规范，使用模块前缀
/// - Warning: 大量历史记录时注意性能优化
struct MyWorksEditHistoryView: View {
    let workId: String
    @Environment(\.dismiss) private var dismiss
    @State private var editHistory: [EditRecord] = []
    @State private var isLoading = true
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                if isLoading {
                    loadingView
                } else if editHistory.isEmpty {
                    emptyStateView
                } else {
                    historyListView
                }
            }
            .navigationTitle("修改记录")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            loadEditHistory()
        }
    }
    
    // MARK: - 子视图
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("加载修改记录...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "clock.arrow.circlepath")
                .font(.system(size: 48))
                .foregroundColor(.secondary.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("暂无修改记录")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("这个作品还没有被修改过")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    private var historyListView: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(editHistory) { record in
                    EditRecordRow(record: record)
                        .padding(.horizontal, 16)
                    
                    if record.id != editHistory.last?.id {
                        Divider()
                            .padding(.leading, 72)
                    }
                }
            }
            .padding(.vertical, 16)
        }
    }
    
    // MARK: - 数据加载
    
    private func loadEditHistory() {
        isLoading = true
        
        // 模拟网络请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            loadMockEditHistory()
            isLoading = false
        }
    }
    
    private func loadMockEditHistory() {
        editHistory = [
            EditRecord(
                id: "1",
                action: .created,
                timestamp: Date().addingTimeInterval(-3600 * 24 * 7), // 7天前
                description: "创建作品",
                details: "初始发布"
            ),
            EditRecord(
                id: "2",
                action: .titleChanged,
                timestamp: Date().addingTimeInterval(-3600 * 24 * 5), // 5天前
                description: "修改标题",
                details: "从「春日樱花」改为「🌸春日樱花季｜无锡鼋头渚的粉色浪漫」"
            ),
            EditRecord(
                id: "3",
                action: .contentUpdated,
                timestamp: Date().addingTimeInterval(-3600 * 24 * 3), // 3天前
                description: "更新内容",
                details: "添加了最佳观赏时间和拍摄建议"
            ),
            EditRecord(
                id: "4",
                action: .imageChanged,
                timestamp: Date().addingTimeInterval(-3600 * 24 * 1), // 1天前
                description: "更换封面图片",
                details: "替换为更高清的樱花照片"
            )
        ]
    }
}

/// 编辑记录行视图
struct EditRecordRow: View {
    let record: EditRecord
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 时间线图标
            VStack {
                Circle()
                    .fill(record.action.color)
                    .frame(width: 12, height: 12)
                    .overlay(
                        Circle()
                            .stroke(Color(.systemBackground), lineWidth: 2)
                    )
                
                if record.id != "1" { // 不是最后一个
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 2)
                        .frame(maxHeight: .infinity)
                }
            }
            .frame(width: 12)
            
            // 内容区域
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(record.description)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                        
                        Text(formatTimestamp(record.timestamp))
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // 操作图标
                    Image(systemName: record.action.iconName)
                        .font(.system(size: 14))
                        .foregroundColor(record.action.color)
                        .frame(width: 24, height: 24)
                        .background(record.action.color.opacity(0.1))
                        .clipShape(Circle())
                }
                
                if !record.details.isEmpty {
                    Text(record.details)
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .padding(.top, 4)
                }
            }
            .padding(.vertical, 12)
        }
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        date.formatted("MM月dd日 HH:mm")
    }
}

/// 编辑记录模型
struct EditRecord: Identifiable {
    let id: String
    let action: EditAction
    let timestamp: Date
    let description: String
    let details: String
}

/// 编辑操作类型
enum EditAction {
    case created
    case titleChanged
    case contentUpdated
    case imageChanged
    case tagChanged
    case locationChanged
    case published
    case unpublished
    
    var iconName: String {
        switch self {
        case .created: return "plus.circle.fill"
        case .titleChanged: return "textformat"
        case .contentUpdated: return "doc.text"
        case .imageChanged: return "photo"
        case .tagChanged: return "tag"
        case .locationChanged: return "location"
        case .published: return "eye"
        case .unpublished: return "eye.slash"
        }
    }
    
    var color: Color {
        switch self {
        case .created: return .green
        case .titleChanged: return .blue
        case .contentUpdated: return .orange
        case .imageChanged: return .purple
        case .tagChanged: return .pink
        case .locationChanged: return .red
        case .published: return .green
        case .unpublished: return .gray
        }
    }
}

// MARK: - 预览

#Preview("MyWorks编辑历史") {
    MyWorksEditHistoryView(workId: "test")
}
