import SwiftUI

/// 用户主页的头部信息视图
struct UserProfileHeaderView: View {
    let username: String
    let avatarUrl: String
    let isCurrentUser: Bool
    let onEditProfile: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            VStack(spacing: 16) {
                HStack(alignment: .top, spacing: 16) {
                    // 头像
                    ProfileAvatarView(
                        avatarUrl: avatarUrl,
                        isCurrentUser: isCurrentUser,
                        onEditProfile: onEditProfile
                    )
                    
                    // 用户信息
                    VStack(alignment: .leading, spacing: 8) {
                        // 用户名
                        Text(username)
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.primary)
                        
                        // 城市信息
                        HStack(spacing: 6) {
                            Image(systemName: "location.fill")
                                .font(.system(size: 12))
                                .foregroundColor(Color.accentColor)
                            Text("上海市")
                                .font(.system(size: 13))
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // 推荐按钮
                    if !isCurrentUser {
                        RecommendButton(action: {
                            // 实现推荐功能
                        })
                        .padding(.top, 2) // 微调顶部对齐
                    }
                }
                
                // 个人简介
                if !isCurrentUser {
                    Text("热爱探索城市中的美好，分享生活中的精彩瞬间")
                        .font(.system(size: 15))
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, 4)
                }
            }
            .padding(.horizontal, DesignSystemConstants.Spacing.standard)
            .padding(.vertical, 16)
        }
        .background(Color(.systemBackground))
    }
}

// MARK: - 推荐按钮视图
private struct RecommendButton: View {
    let action: () -> Void
    @State private var isPressed = false
    @State private var isRecommended = false
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.2, dampingFraction: 0.7)) {
                isPressed = true
                isRecommended.toggle()
                // 延迟重置按压状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    isPressed = false
                }
            }
            action()
        }) {
            HStack(spacing: 4) {
                Image(systemName: isRecommended ? "hand.thumbsup.fill" : "hand.thumbsup")
                    .font(.system(size: 14, weight: .medium))
                Text(isRecommended ? "已推荐" : "推荐")
                    .font(.system(size: 14, weight: .medium))
            }
            .foregroundColor(isRecommended ? .accentColor : .secondary)
            .frame(height: 32)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isRecommended ? Color.accentColor : Color(.systemGray4), lineWidth: 1)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(isRecommended ? Color.accentColor.opacity(0.1) : Color.clear)
                    )
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview("作者主页") {
    UserProfileHeaderView(
        username: "探索者",
        avatarUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop",
        isCurrentUser: false,
        onEditProfile: {}
    )
}

#Preview("个人主页") {
    UserProfileHeaderView(
        username: "我的主页",
        avatarUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop",
        isCurrentUser: true,
        onEditProfile: {}
    )
} 
