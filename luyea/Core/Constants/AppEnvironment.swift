import Foundation
// App 全局环境配置
//
// 环境切换说明：
// 1. 生产环境（默认）：无需特殊操作，直接 Release 构建即为生产环境。
// 2. 开发环境（开发接口）：Xcode 默认 Debug 构建自动进入开发环境。
// 3. Mock 测试环境：
//    - 推荐做法：在 Xcode 的 Scheme > Edit Scheme > Build > Arguments > 添加 `-DMOCK`。
//    - 或在 Build Settings > Other Swift Flags > Debug 下添加 `-DMOCK`。
//    - 这样编译时会自动切换到 mock 环境（本地 mock server 或本地 json 数据）。
//
// 切换后，EnvironmentConfig.current 会自动返回对应环境，NetworkConstants.baseURL 也会自动切换。
//
// 如需运行时切换，可扩展为 UserDefaults/Plist/设置页切换。

enum AppEnvironment: String {
    case production
    case development
    case mock
}

struct EnvironmentConfig {
    /// 直接在此处切换 mock 环境，无需 UserDefaults
    static let forceMock: Bool = true // 切换为 false 即可关闭 mock

    static var current: AppEnvironment {
        if forceMock {
            return .mock
        }
        #if MOCK
        return .mock
        #elseif DEBUG
        return .development
        #else
        return .production
        #endif
    }

    static var baseURL: String {
        switch current {
        case .production:
            return "https://api.luyea.com"
        case .development:
            return "https://dev-api.luyea.com"
        case .mock:
            return "http://localhost:8080"
        }
    }
} 
 