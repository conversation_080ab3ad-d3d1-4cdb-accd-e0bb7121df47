import SwiftUI

struct DestinationPickerView: View {
    @ObservedObject var viewModel: DiscoverViewModel
    @Binding var isPresented: Bool
    @Binding var selectedLocation: String
    @State private var searchText: String = ""
    @State private var selectedTabId: String? = nil
    @State private var selectedCategoryId: String? = nil
    @State private var showToast: Bool = false
    @State private var allDestinations: [Destination] = []
    @State private var isLoading: Bool = false
    @State private var error: String? = nil
    @State private var showLikedOnly: Bool = false

    // Service层依赖
    private let destinationService: DestinationServiceProtocol

    // MARK: - Initialization

    init(
        viewModel: DiscoverViewModel,
        isPresented: Binding<Bool>,
        selectedLocation: Binding<String>,
        destinationService: DestinationServiceProtocol = DestinationService.shared
    ) {
        self.viewModel = viewModel
        self._isPresented = isPresented
        self._selectedLocation = selectedLocation
        self.destinationService = destinationService
    }

    var body: some View {
        let currentCategories = categories
        VStack(spacing: 0) {
            // 顶部标题栏
            ZStack {
                Text("选择探索目的地")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.primary)
                HStack {
                    Spacer()
                    Button(action: { withAnimation { isPresented = false } }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.gray)
                            .padding(8)
                    }
                    .padding(.trailing, 12)
                }
            }
            .padding(.top, 18)
            .padding(.bottom, 10)
            .background(Color(.systemBackground))
            .overlay(
                Rectangle()
                    .frame(height: 0.5)
                    .foregroundColor(Color(.systemGray5)),
                alignment: .bottom
            )

            // 搜索栏
            DestinationSearchBar(text: $searchText)
                .padding(.horizontal, 18)
                .padding(.top, 10)
                .padding(.bottom, 6)

            // 筛选按钮区域
            HStack(spacing: 12) {
                // 精选按钮
                Button(action: {
                    selectedLocation = "精选"
                    showLikedOnly = false
                    isPresented = false
                    viewModel.updateSelectedDestination(nil)
                }) {
                    Text("精选")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(selectedLocation == "精选" && !showLikedOnly ? .white : .primary)
                        .padding(.horizontal, 14)
                        .padding(.vertical, 5)
                        .background(selectedLocation == "精选" && !showLikedOnly ? Color.blue : Color(.systemGray6))
                        .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())

                // 喜欢按钮
                Button(action: {
                    showLikedOnly.toggle()
                    if showLikedOnly {
                        selectedLocation = "喜欢"
                        isPresented = false
                        viewModel.updateSelectedDestination(nil, showLikedOnly: true)
                    } else {
                        selectedLocation = "精选"
                        viewModel.updateSelectedDestination(nil, showLikedOnly: false)
                    }
                }) {
                    Text("喜欢")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(showLikedOnly ? .white : .primary)
                        .padding(.horizontal, 14)
                        .padding(.vertical, 5)
                        .background(showLikedOnly ? Color.blue : Color(.systemGray6))
                        .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())

                Spacer()
            }
            .padding(.horizontal, 18)
            .padding(.bottom, 10)

            Divider()
                .background(Color(.systemGray5))
                .padding(.horizontal, 8)
                .padding(.bottom, 4)

            // Tab分组栏
            HStack(spacing: 0) {
                ForEach(tabs, id: \.id) { tab in
                    Button(action: {
                        selectedTabId = tab.id
                        selectedCategoryId = categories.first?.id
                    }) {
                        Text(tab.name)
                            .font(.system(size: 16, weight: selectedTabId == tab.id ? .bold : .regular))
                            .foregroundColor(selectedTabId == tab.id ? .blue : Color(.systemGray3))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 6)
                            .background(selectedTabId == tab.id ? Color(.systemBackground) : Color(.systemGray6))
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .background(Color(.systemGray6))
            .padding(.horizontal, 18)
            .padding(.bottom, 6)

            // 省份/国家-城市分组内容
            HStack(alignment: .top, spacing: 0) {
                if searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    ProvinceSidebarView(categories: currentCategories, selectedCategoryId: selectedCategoryId, onSelect: { id in
                        selectedCategoryId = id
                    })
                }
                // 右侧城市网格
                ScrollView(.vertical, showsIndicators: false) {
                    if let error = error {
                        Text(error).foregroundColor(.red)
                    } else if !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && filteredCities.isEmpty {
                        Text("未找到相关目的地")
                            .foregroundColor(.gray)
                            .padding()
                    } else {
                        DestinationCityGridView(
                            province: filteredProvince,
                            cities: filteredCities.map { $0.name },
                            showProvinceTag: searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? isDomesticTab : false,
                            selectedLocation: $selectedLocation,
                            isPresented: $isPresented,
                            onSelect: { name in
                                selectedLocation = name
                                if let dest = allDestinations.first(where: { $0.name == name }) {
                                    viewModel.updateSelectedDestination(dest)
                                }
                                isPresented = false
                            }
                        )
                        .padding(.leading, 6)
                        .padding(.vertical, 8)
                        .padding(.trailing, 8)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color(.systemBackground))
                    }
                }
            }
            .padding(.horizontal, 18)
            .padding(.bottom, 16)

            Spacer()
        }
        .background(Color(.systemBackground))
        .ignoresSafeArea(edges: .bottom)
        .onAppear {
            loadAllDestinations()
            // 同步当前的喜欢状态
            showLikedOnly = viewModel.showLikedOnly
        }
        .onChange(of: selectedTabId) { _, _ in
            selectedCategoryId = categories.first?.id
        }
        .overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.10).ignoresSafeArea()
                        VStack(spacing: 12) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: Color.blue))
                                .scaleEffect(1.2)
                            Text("加载中…")
                                .font(.system(size: 15, weight: .regular))
                                .foregroundColor(.primary)
                        }
                    }
                }
                if showToast {
                    VStack {
                        Spacer()
                        ToastView(message: "国际目的地即将上线")
                    }
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .animation(.easeInOut, value: showToast)
                }
            }
        )
    }
    
    // MARK: - 计算属性
    private var tabs: [Destination] {
        allDestinations.filter { $0.type == .tab }
    }
    private var categories: [Destination] {
        guard let tabId = selectedTabId else { return [] }
        if let tab = tabs.first(where: { $0.id == tabId }), tab.name.contains("国际") {
            // 国际tab，左侧为国家
            return allDestinations.filter { $0.type == .country && $0.tabId == tabId }
        } else {
            // 国内tab，左侧为省份
            return allDestinations.filter { $0.type == .province && $0.tabId == tabId }
        }
    }
    private var cities: [Destination] {
        guard let catId = selectedCategoryId else { return [] }
        return allDestinations.filter { $0.type == .city && $0.parentId == catId }
    }
    
    private var isDomesticTab: Bool {
        guard let tabId = selectedTabId else { return false }
        if let tab = tabs.first(where: { $0.id == tabId }), tab.name.contains("国际") {
            return false
        }
        return true
    }
    
    private var filteredCities: [Destination] {
        if searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return cities
        } else {
            // 使用DestinationService进行搜索
            let searchResults = destinationService.searchDestinations(query: searchText, in: allDestinations)
            return searchResults.filter { dest in
                (dest.type == .city || dest.type == .province || dest.type == .country)
                && dest.tabId == selectedTabId
            }
        }
    }

    private var filteredProvince: String {
        if searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return categories.first(where: { $0.id == selectedCategoryId })?.name ?? ""
        } else {
            return ""
        }
    }
    
    // MARK: - 数据加载
    private func loadAllDestinations() {
        isLoading = true
        error = nil
        Task {
            do {
                let result = try await destinationService.fetchAllDestinations(forceRefresh: false)
                await MainActor.run {
                    self.allDestinations = result
                    self.selectedTabId = result.first(where: { $0.type == .tab })?.id
                    self.selectedCategoryId = categories.first?.id
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.error = "加载目的地失败"
                    self.isLoading = false
                }
            }
        }
    }
}

private func ProvinceSidebarView(categories: [Destination], selectedCategoryId: String?, onSelect: @escaping (String) -> Void) -> some View {
    ScrollViewReader { proxy in
        ScrollView(.vertical, showsIndicators: false) {
            VStack(spacing: 0) {
                ForEach(categories.indices, id: \.self) { idx in
                    let cat = categories[idx]
                    Text(cat.name)
                        .font(.system(size: 16, weight: selectedCategoryId == cat.id ? .bold : .regular))
                        .foregroundColor(selectedCategoryId == cat.id ? .blue : Color(.systemGray3))
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 6)
                        .background(selectedCategoryId == cat.id ? Color(.systemBackground) : Color(.systemGray6))
                        .id(cat.id)
                        .onTapGesture {
                            onSelect(cat.id)
                        }
                }
                Spacer()
            }
            .frame(width: 80)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
        }
        .onChange(of: selectedCategoryId) { _, newValue in
            if let id = newValue {
                withAnimation {
                    proxy.scrollTo(id, anchor: .top)
                }
            }
        }
    }
} 
