import Foundation

/// 统一的地理坐标模型
///
/// 用于表示地理位置的经纬度坐标，适用于目的地、景点等所有需要坐标信息的场景。
/// 统一了原来的 DestinationCoordinate 和 AttractionCoordinate。
struct Coordinate: Codable, Equatable {
    /// 纬度（-90到90）
    let latitude: Double
    
    /// 经度（-180到180）
    let longitude: Double
    
    /// 初始化方法
    ///
    /// - Parameters:
    ///   - latitude: 纬度，自动限制在有效范围内
    ///   - longitude: 经度，自动限制在有效范围内
    init(latitude: Double, longitude: Double) {
        self.latitude = max(-90, min(90, latitude))
        self.longitude = max(-180, min(180, longitude))
    }
}

// MARK: - 兼容性扩展

/// DestinationCoordinate 的兼容性别名
/// 保持向后兼容，逐步迁移到统一的 Coordinate
typealias DestinationCoordinate = Coordinate

/// AttractionCoordinate 的兼容性别名  
/// 保持向后兼容，逐步迁移到统一的 Coordinate
typealias AttractionCoordinate = Coordinate
