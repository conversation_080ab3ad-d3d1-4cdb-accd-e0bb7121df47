import Foundation

struct DiscoverItem: Identifiable, Equatable, Codable, Hashable {
    let id: UUID
    let imageUrls: [String]
    let title: String
    let username: String
    let userAvatarUrl: String
    let likes: Int
    let description: String
    let location: String?
    let comments: [Comment]
    let topic: Topic?
    
    init(
        id: UUID = UUID(),
        imageUrls: [String],
        title: String,
        username: String,
        userAvatarUrl: String,
        likes: Int,
        description: String,
        location: String? = nil,
        comments: [Comment] = [],
        topic: Topic? = nil
    ) {
        self.id = id
        self.imageUrls = imageUrls
        self.title = title
        self.username = username
        self.userAvatarUrl = userAvatarUrl
        self.likes = likes
        self.description = description
        self.location = location
        self.comments = comments
        self.topic = topic
    }
    
    static func == (lhs: DiscoverItem, rhs: DiscoverItem) -> Bool {
        lhs.id == rhs.id &&
        lhs.imageUrls == rhs.imageUrls &&
        lhs.title == rhs.title &&
        lhs.username == rhs.username &&
        lhs.userAvatarUrl == rhs.userAvatarUrl &&
        lhs.likes == rhs.likes &&
        lhs.description == rhs.description &&
        lhs.location == rhs.location &&
        lhs.comments == rhs.comments &&
        lhs.topic == rhs.topic
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    enum CodingKeys: String, CodingKey {
        case id, imageUrls, title, username, userAvatarUrl, likes, description, location, comments, topic
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        if let id = try? container.decode(UUID.self, forKey: .id) {
            self.id = id
        } else {
            self.id = UUID()
        }
        self.imageUrls = try container.decode([String].self, forKey: .imageUrls)
        self.title = try container.decode(String.self, forKey: .title)
        self.username = try container.decode(String.self, forKey: .username)
        self.userAvatarUrl = try container.decode(String.self, forKey: .userAvatarUrl)
        self.likes = try container.decode(Int.self, forKey: .likes)
        self.description = try container.decode(String.self, forKey: .description)
        self.location = try container.decodeIfPresent(String.self, forKey: .location)
        self.comments = try container.decode([Comment].self, forKey: .comments)
        self.topic = try container.decodeIfPresent(Topic.self, forKey: .topic)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(imageUrls, forKey: .imageUrls)
        try container.encode(title, forKey: .title)
        try container.encode(username, forKey: .username)
        try container.encode(userAvatarUrl, forKey: .userAvatarUrl)
        try container.encode(likes, forKey: .likes)
        try container.encode(description, forKey: .description)
        try container.encode(location, forKey: .location)
        try container.encode(comments, forKey: .comments)
        try container.encode(topic, forKey: .topic)
    }
}

struct Comment: Identifiable, Equatable, Codable, Hashable {
    let id: UUID
    let username: String
    let content: String
    let timestamp: Date
    
    init(id: UUID = UUID(), username: String, content: String, timestamp: Date = Date()) {
        self.id = id
        self.username = username
        self.content = content
        self.timestamp = timestamp
    }
    
    static func == (lhs: Comment, rhs: Comment) -> Bool {
        lhs.id == rhs.id &&
        lhs.username == rhs.username &&
        lhs.content == rhs.content &&
        lhs.timestamp == rhs.timestamp
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    enum CodingKeys: String, CodingKey {
        case id, username, content, timestamp
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        if let id = try? container.decode(UUID.self, forKey: .id) {
            self.id = id
        } else {
            self.id = UUID()
        }
        self.username = try container.decode(String.self, forKey: .username)
        self.content = try container.decode(String.self, forKey: .content)

        // 处理 timestamp 的多种格式
        if let timestampValue = try? container.decodeIfPresent(Double.self, forKey: .timestamp) {
            // 时间戳格式（秒）
            self.timestamp = Date(timeIntervalSince1970: timestampValue)
        } else if let timestampString = try? container.decodeIfPresent(String.self, forKey: .timestamp) {
            // ISO8601 字符串格式
            let formatter = ISO8601DateFormatter()
            self.timestamp = formatter.date(from: timestampString) ?? Date()
        } else {
            // 默认为当前时间
            self.timestamp = Date()
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(username, forKey: .username)
        try container.encode(content, forKey: .content)
        try container.encode(timestamp, forKey: .timestamp)
    }
} 