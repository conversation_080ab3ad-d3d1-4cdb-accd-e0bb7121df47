import SwiftUI
import Combine

/// 行程创建视图模型
///
/// 管理创建行程Tab的所有状态和业务逻辑，采用MVVM架构模式。
/// 负责目的地选择、景点搜索、行程规划等核心功能的状态管理。
///
/// 主要功能：
/// - 目的地和景点选择状态管理
/// - 景点搜索和结果处理
/// - 行程规划数据管理
/// - 防抖搜索和异步处理
@MainActor
final class ItineraryCreationViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 选中的目的地列表
    @Published var selectedDestinations: [Destination] = [] {
        didSet {
            updateUIState()
        }
    }

    /// 选中的景点列表
    @Published var selectedAttractions: [AttractionModel] = [] {
        didSet {
            updateUIState()
        }
    }

    /// 收藏的景点列表
    @Published var favoriteAttractions: [AttractionModel] = []

    /// 搜索文本
    @Published var searchText: String = ""

    /// 搜索结果
    @Published var searchResults: [AttractionModel] = []

    /// 是否正在搜索
    @Published var isSearching: Bool = false

    /// 是否显示搜索结果
    @Published var showSearchResults: Bool = false

    /// 是否显示目的地选择器
    @Published var showDestinationPicker: Bool = false

    /// 是否显示收藏列表
    @Published var showFavoritesList: Bool = false

    /// 是否正在创建行程
    @Published var isCreatingItinerary: Bool = false

    // MARK: - Trip Planning Properties

    /// 出发地点
    @Published var departureLocation: String = ""

    /// 出发日期
    @Published var departureDate: Date = Date()

    /// 是否已选择日期
    @Published var hasSelectedDate: Bool = false

    /// 行程天数
    @Published var tripDays: Int = 3

    /// 是否包含返程
    @Published var includeReturn: Bool = true

    /// 预计预算
    @Published var estimatedBudget: String = ""

    /// 是否启用城市推荐
    @Published var enableCityRecommendation: Bool = false

    /// 推荐类型集合
    @Published var recommendationTypes: Set<RecommendationType> = []

    // MARK: - UI State Properties

    /// 是否显示推荐区域
    @Published var showRecommendationSection: Bool = false

    /// 是否有选中的景点（用于UI状态判断）
    @Published var hasSelectedAttractions: Bool = false

    /// 是否有选中的目的地（用于UI状态判断）
    @Published var hasSelectedDestinations: Bool = false

    // MARK: - Private Properties

    /// 新建行程服务
    private let newItineraryService: NewItineraryServiceProtocol

    /// 搜索结果缓存（带时间戳）
    private var searchCache: [String: (results: [AttractionModel], timestamp: Date)] = [:]

    /// 缓存过期时间（5分钟）
    private let cacheExpirationTime: TimeInterval = 300

    /// Combine订阅集合
    private var cancellables = Set<AnyCancellable>()

    /// 搜索防抖器（使用项目统一的防抖工具）
    private let searchDebouncer = Debouncer(delay: DesignSystemConstants.Interaction.debounceDelay)

    // MARK: - Initialization

    init(newItineraryService: NewItineraryServiceProtocol = NewItineraryService()) {
        self.newItineraryService = newItineraryService
        setupObservers()
        loadInitialData()
    }

    deinit {
        // 注意：searchDebouncer 会在自己的 deinit 中自动取消正在进行的任务
        // 因此这里不需要手动调用 searchDebouncer.cancel()
    }

    // MARK: - Public Methods - 目的地管理

    /// 添加目的地
    /// - Parameter destination: 要添加的目的地
    func addDestination(_ destination: Destination) {
        guard !selectedDestinations.contains(where: { $0.id == destination.id }) else {
            return
        }

        withAnimation(.easeInOut(duration: 0.25)) {
            selectedDestinations.append(destination)
            updateUIState()
        }

        Log.debug("🎯 ✅ 成功添加目的地: \(destination.name)")
        Log.debug("🎯 当前目的地数量: \(selectedDestinations.count), 推荐区域显示: \(showRecommendationSection)")
    }

    /// 移除目的地
    /// - Parameter destination: 要移除的目的地
    func removeDestination(_ destination: Destination) {
        Log.debug("🎯 开始移除目的地: \(destination.name)")

        // 记录移除前的状态
        let destinationsCountBefore = selectedDestinations.count

        // 使用与景点相同的弹簧动画，保持交互一致性
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
            selectedDestinations.removeAll { $0.id == destination.id }
            updateUIState()

            // 记录移除后的状态
            let destinationsCountAfter = selectedDestinations.count
            Log.debug("🎯 移除完成 - 目的地: \(destinationsCountBefore) -> \(destinationsCountAfter)")
        }

        Log.debug("🎯 ✅ 成功移除目的地: \(destination.name)")
    }

    /// 清空所有目的地
    func clearDestinations() {
        Log.debug("🎯 开始清空所有目的地")

        let destinationsCountBefore = selectedDestinations.count

        // 使用与景点相同的弹簧动画，保持交互一致性
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
            selectedDestinations.removeAll()
            updateUIState()
        }

        Log.debug("🎯 ✅ 清空完成 - 移除了 \(destinationsCountBefore) 个目的地")
    }

    // MARK: - Public Methods - 景点管理

    /// 添加景点
    /// - Parameter attraction: 要添加的景点
    func addAttraction(_ attraction: AttractionModel) {
        guard !selectedAttractions.contains(where: { $0.id == attraction.id }) else {
            Log.debug("🎯 景点已存在，跳过添加: \(attraction.name)")
            return
        }

        Log.debug("🎯 开始添加景点: \(attraction.name), 城市: \(attraction.city)")

        // 记录添加前的状态
        let destinationsCountBefore = selectedDestinations.count
        let attractionsCountBefore = selectedAttractions.count

        // 使用流畅的动画执行整个添加过程
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
            // 将新景点添加到最前面，让用户立即看到最新添加的内容
            selectedAttractions.insert(attraction, at: 0)

            // 自动添加对应的城市目的地
            let cityAdded = autoAddCityDestination(for: attraction)

            // 更新UI状态
            updateUIState()

            // 记录添加后的状态
            let destinationsCountAfter = selectedDestinations.count
            let attractionsCountAfter = selectedAttractions.count

            Log.debug("🎯 添加完成 - 景点: \(attractionsCountBefore) -> \(attractionsCountAfter), 目的地: \(destinationsCountBefore) -> \(destinationsCountAfter)")

            Log.debug("🎯 添加景点成功: \(attraction.name)")
            if cityAdded {
                Log.debug("🎯 同时自动添加了城市: \(attraction.city)")
            } else {
                Log.debug("🎯 城市已存在，未重复添加: \(attraction.city)")
            }

            // 打印最终状态
            Log.debug("🎯 最终状态 - 城市数量: \(selectedDestinations.filter { $0.type == .city }.count), 推荐可用: \(!isRecommendationDisabled), 推荐启用: \(enableCityRecommendation)")
        }
    }

    /// 移除景点
    /// - Parameter attraction: 要移除的景点
    func removeAttraction(_ attraction: AttractionModel) {
        Log.debug("🎯 开始移除景点: \(attraction.name)")

        // 记录移除前的状态
        let attractionsCountBefore = selectedAttractions.count

        // 使用与添加相同的弹簧动画，保持交互一致性
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
            selectedAttractions.removeAll { $0.id == attraction.id }
            updateUIState()

            // 记录移除后的状态
            let attractionsCountAfter = selectedAttractions.count
            Log.debug("🎯 移除完成 - 景点: \(attractionsCountBefore) -> \(attractionsCountAfter)")
        }

        Log.debug("🎯 ✅ 成功移除景点: \(attraction.name)")
    }

    /// 清空所有景点
    func clearAttractions() {
        Log.debug("🎯 开始清空所有景点")

        let attractionsCountBefore = selectedAttractions.count

        // 使用与添加相同的弹簧动画，保持交互一致性
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0)) {
            selectedAttractions.removeAll()
            updateUIState()
        }

        Log.debug("🎯 ✅ 清空完成 - 移除了 \(attractionsCountBefore) 个景点")
    }

    /// 切换景点选择状态（用于组件统一调用）
    /// - Parameter attraction: 要切换状态的景点
    func toggleAttractionSelection(_ attraction: AttractionModel) {
        if selectedAttractions.contains(where: { $0.id == attraction.id }) {
            removeAttraction(attraction)
        } else {
            addAttraction(attraction)
        }
    }

    // MARK: - Public Methods - 搜索功能

    /// 执行搜索（带防抖）
    /// - Parameter query: 搜索关键词
    func performSearch(query: String) {
        // 使用统一的防抖工具
        searchDebouncer.call {
            Task { @MainActor in
                self.performSearchInternal(query: query)
            }
        }
    }

    /// 内部搜索实现（不带防抖）
    /// - Parameter query: 搜索关键词
    private func performSearchInternal(query: String) {
        // 如果查询为空，清空结果
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            clearSearchResults()
            return
        }

        // 检查缓存（带过期检查）
        if let cachedData = searchCache[query] {
            let isExpired = Date().timeIntervalSince(cachedData.timestamp) > cacheExpirationTime
            if !isExpired {
                updateSearchResults(cachedData.results, for: query)
                return
            } else {
                // 移除过期缓存
                searchCache.removeValue(forKey: query)
            }
        }

        // 设置搜索状态
        isSearching = true
        showSearchResults = true

        // 执行搜索（直接在当前任务中执行，由Debouncer管理任务生命周期）
        Task {
            let results = await executeSearch(query: query)

            // 更新结果
            await MainActor.run {
                updateSearchResults(results, for: query)
            }
        }
    }

    /// 清空搜索结果
    func clearSearchResults() {
        searchDebouncer.cancel()
        searchResults.removeAll()
        showSearchResults = false
        isSearching = false
    }

    // MARK: - Public Methods - 行程创建

    func createItinerary() {
        let data = buildItineraryCreationData()

        // 先进行本地验证
        let validation = data.validate()
        guard validation.isValid else {
            // 显示验证错误
            ToastManager.shared.show("数据验证失败: \(validation.errors.joined(separator: ", "))", style: .error)
            return
        }

        Task {
            do {
                // 显示加载状态
                await MainActor.run {
                    isCreatingItinerary = true
                }

                let result = try await newItineraryService.createItinerary(data)
                await MainActor.run {
                    isCreatingItinerary = false
                    Log.info("🎯 ✅ 行程创建成功: \(result.itineraryId)")
                    ToastManager.shared.show("行程创建成功！", style: .success)
                    // 这里可以添加成功后的处理逻辑，比如导航到行程详情页
                }
            } catch {
                await MainActor.run {
                    isCreatingItinerary = false
                    Log.error("🎯 ❌ 行程创建失败: \(error.localizedDescription)")

                    // 根据错误类型显示不同的提示
                    if let newItineraryError = error as? NewItineraryError {
                        ToastManager.shared.show(newItineraryError.localizedDescription, style: .error)
                    } else {
                        ToastManager.shared.show("创建行程失败，请稍后重试", style: .error)
                    }
                }
            }
        }
    }

    /// 重置所有状态
    func resetAllState() {
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedDestinations.removeAll()
            selectedAttractions.removeAll()
            searchText = ""
            clearSearchResults()
            departureLocation = ""
            hasSelectedDate = false
            tripDays = 3
            includeReturn = true
            estimatedBudget = ""
            enableCityRecommendation = false
            recommendationTypes.removeAll()
            updateUIState()
        }

        Log.debug("🎯 重置所有状态")
    }

    // MARK: - Private Methods

    /// 设置观察者
    private func setupObservers() {
        // 监听搜索文本变化（直接调用performSearch，让它处理防抖）
        $searchText
            .removeDuplicates()
            .sink { [weak self] query in
                self?.performSearch(query: query)
            }
            .store(in: &cancellables)

        // 监听选中状态变化
        Publishers.CombineLatest($selectedDestinations, $selectedAttractions)
            .sink { [weak self] destinations, attractions in
                self?.updateUIState()
            }
            .store(in: &cancellables)
    }

    /// 加载初始数据
    private func loadInitialData() {
        Task {
            // 异步加载收藏景点
            await loadFavoriteAttractions()
        }
    }

    /// 更新UI状态
    private func updateUIState() {
        hasSelectedDestinations = !selectedDestinations.isEmpty
        hasSelectedAttractions = !selectedAttractions.isEmpty
        showRecommendationSection = hasSelectedDestinations || hasSelectedAttractions

        // 智能管理推荐开关状态
        updateRecommendationState()
    }

    /// 智能更新推荐状态
    private func updateRecommendationState() {
        let hasCities = !selectedDestinations.filter { $0.type == .city }.isEmpty

        if !hasCities {
            // 如果没有城市，自动关闭推荐并禁用
            if enableCityRecommendation {
                enableCityRecommendation = false
                Log.debug("🎯 没有目的地城市，自动关闭推荐功能")
            }
        } else {
            // 有城市时，必须启用推荐功能且无法关闭
            if !enableCityRecommendation {
                enableCityRecommendation = true
                Log.debug("🎯 有目的地城市，自动启用推荐功能")
            }
            Log.debug("🎯 有目的地城市(\(selectedDestinations.filter { $0.type == .city }.count)个)，推荐功能强制启用")
            Log.debug("🎯 推荐状态: \(recommendationStatusDescription)")
        }
    }

    /// 智能启用推荐（当自动添加城市时调用）
    private func smartEnableRecommendationIfNeeded() {
        let hasCities = !selectedDestinations.filter { $0.type == .city }.isEmpty

        // 如果有城市且推荐功能未启用，可以考虑自动启用
        // 但为了尊重用户选择，这里只是提示可用，不强制开启
        if hasCities && !enableCityRecommendation {
            // 可以在这里添加逻辑，比如显示提示或自动开启
            Log.debug("🎯 提示：推荐功能现在可用，您可以手动开启")
        }
    }

    /// 自动添加城市目的地
    /// - Parameter attraction: 景点信息
    /// - Returns: 是否成功添加了城市
    @discardableResult
    private func autoAddCityDestination(for attraction: AttractionModel) -> Bool {
        Log.debug("🎯 尝试自动添加城市: \(attraction.city) (来源景点: \(attraction.name))")
        Log.debug("🎯 当前已选目的地数量: \(selectedDestinations.count)")

        // 打印当前已选目的地列表
        for (index, destination) in selectedDestinations.enumerated() {
            Log.debug("🎯 已选目的地[\(index)]: \(destination.name) (类型: \(destination.type))")
        }

        let addedCity = UnifiedDestinationService.shared.addDestinationCityIfNeeded(
            cityName: attraction.city,
            sourceAttraction: attraction,
            to: &selectedDestinations
        )

        Log.debug("🎯 添加城市后的目的地数量: \(selectedDestinations.count)")

        if let addedCity = addedCity {
            Log.debug("🎯 ✅ 成功自动添加城市目的地: \(addedCity.name) (ID: \(addedCity.id), 来源景点: \(attraction.name))")

            // 智能提示推荐功能可用
            smartEnableRecommendationIfNeeded()

            return true
        } else {
            Log.debug("🎯 ❌ 城市已存在或添加失败: \(attraction.city)")
            return false
        }
    }

    /// 执行实际搜索
    private func executeSearch(query: String) async -> [AttractionModel] {
        do {
            // 使用Service层进行搜索
            let results = try await newItineraryService.searchAttractions(query: query, location: nil)
            return results
        } catch {
            Log.error("🎯 ❌ 搜索失败: \(error.localizedDescription)")

            // 降级到本地搜索
            let results = AttractionModel.samples.filter { attraction in
                attraction.name.localizedCaseInsensitiveContains(query) ||
                attraction.city.localizedCaseInsensitiveContains(query) ||
                attraction.tags.joined().localizedCaseInsensitiveContains(query)
            }
            return results
        }
    }

    /// 更新搜索结果
    private func updateSearchResults(_ results: [AttractionModel], for query: String) {
        searchResults = results
        isSearching = false

        // 缓存结果（带时间戳）
        searchCache[query] = (results: results, timestamp: Date())

        // 限制缓存大小并清理过期缓存
        cleanupCache()
    }

    /// 清理缓存
    private func cleanupCache() {
        let now = Date()

        // 移除过期缓存
        searchCache = searchCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) <= cacheExpirationTime
        }

        // 如果缓存仍然过大，移除最旧的条目
        if searchCache.count > 50 {
            let sortedKeys = searchCache.keys.sorted { key1, key2 in
                searchCache[key1]!.timestamp < searchCache[key2]!.timestamp
            }

            for key in sortedKeys.prefix(searchCache.count - 40) {
                searchCache.removeValue(forKey: key)
            }
        }
    }

    /// 加载收藏景点
    private func loadFavoriteAttractions() async {
        // 模拟异步加载
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2秒

        await MainActor.run {
            // 这里应该从本地存储或网络加载收藏数据
            favoriteAttractions = Array(AttractionModel.samples.prefix(5))
        }
    }

    /// 保存草稿
    func saveDraft() {
        let data = buildItineraryCreationData()
        Task {
            do {
                try await newItineraryService.saveDraft(data)
                Log.info("🎯 ✅ 草稿保存成功")
            } catch {
                Log.error("🎯 ❌ 草稿保存失败: \(error.localizedDescription)")
            }
        }
    }

    /// 加载草稿
    func loadDraft() {
        Task {
            do {
                if let draftData = try await newItineraryService.loadDraft() {
                    await MainActor.run {
                        // 恢复草稿数据
                        selectedDestinations = draftData.destinations
                        selectedAttractions = draftData.attractions
                        departureLocation = draftData.departureLocation
                        tripDays = draftData.tripDays
                        includeReturn = draftData.includeReturn
                        estimatedBudget = draftData.budgetString
                        enableCityRecommendation = draftData.isRecommendationEnabled

                        // 恢复日期
                        if let date = draftData.departureDate {
                            departureDate = date
                            hasSelectedDate = true
                        }

                        updateUIState()
                        Log.info("🎯 ✅ 草稿加载成功")
                    }
                }
            } catch {
                Log.error("🎯 ❌ 草稿加载失败: \(error.localizedDescription)")
            }
        }
    }



    /// 构建行程创建数据
    private func buildItineraryCreationData() -> ItineraryCreationData {
        return ItineraryCreationData(
            departureLocation: departureLocation,
            departureDate: hasSelectedDate ? departureDate : nil,
            destinations: selectedDestinations,
            attractions: selectedAttractions,
            tripType: includeReturn ? .roundTrip : .oneWay,
            tripDays: tripDays,
            budget: estimatedBudget.isEmpty ? nil : Double(estimatedBudget),
            recommendationEnabled: enableCityRecommendation
        )
    }
}

// MARK: - Computed Properties

extension ItineraryCreationViewModel {

    /// 是否可以创建行程
    var canCreateItinerary: Bool {
        hasSelectedDestinations || hasSelectedAttractions
    }

    /// 创建按钮文案
    var createButtonTitle: String {
        if selectedAttractions.isEmpty {
            return "创建行程（推荐景点）"
        } else {
            return "创建行程"
        }
    }

    /// 搜索结果数量
    var searchResultsCount: Int {
        searchResults.count
    }

    /// 是否有搜索结果
    var hasSearchResults: Bool {
        !searchResults.isEmpty
    }

    /// 是否有目的地城市（推荐功能的前提条件）
    var hasCityDestinations: Bool {
        !selectedDestinations.filter { $0.type == .city }.isEmpty
    }

    /// 推荐功能是否应该被禁用
    var isRecommendationDisabled: Bool {
        !hasCityDestinations
    }

    /// 推荐功能是否应该强制开启（当有目的地城市时）
    var shouldForceEnableRecommendation: Bool {
        // 如果添加了目的地城市，必须启用且无法关闭
        return hasCityDestinations
    }

    /// 推荐状态描述（用于调试和UI显示）
    var recommendationStatusDescription: String {
        if isRecommendationDisabled {
            return "推荐功能已禁用：缺少目的地城市"
        } else if shouldForceEnableRecommendation {
            return "推荐功能已强制启用：有目的地城市"
        } else if enableCityRecommendation {
            return "推荐功能已启用"
        } else {
            return "推荐功能可用但未启用"
        }
    }
}