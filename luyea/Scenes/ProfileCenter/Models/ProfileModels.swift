import Foundation

/// 个人中心模块数据模型集合
///
/// 包含ProfileCenter模块所需的所有数据模型定义，遵循四层架构规范。
/// 按功能域分组组织，提供类型安全的数据结构支持。
///
/// 功能特性:
/// - 用户资料和统计数据模型
/// - 内容发现和作品管理模型
/// - 支持Codable序列化和Equatable比较
/// - 提供示例数据和空状态支持
///
/// 架构职责:
/// - 📊 数据结构：定义模块内所有数据模型
/// - 🔄 序列化支持：支持JSON编解码和数据持久化
/// - 🎯 类型安全：提供强类型的数据访问接口
/// - 📝 文档完整：每个模型都有详细的文档说明
///
/// - Note: 所有模型都支持Codable，便于网络传输和本地存储
/// - Warning: 修改模型结构时需要考虑向后兼容性
enum ProfileModels {

    // MARK: - 用户统计数据模型

    /// 用户统计数据
    ///
    /// 包含个人中心页面显示的统计信息。
    struct UserStats: Codable, Equatable {
        /// 推荐数量
        let recommendations: Int

        /// 获得的点赞数
        let likes: Int

        /// 发布的内容数量
        let posts: Int

        /// 初始化方法
        init(
            recommendations: Int = 0,
            likes: Int = 0,
            posts: Int = 0
        ) {
            self.recommendations = max(0, recommendations)
            self.likes = max(0, likes)
            self.posts = max(0, posts)
        }

        /// 空统计数据
        static let empty = UserStats()
    }







    // MARK: - 用户发现内容模型

    /// 用户主页中的发现内容数据模型
    ///
    /// 表示用户发布或收藏的发现内容，包含完整的元数据信息。
    struct UserDiscoverItem: Identifiable, Codable, Equatable {
        /// 内容唯一标识符
        let id: String

        /// 内容标题
        let title: String

        /// 内容描述
        let description: String

        /// 封面图片URL
        let coverImage: String?

        /// 内容标签
        let tag: String?

        /// 点赞数
        let likes: Int

        /// 评论数
        let comments: Int

        /// 浏览数
        let views: Int

        /// 收藏数
        let collections: Int

        /// 创建时间
        let createdAt: Date

        /// 更新时间
        let updatedAt: Date?

        /// 位置信息
        let location: String?

        /// 当前用户是否已点赞
        let isLiked: Bool

        /// 当前用户是否已收藏
        let isCollected: Bool

        /// 内容状态
        let status: ContentStatus

        /// 作者信息
        let author: UserInfo?

        /// 额外的媒体文件
        let mediaUrls: [String]

        /// 是否包含行程内容
        let hasItinerary: Bool

        /// 行程被fork的数量（仅当hasItinerary为true时有效）
        let forkCount: Int

        /// 初始化方法
        init(
            id: String,
            title: String,
            description: String,
            coverImage: String? = nil,
            tag: String? = nil,
            likes: Int = 0,
            comments: Int = 0,
            views: Int = 0,
            collections: Int = 0,
            createdAt: Date = Date(),
            updatedAt: Date? = nil,
            location: String? = nil,
            isLiked: Bool = false,
            isCollected: Bool = false,
            status: ContentStatus = .published,
            author: UserInfo? = nil,
            mediaUrls: [String] = [],
            hasItinerary: Bool = false,
            forkCount: Int = 0
        ) {
            self.id = id
            self.title = title
            self.description = description
            self.coverImage = coverImage
            self.tag = tag
            self.likes = max(0, likes)
            self.comments = max(0, comments)
            self.views = max(0, views)
            self.collections = max(0, collections)
            self.createdAt = createdAt
            self.updatedAt = updatedAt
            self.location = location
            self.isLiked = isLiked
            self.isCollected = isCollected
            self.status = status
            self.author = author
            self.mediaUrls = mediaUrls
            self.hasItinerary = hasItinerary
            self.forkCount = max(0, forkCount)
        }

        // MARK: - Computed Properties

        /// 是否为热门内容
        var isPopular: Bool {
            likes > 100 || views > 1000
        }

        /// 互动总数
        var totalInteractions: Int {
            likes + comments + collections
        }

        /// 格式化的创建时间
        var formattedCreatedAt: String {
            let formatter = RelativeDateTimeFormatter()
            formatter.locale = Locale(identifier: "zh_CN")
            return formatter.localizedString(for: createdAt, relativeTo: Date())
        }
    }

    // MARK: - 内容状态枚举

    /// 内容状态
    enum ContentStatus: String, Codable, CaseIterable {
        /// 草稿
        case draft = "draft"
        /// 已发布
        case published = "published"
        /// 已隐藏
        case hidden = "hidden"
        /// 已删除
        case deleted = "deleted"

        /// 状态显示名称
        var displayName: String {
            switch self {
            case .draft:
                return "草稿"
            case .published:
                return "已发布"
            case .hidden:
                return "已隐藏"
            case .deleted:
                return "已删除"
            }
        }
    }
    // MARK: - 用户信息模型

    /// 简化的用户信息模型
    ///
    /// 用于在内容中显示作者信息，避免循环引用。
    struct UserInfo: Codable, Equatable {
        /// 用户ID
        let id: String

        /// 用户名
        let username: String

        /// 头像URL
        let avatarUrl: String?

        /// 是否为认证用户
        let isVerified: Bool

        /// 初始化方法
        init(
            id: String,
            username: String,
            avatarUrl: String? = nil,
            isVerified: Bool = false
        ) {
            self.id = id
            self.username = username
            self.avatarUrl = avatarUrl
            self.isVerified = isVerified
        }
    }

    // MARK: - 用户资料模型

    /// 用户资料模型
    ///
    /// 包含个人中心页面需要的用户信息。
    struct UserProfile: Codable, Equatable {
        /// 用户ID
        let id: String

        /// 用户显示名称
        let displayName: String

        /// 头像URL
        let avatarUrl: String?

        /// 归属地
        let location: String?

        /// 用户统计数据
        let stats: UserStats

        /// 初始化方法
        init(
            id: String,
            displayName: String,
            avatarUrl: String? = nil,
            location: String? = nil,
            stats: UserStats = .empty
        ) {
            self.id = id
            self.displayName = displayName
            self.avatarUrl = avatarUrl
            self.location = location
            self.stats = stats
        }

        /// 示例用户资料
        static let sample = UserProfile(
            id: "1098570",
            displayName: "高级知识质子",
            avatarUrl: "https://picsum.photos/200/200?random=1",
            location: "北京",
            stats: UserStats(
                recommendations: 128,
                likes: 2456,
                posts: 45
            )
        )
    }

    // MARK: - 头像上传响应模型

    /// 头像上传响应
    struct AvatarUploadResponse: Codable {
        /// 上传后的头像URL
        let avatarUrl: String

        /// 初始化方法
        init(avatarUrl: String) {
            self.avatarUrl = avatarUrl
        }
    }
}