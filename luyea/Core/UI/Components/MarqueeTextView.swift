import SwiftUI

/// 跑马灯文字滚动组件
///
/// 实现文字的水平滚动效果，当文字长度超过容器宽度时自动启动滚动。
/// 提供无缝循环滚动、速度控制和性能优化功能。
///
/// 核心功能：
/// - 自动检测文字长度并启动滚动
/// - 无缝循环滚动效果
/// - 可配置的滚动速度
/// - 性能优化和资源管理
/// - 支持动态文字更新
/// - 可访问性支持
///
/// 设计原则：
/// - 性能优先：避免不必要的重绘
/// - 用户友好：自然的滚动体验
/// - 资源节约：智能的动画管理
/// - 可配置性：灵活的参数调整
struct MarqueeTextView: View {
    
    // MARK: - Properties
    
    /// 要显示的文字
    let text: String
    
    /// 容器宽度
    let width: CGFloat
    
    /// 文字字体
    let font: Font
    
    /// 滚动速度（点/秒）
    let speed: Double
    
    /// 滚动间隔（文字结束到重新开始的间距）
    let spacing: CGFloat
    
    /// 是否启用滚动
    let isScrollEnabled: Bool
    
    // MARK: - State
    
    /// 文字实际宽度
    @State private var textWidth: CGFloat = 0
    
    /// 当前偏移量
    @State private var offset: CGFloat = 0
    
    /// 滚动动画任务
    @State private var animationTask: Task<Void, Never>?
    
    /// 是否正在滚动
    @State private var isScrolling: Bool = false
    
    /// 是否需要滚动（文字宽度超过容器宽度）
    @State private var needsScrolling: Bool = false
    
    // MARK: - Constants
    
    /// 默认配置
    private struct DefaultConfig {
        static let speed: Double = 30.0 // 点/秒
        static let spacing: CGFloat = 40.0 // 间距
        static let pauseDuration: TimeInterval = 1.0 // 暂停时间
        static let animationSteps: Int = 60 // 动画步数（60fps）
    }
    
    // MARK: - Initialization
    
    /// 创建跑马灯文字视图
    /// - Parameters:
    ///   - text: 要显示的文字
    ///   - width: 容器宽度
    ///   - font: 文字字体
    ///   - speed: 滚动速度（点/秒），默认30
    ///   - spacing: 滚动间隔，默认40
    ///   - isScrollEnabled: 是否启用滚动，默认true
    init(
        text: String,
        width: CGFloat,
        font: Font = .body,
        speed: Double = DefaultConfig.speed,
        spacing: CGFloat = DefaultConfig.spacing,
        isScrollEnabled: Bool = true
    ) {
        self.text = text
        self.width = width
        self.font = font
        self.speed = max(1.0, speed) // 确保速度至少为1
        self.spacing = max(0, spacing)
        self.isScrollEnabled = isScrollEnabled
    }
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 主文字
                textContent
                    .offset(x: offset)
                
                // 如果需要滚动，显示重复的文字以实现无缝循环
                if needsScrolling && isScrolling {
                    textContent
                        .offset(x: offset + textWidth + spacing)
                }
            }
            .clipped()
            .onAppear {
                setupScrolling()
            }
            .onDisappear {
                stopScrolling()
            }
            .onChange(of: text) { _, _ in
                resetAndSetupScrolling()
            }
            .onChange(of: width) { _, _ in
                resetAndSetupScrolling()
            }
        }
        .frame(width: width)
        .frame(maxHeight: .infinity) // 确保容器高度适应内容
        .clipped() // 裁剪超出部分
        .accessibilityElement(children: .ignore)
        .accessibilityLabel(text)
        .accessibilityValue(isScrolling ? "正在滚动" : "静态显示")
    }
    
    // MARK: - Subviews
    
    /// 文字内容
    private var textContent: some View {
        Text(text)
            .font(font)
            .lineLimit(1)
            .fixedSize(horizontal: true, vertical: false)
            .background(
                GeometryReader { textGeometry in
                    Color.clear
                        .onAppear {
                            updateTextWidth(textGeometry.size.width)
                        }
                        .onChange(of: textGeometry.size.width) { _, newWidth in
                            updateTextWidth(newWidth)
                        }
                }
            )
    }
    
    // MARK: - Private Methods
    
    /// 更新文字宽度
    /// - Parameter newWidth: 新的文字宽度
    private func updateTextWidth(_ newWidth: CGFloat) {
        guard newWidth != textWidth else { return }

        textWidth = newWidth
        needsScrolling = isScrollEnabled && textWidth > width
    }
    
    /// 设置滚动
    private func setupScrolling() {
        guard needsScrolling else {
            // 不需要滚动，重置偏移量
            offset = 0
            return
        }
        
        startScrolling()
    }
    
    /// 重置并设置滚动
    private func resetAndSetupScrolling() {
        stopScrolling()
        offset = 0
        isScrolling = false
        
        // 延迟一帧以确保布局更新完成
        DispatchQueue.main.async {
            setupScrolling()
        }
    }
    
    /// 开始滚动
    private func startScrolling() {
        guard needsScrolling && !isScrolling else { return }

        isScrolling = true

        animationTask = Task {
            await performScrollAnimation()
        }
    }
    
    /// 停止滚动
    private func stopScrolling() {
        animationTask?.cancel()
        animationTask = nil
        isScrolling = false
    }
    
    /// 执行滚动动画
    private func performScrollAnimation() async {
        while !Task.isCancelled && needsScrolling {
            // 计算滚动距离和时间
            let totalDistance = textWidth + spacing
            let duration = totalDistance / speed

            // 重置到起始位置
            await MainActor.run {
                offset = 0
            }

            // 暂停一下再开始滚动
            try? await Task.sleep(nanoseconds: UInt64(DefaultConfig.pauseDuration * 1_000_000_000))

            // 使用 SwiftUI 原生动画进行流畅滚动
            await MainActor.run {
                withAnimation(.linear(duration: duration)) {
                    offset = -totalDistance
                }
            }

            // 等待动画完成
            try? await Task.sleep(nanoseconds: UInt64(duration * 1_000_000_000))

            // 检查是否需要继续滚动
            guard !Task.isCancelled && needsScrolling else { break }
        }
    }
}

// MARK: - 便利初始化

extension MarqueeTextView {
    
    /// 创建简单的跑马灯文字视图
    /// - Parameters:
    ///   - text: 要显示的文字
    ///   - width: 容器宽度
    ///   - font: 文字字体
    static func simple(
        text: String,
        width: CGFloat,
        font: Font = .body
    ) -> MarqueeTextView {
        MarqueeTextView(
            text: text,
            width: width,
            font: font
        )
    }
    
    /// 创建快速滚动的跑马灯文字视图
    /// - Parameters:
    ///   - text: 要显示的文字
    ///   - width: 容器宽度
    ///   - font: 文字字体
    static func fast(
        text: String,
        width: CGFloat,
        font: Font = .body
    ) -> MarqueeTextView {
        MarqueeTextView(
            text: text,
            width: width,
            font: font,
            speed: 50.0
        )
    }
    
    /// 创建慢速滚动的跑马灯文字视图
    /// - Parameters:
    ///   - text: 要显示的文字
    ///   - width: 容器宽度
    ///   - font: 文字字体
    static func slow(
        text: String,
        width: CGFloat,
        font: Font = .body
    ) -> MarqueeTextView {
        MarqueeTextView(
            text: text,
            width: width,
            font: font,
            speed: 15.0
        )
    }
}

// MARK: - 预览

#Preview("跑马灯文字 - 长文本") {
    VStack(spacing: 20) {
        Text("跑马灯文字滚动效果")
            .font(.headline)
        
        VStack(spacing: 15) {
            // 长文本滚动
            MarqueeTextView(
                text: "这是一个很长的音频标题，用于测试跑马灯滚动效果的显示",
                width: 200,
                font: .system(size: 16, weight: .medium)
            )
            .padding()
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
            
            // 短文本不滚动
            MarqueeTextView(
                text: "短标题",
                width: 200,
                font: .system(size: 16, weight: .medium)
            )
            .padding()
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
            
            // 快速滚动
            MarqueeTextView.fast(
                text: "快速滚动的音频标题示例文本",
                width: 150,
                font: .system(size: 14)
            )
            .padding()
            .background(Color.blue.opacity(0.2))
            .cornerRadius(8)
            
            // 慢速滚动
            MarqueeTextView.slow(
                text: "慢速滚动的音频标题示例文本",
                width: 150,
                font: .system(size: 14)
            )
            .padding()
            .background(Color.green.opacity(0.2))
            .cornerRadius(8)
        }
    }
    .padding()
}

#Preview("跑马灯文字 - 不同宽度") {
    VStack(spacing: 20) {
        Text("不同容器宽度效果")
            .font(.headline)
        
        let sampleText = "示例音频标题 - 艺术家名称"
        
        VStack(spacing: 15) {
            ForEach([100, 150, 200, 250], id: \.self) { width in
                VStack(alignment: .leading, spacing: 5) {
                    Text("宽度: \(width)pt")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    MarqueeTextView(
                        text: sampleText,
                        width: CGFloat(width),
                        font: .system(size: 14, weight: .medium)
                    )
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
                }
            }
        }
    }
    .padding()
}
