import SwiftUI

/// 收藏地点列表弹窗
///
/// 功能特性：
/// - 显示所有收藏的地点
/// - 支持多选操作
/// - 搜索功能
/// - 现代化弹窗设计
struct FavoriteAttractionsSheet: View {
    
    // MARK: - Properties

    @Binding var favoriteAttractions: [AttractionModel]
    @Binding var selectedAttractions: [AttractionModel]
    @Binding var isPresented: Bool
    let onAttractionsConfirmed: (([AttractionModel]) -> Void)?
    
    // MARK: - State

    @State private var searchText: String = ""
    @State private var temporarySelectedAttractions: [AttractionModel] = []

    // MARK: - Computed Properties

    /// 临时选择景点的ID集合（用于快速查找）
    private var temporarySelectedAttractionIds: Set<String> {
        Set(temporarySelectedAttractions.map { $0.id })
    }

    /// 已选择景点的ID集合（用于显示已添加状态）
    private var selectedAttractionIds: Set<String> {
        Set(selectedAttractions.map { $0.id })
    }

    /// 过滤后的收藏景点列表（高性能优化）
    private var filteredAttractions: [AttractionModel] {
        if searchText.isEmpty {
            return favoriteAttractions
        } else {
            // 使用更高效的搜索算法
            let lowercaseSearchText = searchText.lowercased()
            return favoriteAttractions.filter { attraction in
                attraction.name.lowercased().contains(lowercaseSearchText) ||
                (attraction.description?.lowercased().contains(lowercaseSearchText) ?? false)
            }
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // 搜索栏
                if !favoriteAttractions.isEmpty {
                    searchSection
                }
                
                // 收藏地点列表
                favoritesList
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("收藏的地点")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        isPresented = false
                    }
                    .foregroundColor(.blue)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确认(\(temporarySelectedAttractions.count))") {
                        confirmSelection()
                    }
                    .foregroundColor(temporarySelectedAttractions.isEmpty ? .gray : .blue)
                    .fontWeight(.semibold)
                    .disabled(temporarySelectedAttractions.isEmpty)
                }
            }
        }
        .onAppear {
            // 初始化临时选择状态
            temporarySelectedAttractions = selectedAttractions

            // 大幅延迟预加载，确保弹窗动画完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.preloadInitialImages()
            }
        }
    }
    
    // MARK: - Private Views
    
    /// 搜索区域
    private var searchSection: some View {
        VStack(spacing: 0) {
            HStack(spacing: 16) {
                Image(systemName: "magnifyingglass")
                    .font(.title3)
                    .foregroundColor(.gray)
                
            TextField("搜索收藏的地点", text: $searchText)
                    .font(.body)
                    .debounced(text: $searchText, delay: 0.3) { query in
                        // 这里可以添加搜索逻辑，目前使用计算属性已足够
                    }
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title3)
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(16)
            .background(Color(.systemBackground))
            
            Divider()
        }
    }
    
    /// 收藏地点列表
    private var favoritesList: some View {
        Group {
            if favoriteAttractions.isEmpty {
                emptyFavoritesView
            } else {
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(Array(filteredAttractions.enumerated()), id: \.element.id) { index, attraction in
                            FavoriteAttractionDetailRow(
                                attraction: attraction,
                                isSelected: temporarySelectedAttractionIds.contains(attraction.id),
                                onTap: {
                                    // 使用异步操作，确保点击响应不被阻塞
                                    Task { @MainActor in
                                        toggleTemporarySelection(attraction)
                                    }
                                }
                            )
                            .id(attraction.id)
                            .onAppear {
                                // 智能预加载：当用户滚动到某个位置时，预加载后续图片
                                preloadUpcomingImages(currentIndex: index)
                            }
                        }
                    }
                    .background(Color(.systemBackground))
                    .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                }
            }
        }
    }
    
    /// 空收藏状态视图
    private var emptyFavoritesView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            VStack(spacing: 16) {
                Image(systemName: "heart")
                    .font(.system(size: 60))
                    .foregroundColor(.gray.opacity(0.4))
                
                VStack(spacing: 8) {
                    Text("暂无收藏的地点")
                        .font(.title3.weight(.medium))
                        .foregroundColor(.primary)
                    
                    Text("在景点详情页点击收藏按钮\n即可将喜欢的地点添加到这里")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineSpacing(2)
                }
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    

    
    // MARK: - Private Methods
    
    /// 切换临时选择状态（超高性能优化版本 - 确保UI响应性）
    private func toggleTemporarySelection(_ attraction: AttractionModel) {
        // 预先检查是否已选择，避免重复计算
        let isCurrentlySelected = temporarySelectedAttractionIds.contains(attraction.id)

        // 立即提供触觉反馈，不等待动画
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        if isCurrentlySelected {
            // 移除选择 - 使用更快的动画
            withAnimation(.easeInOut(duration: 0.1)) {
                temporarySelectedAttractions.removeAll { $0.id == attraction.id }
            }
        } else {
            // 添加选择 - 使用更快的动画
            withAnimation(.easeInOut(duration: 0.1)) {
                temporarySelectedAttractions.append(attraction)
            }
        }
    }

    /// 确认添加选择的景点
    private func confirmSelection() {
        if !temporarySelectedAttractions.isEmpty {
            onAttractionsConfirmed?(temporarySelectedAttractions)
            // 清空临时选择
            temporarySelectedAttractions.removeAll()
            // 关闭弹窗
            isPresented = false
        }
    }

    /// 预加载前几张图片，提升用户体验（延迟执行，不阻塞UI）
    private func preloadInitialImages() {
        // 获取前3个景点的图片URL进行预加载（进一步减少数量）
        let imageUrls = self.favoriteAttractions.prefix(3).compactMap { $0.imageUrl }

        if !imageUrls.isEmpty {
            print("🖼️ 延迟预加载 \(imageUrls.count) 张收藏景点图片")
            // 使用Task包装actor调用，避免Swift 6警告
            Task {
                await ImagePreloadService.shared.preloadImages(urls: imageUrls, priority: .background)
            }
        }
    }

    /// 预加载可见区域的图片
    private func preloadVisibleImages() {
        // 预加载前5张图片，进一步减少数量避免卡顿
        let imageUrls = filteredAttractions.prefix(5).compactMap { $0.imageUrl }

        if !imageUrls.isEmpty {
            print("👁️ 预加载可见区域 \(imageUrls.count) 张收藏景点图片")
            // 使用Task包装actor调用，避免Swift 6警告
            Task {
                await ImagePreloadService.shared.preloadImages(urls: imageUrls, priority: .userInitiated)
            }
        }
    }

    /// 智能预加载即将出现的图片（低优先级，不阻塞滚动）
    private func preloadUpcomingImages(currentIndex: Int) {
        // 当滚动到某个位置时，预加载后续1张图片（最小化数量）
        let startIndex = currentIndex + 1
        let endIndex = min(startIndex + 1, filteredAttractions.count)

        if startIndex < filteredAttractions.count {
            let upcomingAttractions = Array(filteredAttractions[startIndex..<endIndex])
            let imageUrls = upcomingAttractions.compactMap { $0.imageUrl }

            if !imageUrls.isEmpty {
                // 使用Task包装actor调用，避免Swift 6警告
                Task {
                    await ImagePreloadService.shared.preloadImages(urls: imageUrls, priority: .background)
                }
            }
        }
    }
}

/// 收藏地点详细行组件（用于弹窗列表）
private struct FavoriteAttractionDetailRow: View {
    let attraction: AttractionModel
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 景点图片
                CachedAsyncImage(
                    url: URL(string: attraction.imageUrl ?? "")
                ) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.2))
                        .overlay(
                            Image(systemName: attractionTypeIcon)
                                .font(.title3)
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 50, height: 50)
                .clipShape(RoundedRectangle(cornerRadius: 8))

                // 景点信息
                VStack(alignment: .leading, spacing: 6) {
                    // 景点名称
                    Text(attraction.name)
                        .font(.headline.weight(.medium))
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    // 地址信息
                    Text(attraction.fullAddress)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)

                    // 收藏时间和评分
                    HStack(spacing: 12) {
                        // 收藏时间
                        if let favoriteTime = attraction.formattedFavoriteDate {
                            HStack(spacing: 4) {
                                Image(systemName: "heart.fill")
                                    .font(.caption2)
                                    .foregroundColor(.red.opacity(0.7))

                                Text(favoriteTime)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        // 评分
                        if let rating = attraction.formattedRating {
                            HStack(spacing: 4) {
                                Image(systemName: "star.fill")
                                    .font(.caption2)
                                    .foregroundColor(.orange)

                                Text(rating)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        Spacer()
                    }
                }

                Spacer()

                // 选择指示器
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title3)
                    .foregroundColor(isSelected ? .blue : .gray.opacity(0.5))
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 根据景点类型返回对应图标
    private var attractionTypeIcon: String {
        switch attraction.type {
        case "自然风光":
            return "mountain.2"
        case "历史文化":
            return "building.columns"
        case "地标建筑":
            return "building.2"
        case "主题乐园":
            return "gamecontroller"
        default:
            return "mappin"
        }
    }
}

// MARK: - Preview

#Preview {
    FavoriteAttractionsSheet(
        favoriteAttractions: .constant(AttractionModel.samples),
        selectedAttractions: .constant([]),
        isPresented: .constant(true),
        onAttractionsConfirmed: { attractions in
            print("确认添加景点: \(attractions.map { $0.name }.joined(separator: ", "))")
        }
    )
}

#Preview("Empty State") {
    FavoriteAttractionsSheet(
        favoriteAttractions: .constant([]),
        selectedAttractions: .constant([]),
        isPresented: .constant(true),
        onAttractionsConfirmed: { attractions in
            print("确认添加景点: \(attractions.map { $0.name }.joined(separator: ", "))")
        }
    )
}
