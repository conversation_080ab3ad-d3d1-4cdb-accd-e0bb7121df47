import SwiftUI

/// 我的作品页面的空状态视图
/// 简洁通用设计，适配多种空状态场景
struct MyWorksEmptyView: View {
    let selectedTag: String?

    var body: some View {
        VStack(spacing: 24) {
            // 图标区域
            ZStack {
                // 背景装饰圆圈
                Circle()
                    .fill(Color.secondary.opacity(0.08))
                    .frame(width: 100, height: 100)

                // 主图标
                Image(systemName: emptyStateIcon)
                    .font(.system(size: 40, weight: .light))
                    .foregroundColor(.secondary.opacity(0.7))
            }

            // 文字内容
            VStack(spacing: 8) {
                Text(emptyStateTitle)
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(emptyStateDescription)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }

    // MARK: - 计算属性
    private var emptyStateIcon: String {
        if selectedTag == nil {
            return "tray"
        } else {
            return "magnifyingglass"
        }
    }

    private var emptyStateTitle: String {
        if selectedTag == nil {
            return "暂无内容"
        } else {
            return "暂无相关内容"
        }
    }

    private var emptyStateDescription: String {
        if selectedTag == nil {
            return "这里还没有任何内容"
        } else {
            return "没有找到与「\(selectedTag ?? "")」相关的内容"
        }
    }
}

// MARK: - 预览
#Preview("无作品状态") {
    MyWorksEmptyView(selectedTag: nil)
        .background(Color(.systemGroupedBackground))
}

#Preview("筛选无结果状态") {
    MyWorksEmptyView(selectedTag: "美食探店")
        .background(Color(.systemGroupedBackground))
}