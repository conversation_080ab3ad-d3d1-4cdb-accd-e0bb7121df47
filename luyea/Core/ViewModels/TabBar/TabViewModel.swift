import SwiftUI

@MainActor
final class TabViewModel: ObservableObject {
    @Published private(set) var loadingStates: [AppTab: Bool] = [:]
    @Published private(set) var errorStates: [AppTab: Error?] = [:]
    
    private var dataLoaders: [AppTab: () async throws -> Void] = [:]
    
    init() {
        setupDataLoaders()
    }
    
    private func setupDataLoaders() {
        dataLoaders = [
            .itineraryPlan: loadItineraryData,
            .exploreJourney: loadDiscoverData,
            .profileCenter: loadProfile
        ]
    }
    
    func loadData(for tab: AppTab) {
        guard let loader = dataLoaders[tab] else { return }
        
        Task {
            do {
                loadingStates[tab] = true
                errorStates[tab] = nil
                try await loader()
            } catch {
                errorStates[tab] = error
            }
            loadingStates[tab] = false
        }
    }
    
    private func loadItineraryData() async throws {
        try await Task.sleep(nanoseconds: 1_000_000_000)
    }
    
    private func loadDiscoverData() async throws {
        try await Task.sleep(nanoseconds: 1_000_000_000)
    }
    
    private func loadProfile() async throws {
        try await Task.sleep(nanoseconds: 1_000_000_000)
    }
} 
