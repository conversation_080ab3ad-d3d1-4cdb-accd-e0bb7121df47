import SwiftUI

/// 自定义日期选择器视图
///
/// 提供年月日分段选择功能，支持动态天数计算和日期验证。
/// 采用轮盘式选择器，提供直观的日期选择体验。
struct CustomDateSelectorView: View {
    
    // MARK: - Properties
    
    @Binding var selectedDate: Date
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题区域
            titleSection
            
            // 日期选择器内容
            datePickerContent
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
    }
    
    // MARK: - Private Views
    
    /// 标题区域
    private var titleSection: some View {
        HStack {
            HStack(spacing: 8) {
                Image(systemName: "calendar.badge.plus")
                    .font(.title3)
                    .foregroundColor(.green)
                
                Text("自定义日期")
                    .font(.title2.weight(.semibold))
                    .foregroundColor(.primary)
            }
            
            Spacer()
            
            // 装饰性元素
            HStack(spacing: 4) {
                ForEach(0..<3, id: \.self) { _ in
                    Circle()
                        .fill(Color.green.opacity(0.3))
                        .frame(width: 6, height: 6)
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 24)
        .padding(.bottom, 20)
    }
    
    /// 日期选择器内容
    private var datePickerContent: some View {
        VStack(spacing: 20) {
            // 当前选择的日期显示
            selectedDateDisplay
            
            // 年月日选择器
            dateComponentPickers
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 24)
    }
    
    /// 选择的日期显示
    private var selectedDateDisplay: some View {
        HStack {
            Image(systemName: "calendar.circle.fill")
                .font(.title3)
                .foregroundColor(.green)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("选择的日期")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(DateUtils.formatDisplayDate(selectedDate))
                    .font(.headline.weight(.semibold))
                    .foregroundColor(.primary)
            }
            
            Spacer()
            
            // 重置按钮
            Button(action: {
                selectedDate = Date()
            }) {
                Image(systemName: "arrow.clockwise.circle.fill")
                    .font(.title3)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
    
    /// 年月日选择器
    private var dateComponentPickers: some View {
        HStack(spacing: 8) {
            // 年份选择
            DateComponentPicker(
                title: "年",
                selection: Binding(
                    get: { Calendar.current.component(.year, from: selectedDate) },
                    set: { newYear in
                        selectedDate = DateUtils.updateDateComponent(selectedDate, component: .year, value: newYear)
                    }
                ),
                options: DateUtils.getAvailableYears()
            )
            
            // 月份选择
            DateComponentPicker(
                title: "月",
                selection: Binding(
                    get: { Calendar.current.component(.month, from: selectedDate) },
                    set: { newMonth in
                        selectedDate = DateUtils.updateDateComponent(selectedDate, component: .month, value: newMonth)
                    }
                ),
                options: Array(1...12)
            )
            
            // 日期选择
            DateComponentPicker(
                title: "日",
                selection: Binding(
                    get: { Calendar.current.component(.day, from: selectedDate) },
                    set: { newDay in
                        selectedDate = DateUtils.updateDateComponent(selectedDate, component: .day, value: newDay)
                    }
                ),
                options: availableDays
            )
        }
        .frame(height: 120)
    }
    
    // MARK: - Computed Properties
    
    /// 可选择的日期范围（根据当前选择的年月动态计算）
    private var availableDays: [Int] {
        let calendar = Calendar.current
        let year = calendar.component(.year, from: selectedDate)
        let month = calendar.component(.month, from: selectedDate)
        return DateUtils.getAvailableDays(for: year, month: month)
    }
}

// MARK: - DateComponentPicker

/// 日期组件选择器
private struct DateComponentPicker: View {
    let title: String
    @Binding var selection: Int
    let options: [Int]
    
    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption.weight(.semibold))
                .foregroundColor(.primary)
            
            Picker(title, selection: $selection) {
                ForEach(options, id: \.self) { value in
                    Text("\(value)")
                        .tag(value)
                }
            }
            .pickerStyle(.wheel)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
        }
    }
}

// MARK: - Preview

#Preview {
    CustomDateSelectorView(
        selectedDate: .constant(Date())
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
