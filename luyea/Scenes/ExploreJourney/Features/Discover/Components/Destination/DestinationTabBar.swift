import SwiftUI

struct DestinationTabBar: View {
    let tabs: [String]
    @Binding var selectedTab: Int

    var body: some View {
        HStack(spacing: 24) {
            ForEach(tabs.indices, id: \.self) { idx in
                Button(action: { selectedTab = idx }) {
                    VStack(spacing: 2) {
                        Text(tabs[idx])
                            .font(.system(size: 15, weight: selectedTab == idx ? .bold : .regular))
                            .foregroundColor(selectedTab == idx ? .primary : .gray)
                        Rectangle()
                            .frame(height: 2)
                            .foregroundColor(selectedTab == idx ? .accentColor : .clear)
                            .cornerRadius(1)
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
} 