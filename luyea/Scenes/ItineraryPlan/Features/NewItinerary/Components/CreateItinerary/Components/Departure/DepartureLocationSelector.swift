import SwiftUI
import CoreLocation

/// 出发地点选择器
///
/// 功能特性：
/// - 搜索框输入地点
/// - 当前位置自动检测
/// - 地理位置选择
/// - 历史地点记录
struct DepartureLocationSelector: View {
    
    // MARK: - Properties
    
    @Binding var selectedLocation: String
    @Binding var showLocationPicker: Bool
    
    // MARK: - State
    
    @State private var searchText: String = ""
    @State private var currentLocation: String = "正在获取当前位置..."
    @State private var isLoadingLocation = true
    @ObservedObject private var locationManager = LocationManager.shared
    
    // MARK: - Body
    
    var body: some View {
        Button(action: { showLocationPicker = true }) {
            HStack(spacing: 12) {
                // 位置图标
                Image(systemName: selectedLocation.isEmpty ? "location.circle" : "location.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(selectedLocation.isEmpty ? .gray : .blue)
                    .frame(width: 20, height: 20)

                VStack(alignment: .leading, spacing: 4) {
                    Text("出发地点")
                        .font(.subheadline.weight(.medium))
                        .foregroundColor(.primary)

                    Text(displayText)
                        .font(.subheadline)
                        .foregroundColor(selectedLocation.isEmpty ? .secondary : .blue)
                        .fontWeight(selectedLocation.isEmpty ? .regular : .medium)
                }

                Spacer()

                if !selectedLocation.isEmpty {
                    Button(action: {
                        selectedLocation = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.gray)
                            .frame(width: 20, height: 20)
                    }
                    .onTapGesture {
                        selectedLocation = ""
                    }
                }

                Image(systemName: "chevron.right")
                    .font(.caption.weight(.semibold))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showLocationPicker) {
            DepartureLocationPickerSheet(
                selectedLocation: $selectedLocation,
                isPresented: $showLocationPicker,
                currentLocation: currentLocation
            )
        }
        .onAppear {
            requestLocationPermission()
        }
        .onReceive(locationManager.$location) { location in
            if let location = location {
                updateLocationInfo(from: location)
            }
        }
    }
    
    // MARK: - Private Properties
    
    /// 显示文本
    private var displayText: String {
        if !selectedLocation.isEmpty {
            return selectedLocation
        } else if isLoadingLocation {
            return "正在获取当前位置..."
        } else {
            return "点击选择出发地点"
        }
    }
    
    // MARK: - Private Methods
    
    /// 请求位置权限
    private func requestLocationPermission() {
        locationManager.requestLocationPermission()

        // 监听位置变化
        if let location = locationManager.location {
            updateLocationInfo(from: location)
        }
    }

    /// 更新位置信息
    private func updateLocationInfo(from location: CLLocation) {
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [self] placemarks, error in
            DispatchQueue.main.async {
                if let placemark = placemarks?.first {
                    let city = placemark.locality ?? placemark.administrativeArea ?? "未知位置"
                    self.currentLocation = city
                    self.isLoadingLocation = false

                    // 如果用户还没有选择出发地点，自动设置为当前位置
                    if self.selectedLocation.isEmpty {
                        self.selectedLocation = city
                    }
                } else {
                    self.currentLocation = "无法获取位置信息"
                    self.isLoadingLocation = false
                }
            }
        }
    }
}

// MARK: - Preview

#Preview {
    DepartureLocationSelector(
        selectedLocation: .constant(""),
        showLocationPicker: .constant(false)
    )
    .padding()
    .background(Color(.systemGroupedBackground))
}
