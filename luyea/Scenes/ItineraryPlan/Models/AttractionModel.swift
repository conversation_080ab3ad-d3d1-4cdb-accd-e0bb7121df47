import Foundation

/// 景点数据模型
///
/// 用于表示搜索到的景点信息，包含景点名称、所在城市、类型等信息。
struct AttractionModel: Identifiable, Codable, Equatable {
    // MARK: - Properties
    
    /// 景点唯一标识符
    let id: String
    
    /// 景点名称
    let name: String
    
    /// 景点所在城市
    let city: String
    
    /// 景点所在省份/地区
    let province: String
    
    /// 景点所在国家
    let country: String
    
    /// 景点类型（如：自然风光、历史文化、主题乐园等）
    let type: String
    
    /// 景点描述
    let description: String?
    
    /// 景点图片URL
    let imageUrl: String?
    
    /// 景点评分（1-5分）
    let rating: Double?
    
    /// 景点标签
    let tags: [String]
    
    /// 景点坐标
    let coordinate: Coordinate?

    /// 收藏时间（可选，用于收藏地点）
    let favoriteDate: Date?

    // MARK: - Computed Properties
    
    /// 完整的地址描述
    var fullAddress: String {
        if country == "中国" {
            return "\(province) \(city)"
        } else {
            return "\(city), \(country)"
        }
    }
    
    /// 格式化的评分文本
    var formattedRating: String? {
        guard let rating = rating else { return nil }
        return String(format: "%.1f", rating)
    }

    /// 格式化的收藏时间文本
    var formattedFavoriteDate: String? {
        guard let favoriteDate = favoriteDate else { return nil }

        let now = Date()
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day, .hour, .minute], from: favoriteDate, to: now)

        if let days = components.day, days > 0 {
            if days == 1 {
                return "昨天收藏"
            } else if days < 7 {
                return "\(days)天前收藏"
            } else if days < 30 {
                let weeks = days / 7
                return "\(weeks)周前收藏"
            } else if days < 365 {
                let months = days / 30
                return "\(months)个月前收藏"
            } else {
                let years = days / 365
                return "\(years)年前收藏"
            }
        } else if let hours = components.hour, hours > 0 {
            return "\(hours)小时前收藏"
        } else if let minutes = components.minute, minutes > 0 {
            return "\(minutes)分钟前收藏"
        } else {
            return "刚刚收藏"
        }
    }
}

// MARK: - 坐标模型已统一
// 坐标相关定义已迁移到 Core/Models/Coordinate.swift
// 使用统一的 Coordinate 结构体

// MARK: - Sample Data

extension AttractionModel {
    /// 示例景点数据
    static let samples: [AttractionModel] = [
        AttractionModel(
            id: "1",
            name: "西湖",
            city: "杭州",
            province: "浙江省",
            country: "中国",
            type: "自然风光",
            description: "杭州西湖，以秀丽的湖光山色和众多的名胜古迹闻名中外",
            imageUrl: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
            rating: 4.8,
            tags: ["湖泊", "风景", "历史"],
            coordinate: Coordinate(latitude: 30.2741, longitude: 120.1551),
            favoriteDate: Calendar.current.date(byAdding: .day, value: -3, to: Date())
        ),
        AttractionModel(
            id: "2",
            name: "东京塔",
            city: "东京",
            province: "关东地区",
            country: "日本",
            type: "地标建筑",
            description: "东京的标志性建筑，可俯瞰整个东京市区",
            imageUrl: "https://images.unsplash.com/photo-1465101046530-73398c7f28ca",
            rating: 4.5,
            tags: ["地标", "观景", "城市"],
            coordinate: Coordinate(latitude: 35.6586, longitude: 139.7454),
            favoriteDate: Calendar.current.date(byAdding: .hour, value: -6, to: Date())
        ),
        AttractionModel(
            id: "3",
            name: "埃菲尔铁塔",
            city: "巴黎",
            province: "法兰西岛大区",
            country: "法国",
            type: "地标建筑",
            description: "巴黎的象征，世界著名的建筑和旅游胜地",
            imageUrl: "https://images.unsplash.com/photo-1502602898657-3e91760cbb34",
            rating: 4.7,
            tags: ["地标", "历史", "浪漫"],
            coordinate: Coordinate(latitude: 48.8584, longitude: 2.2945),
            favoriteDate: Calendar.current.date(byAdding: .day, value: -1, to: Date())
        ),
        AttractionModel(
            id: "4",
            name: "天山天池",
            city: "乌鲁木齐",
            province: "新疆维吾尔自治区",
            country: "中国",
            type: "自然风光",
            description: "新疆著名的高山湖泊，被誉为天山明珠",
            imageUrl: "https://images.unsplash.com/photo-1500534314209-a25ddb2bd429",
            rating: 4.6,
            tags: ["湖泊", "山景", "自然"],
            coordinate: Coordinate(latitude: 43.8841, longitude: 88.1253),
            favoriteDate: Calendar.current.date(byAdding: .day, value: -7, to: Date())
        ),
        AttractionModel(
            id: "5",
            name: "故宫博物院",
            city: "北京",
            province: "北京市",
            country: "中国",
            type: "历史文化",
            description: "中国明清两代的皇家宫殿，世界文化遗产",
            imageUrl: "https://images.unsplash.com/photo-1508804185872-d7badad00f7d",
            rating: 4.9,
            tags: ["历史", "文化", "宫殿"],
            coordinate: Coordinate(latitude: 39.9163, longitude: 116.3972),
            favoriteDate: Calendar.current.date(byAdding: .day, value: -14, to: Date())
        )
    ]
}
