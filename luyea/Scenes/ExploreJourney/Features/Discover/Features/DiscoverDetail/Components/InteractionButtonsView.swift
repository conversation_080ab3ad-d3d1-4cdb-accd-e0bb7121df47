import SwiftUI

struct InteractionButtonsView: View {
    @Binding var isLiked: Bool
    let likeCount: Int
    let commentCount: Int
    let shareCount: Int
    let onLike: () -> Void
    let onComment: () -> Void
    let onShare: () -> Void
    
    var body: some View {
        HStack(spacing: 24) {
            // 点赞按钮
            InteractionButton(
                icon: isLiked ? "heart.fill" : "heart",
                count: likeCount,
                isActive: isLiked,
                activeColor: .red,
                action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isLiked.toggle()
                    }
                    onLike()
                }
            )
            
            // 评论按钮
            InteractionButton(
                icon: "message",
                count: commentCount,
                isActive: false,
                activeColor: .blue,
                action: onComment
            )
            
            // 分享按钮
            InteractionButton(
                icon: "square.and.arrow.up",
                count: shareCount,
                isActive: false,
                activeColor: .green,
                action: onShare
            )
            
            Spacer()
        }
        .padding(.horizontal, 16)
    }
}

struct InteractionButton: View {
    let icon: String
    let count: Int
    let isActive: Bool
    let activeColor: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 18))
                    .foregroundColor(isActive ? activeColor : .secondary)
                
                if count > 0 {
                    Text("\(count)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        InteractionButtonsView(
            isLiked: .constant(false),
            likeCount: 128,
            commentCount: 45,
            shareCount: 23,
            onLike: {},
            onComment: {},
            onShare: {}
        )
        
        InteractionButtonsView(
            isLiked: .constant(true),
            likeCount: 129,
            commentCount: 45,
            shareCount: 23,
            onLike: {},
            onComment: {},
            onShare: {}
        )
    }
    .padding()
}
