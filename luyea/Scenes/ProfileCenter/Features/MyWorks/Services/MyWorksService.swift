import Foundation
import Combine

/// 我的作品服务协议
protocol MyWorksServiceProtocol {
    /// 获取作品统计信息
    func fetchWorksStats() async throws -> MyWorksModels.WorksStats

    /// 获取话题统计信息
    func fetchTopicStats() async throws -> [MyWorksModels.TopicStats]

    /// 获取分页作品数据
    func fetchWorks(page: Int, pageSize: Int, status: MyWorksModels.WorkStatus?, topic: String?, keyword: String?, sortBy: String?, order: String?) async throws -> MyWorksModels.WorksPageResponse

    /// 刷新作品数据
    func refreshWorks(status: MyWorksModels.WorkStatus?, topic: String?, keyword: String?, sortBy: String?, order: String?) async throws -> MyWorksModels.WorksPageResponse

    /// 删除作品
    func deleteWork(_ workId: String) async throws

    /// 更新作品状态
    func updateWorkStatus(_ workId: String, status: MyWorksModels.WorkStatus) async throws

    /// 合并分页数据
    func mergeWorksData(existing: [MyWorksModels.WorkItem], newPage: MyWorksModels.WorksPageResponse, isRefresh: Bool) -> [MyWorksModels.WorkItem]

    /// 验证分页参数
    func validatePaginationParams(page: Int, pageSize: Int) -> Bool

    /// 计算加载更多的阈值
    func shouldLoadMore(currentIndex: Int, totalItems: Int, threshold: Int) -> Bool
}

/// 我的作品服务实现
final class MyWorksService: MyWorksServiceProtocol {
    
    // MARK: - Dependencies
    
    /// 网络服务
    private let networkService: NetworkServiceProtocol
    
    // MARK: - Cache Properties
    
    /// 作品统计缓存
    private var worksStatsCache: MyWorksModels.WorksStats?
    
    /// 话题统计缓存
    private var topicStatsCache: [MyWorksModels.TopicStats] = []
    
    /// 分页数据缓存 - 按筛选条件分组
    private var worksCache: [String: MyWorksModels.WorksPageResponse] = [:]
    
    /// 缓存时间戳
    private var cacheTimestamps: [String: Date] = [:]
    
    /// 缓存有效期（秒）
    private let cacheValidityDuration: TimeInterval = 180 // 3分钟
    
    // MARK: - Initialization
    
    init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }
    
    // MARK: - Public Methods
    
    /// 获取作品统计信息
    func fetchWorksStats() async throws -> MyWorksModels.WorksStats {
        // 检查缓存
        if let cachedStats = getCachedWorksStats() {
            return cachedStats
        }
        
        // 从网络获取数据
        let response: MyWorksModels.WorksStatsResponse = try await networkService.request(
            .get(APIPaths.myWorksStats)
        )
        
        // 更新缓存
        updateWorksStatsCache(response.stats)
        
        return response.stats
    }
    
    /// 获取话题统计信息
    func fetchTopicStats() async throws -> [MyWorksModels.TopicStats] {
        // 检查缓存
        if let cachedTopics = getCachedTopicStats() {
            return cachedTopics
        }
        
        // 从网络获取数据
        let topics: [MyWorksModels.TopicStats] = try await networkService.request(
            .get(APIPaths.myWorksTopics)
        )
        
        // 更新缓存
        updateTopicStatsCache(topics)
        
        return topics
    }
    
    /// 获取分页作品数据
    func fetchWorks(page: Int, pageSize: Int, status: MyWorksModels.WorkStatus?, topic: String?, keyword: String?, sortBy: String?, order: String?) async throws -> MyWorksModels.WorksPageResponse {
        let cacheKey = buildCacheKey(status: status, topic: topic, keyword: keyword, page: page, sortBy: sortBy, order: order)

        // 检查缓存（仅对第一页进行缓存）
        if page == 1, let cachedData = getCachedWorks(for: cacheKey) {
            return cachedData
        }

        // 构建API请求
        var api = APIRequest
            .get(APIPaths.myWorksList)
            .query("page", String(page))
            .query("pageSize", String(pageSize))

        // 添加筛选参数
        if let status = status {
            api = api.query("status", status.rawValue)
        }

        if let topic = topic, !topic.isEmpty {
            api = api.query("topic", topic)
        }

        if let keyword = keyword, !keyword.isEmpty {
            api = api.query("keyword", keyword)
        }

        // 添加排序参数
        if let sortBy = sortBy, !sortBy.isEmpty {
            api = api.query("sortBy", sortBy)
        }

        if let order = order, !order.isEmpty {
            api = api.query("order", order)
        }

        // 执行请求
        let response: MyWorksModels.WorksPageResponse = try await networkService.request(api)

        // 缓存第一页数据
        if page == 1 {
            updateWorksCache(response, for: cacheKey)
        }

        return response
    }
    
    /// 刷新作品数据
    func refreshWorks(status: MyWorksModels.WorkStatus?, topic: String?, keyword: String?, sortBy: String?, order: String?) async throws -> MyWorksModels.WorksPageResponse {
        // 清除相关缓存
        clearWorksCache(for: status, topic: topic, keyword: keyword, sortBy: sortBy, order: order)

        // 获取第一页数据
        return try await fetchWorks(page: 1, pageSize: 10, status: status, topic: topic, keyword: keyword, sortBy: sortBy, order: order)
    }
    
    /// 删除作品
    func deleteWork(_ workId: String) async throws {
        let request = APIRequest.delete(APIPaths.myWorksList + "/\(workId)")

        let _: EmptyResponse = try await networkService.request(request)

        // 清除所有缓存，因为删除操作会影响统计和列表
        clearAllCache()
    }

    /// 更新作品状态
    func updateWorkStatus(_ workId: String, status: MyWorksModels.WorkStatus) async throws {
        let statusData = try JSONEncoder().encode(["status": status.rawValue])
        let request = APIRequest
            .put(APIPaths.myWorksList + "/\(workId)/status")
            .body(statusData)

        let _: EmptyResponse = try await networkService.request(request)

        // 清除所有缓存，因为状态更新会影响统计和列表
        clearAllCache()
    }
    
    // MARK: - Private Methods - Cache Management
    
    /// 构建缓存键
    private func buildCacheKey(status: MyWorksModels.WorkStatus?, topic: String?, keyword: String?, page: Int, sortBy: String?, order: String?) -> String {
        let statusKey = status?.rawValue ?? "all"
        let topicKey = topic ?? "all"
        let keywordKey = keyword ?? "all"
        let sortByKey = sortBy ?? "default"
        let orderKey = order ?? "desc"
        return "\(statusKey)_\(topicKey)_\(keywordKey)_\(sortByKey)_\(orderKey)_page_\(page)"
    }
    
    /// 获取缓存的作品统计
    private func getCachedWorksStats() -> MyWorksModels.WorksStats? {
        guard isCacheValid(for: "stats") else { return nil }
        return worksStatsCache
    }
    
    /// 获取缓存的话题统计
    private func getCachedTopicStats() -> [MyWorksModels.TopicStats]? {
        guard isCacheValid(for: "topics") else { return nil }
        return topicStatsCache.isEmpty ? nil : topicStatsCache
    }
    
    /// 获取缓存的作品数据
    private func getCachedWorks(for key: String) -> MyWorksModels.WorksPageResponse? {
        guard isCacheValid(for: key) else { return nil }
        return worksCache[key]
    }
    
    /// 检查缓存是否有效
    private func isCacheValid(for key: String) -> Bool {
        guard let timestamp = cacheTimestamps[key] else { return false }
        return Date().timeIntervalSince(timestamp) < cacheValidityDuration
    }
    
    /// 更新作品统计缓存
    private func updateWorksStatsCache(_ stats: MyWorksModels.WorksStats) {
        worksStatsCache = stats
        cacheTimestamps["stats"] = Date()
    }
    
    /// 更新话题统计缓存
    private func updateTopicStatsCache(_ topics: [MyWorksModels.TopicStats]) {
        topicStatsCache = topics
        cacheTimestamps["topics"] = Date()
    }
    
    /// 更新作品缓存
    private func updateWorksCache(_ response: MyWorksModels.WorksPageResponse, for key: String) {
        worksCache[key] = response
        cacheTimestamps[key] = Date()
    }
    
    /// 清除指定条件的作品缓存
    private func clearWorksCache(for status: MyWorksModels.WorkStatus?, topic: String?, keyword: String?, sortBy: String?, order: String?) {
        let statusKey = status?.rawValue ?? "all"
        let topicKey = topic ?? "all"
        let keywordKey = keyword ?? "all"
        let sortByKey = sortBy ?? "default"
        let orderKey = order ?? "desc"
        let prefix = "\(statusKey)_\(topicKey)_\(keywordKey)_\(sortByKey)_\(orderKey)"

        // 清除该条件相关的所有缓存
        let keysToRemove = worksCache.keys.filter { $0.hasPrefix(prefix) }
        for key in keysToRemove {
            worksCache.removeValue(forKey: key)
            cacheTimestamps.removeValue(forKey: key)
        }
    }
    
    /// 清除所有缓存
    private func clearAllCache() {
        worksStatsCache = nil
        topicStatsCache.removeAll()
        worksCache.removeAll()
        cacheTimestamps.removeAll()
    }
}

// MARK: - Business Logic Extensions

extension MyWorksService {
    
    /// 合并分页数据
    func mergeWorksData(existing: [MyWorksModels.WorkItem], newPage: MyWorksModels.WorksPageResponse, isRefresh: Bool) -> [MyWorksModels.WorkItem] {
        if isRefresh {
            return newPage.items
        } else {
            return existing + newPage.items
        }
    }
    
    /// 验证分页参数
    func validatePaginationParams(page: Int, pageSize: Int) -> Bool {
        return page > 0 && pageSize > 0 && pageSize <= 100
    }
    
    /// 计算加载更多的阈值
    func shouldLoadMore(currentIndex: Int, totalItems: Int, threshold: Int = 3) -> Bool {
        return currentIndex >= totalItems - threshold
    }
}

// MARK: - Supporting Types

/// 空响应类型
private struct EmptyResponse: Codable {}
