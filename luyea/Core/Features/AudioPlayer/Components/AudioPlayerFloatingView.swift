import SwiftUI

struct AudioPlayerFloatingView: View {
    @EnvironmentObject private var playerManager: AudioPlayerManager

    var body: some View {
        ZStack {
            if playerManager.isVisible {
                if playerManager.playerState == .expanded {
                    Rectangle()
                        .fill(Color.clear)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .ignoresSafeArea()
                        .contentShape(Rectangle())
                        .onTapGesture {
                            playerManager.collapsePlayer()
                        }
                        .zIndex(0)
                }

                AudioPlayerView()
                    .position(playerManager.displayPosition)
                    .animation(
                        playerManager.isDragging ? nil : .spring(response: 0.4, dampingFraction: 0.8),
                        value: playerManager.displayPosition
                    )
                    .onTapGesture {
                        if playerManager.playerState == .collapsed {
                            playerManager.expandPlayer()
                        }
                    }
                    .gesture(dragGesture)
                    .zIndex(1)
            }
        }
        .allowsHitTesting(playerManager.isVisible)
    }

    private var dragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                if !playerManager.isDragging {
                    playerManager.startDragging()
                }
                playerManager.updateDragPosition(value.translation)
            }
            .onEnded { _ in
                withAnimation(.spring(response: 0.4, dampingFraction: 0.85)) {
                    playerManager.endDragging()
                }
            }
    }
}


