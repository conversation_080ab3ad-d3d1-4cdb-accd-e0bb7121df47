import Foundation

/// 网络相关常量定义
///
/// 统一管理网络请求相关的常量，包括URL、超时时间、请求头、状态码等。
/// 与AppConfig.Network配合使用，避免重复定义。
enum NetworkConstants {
    // MARK: - 基础配置

    /// API 基础 URL，从环境配置中获取
    static var baseURL: String { EnvironmentConfig.baseURL }

    /// API 版本号
    static let apiVersion = "v1"

    /// 网络请求超时时间（秒），从AppConfig获取
    static var timeoutInterval: TimeInterval { AppConfig.Network.requestTimeout }

    // MARK: - HTTP 请求头

    /// HTTP 请求头常量
    enum Headers {
        /// Content-Type 请求头键名
        static let contentType = "Content-Type"
        /// Authorization 请求头键名
        static let authorization = "Authorization"
        /// Accept 请求头键名
        static let accept = "Accept"
        /// User-Agent 请求头键名
        static let userAgent = "User-Agent"
    }

    // MARK: - HTTP 状态码

    /// HTTP 响应状态码常量
    enum StatusCode {
        /// 请求成功
        static let success = 200
        /// 资源已创建
        static let created = 201
        /// 请求已接受
        static let accepted = 202
        /// 无内容
        static let noContent = 204
        /// 请求错误
        static let badRequest = 400
        /// 未授权
        static let unauthorized = 401
        /// 禁止访问
        static let forbidden = 403
        /// 资源未找到
        static let notFound = 404
        /// 服务器内部错误
        static let serverError = 500
        /// 服务不可用
        static let serviceUnavailable = 503
    }
}
