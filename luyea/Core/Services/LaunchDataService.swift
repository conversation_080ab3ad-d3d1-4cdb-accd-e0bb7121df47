import SwiftUI

/// 启动数据服务错误类型
///
/// 定义启动数据加载过程中可能出现的各种错误类型。
enum LaunchDataError: Error, LocalizedError {
    case loadFailed(Error)
    case saveFailed(Error)
    case invalidConfig
    case networkError(Error)
    case invalidResponse
    case fileNotFound
    case configurationCorrupted

    /// 错误描述信息
    var errorDescription: String? {
        switch self {
        case .loadFailed(let error):
            return "配置加载失败: \(error.localizedDescription)"
        case .saveFailed(let error):
            return "配置保存失败: \(error.localizedDescription)"
        case .invalidConfig:
            return "配置格式无效"
        case .networkError(let error):
            return "网络请求失败: \(error.localizedDescription)"
        case .invalidResponse:
            return "服务器响应无效"
        case .fileNotFound:
            return "配置文件未找到"
        case .configurationCorrupted:
            return "配置文件已损坏"
        }
    }
}

/// 启动数据服务协议，便于依赖注入和单元测试
protocol LaunchDataServiceProtocol: AnyObject {
    var splashConfig: SplashConfig { get }
    var isLoading: Bool { get }
    var error: LaunchDataError? { get }

    func loadRemoteConfig()
    func refreshConfig()
}

/// 启动数据服务实现类
///
/// 负责管理应用启动相关的配置数据，包括启动页配置等。
/// 支持本地缓存和远程配置同步，具备完善的错误处理和重试机制。
final class LaunchDataService: LaunchDataServiceProtocol, ObservableObject {
    /// 单例实例
    static let shared = LaunchDataService()

    /// 当前启动页配置
    @Published private(set) var splashConfig: SplashConfig

    /// 是否正在加载配置
    @Published private(set) var isLoading = false

    /// 加载过程中的错误
    @Published private(set) var error: LaunchDataError?

    /// 文件管理服务
    private let fileManager = FileManagerService.shared

    /// 网络服务
    private let networkService = NetworkService.shared

    /// 最大重试次数
    private let maxRetries = AppConfig.Network.maxRetries

    /// 是否已加载远程配置（防止重复加载）
    private var hasLoadedRemoteConfig = false

    /// 配置加载队列
    private let configQueue = DispatchQueue(label: "com.luyea.launchdata", qos: .userInitiated)

    /// 私有初始化方法，确保单例模式
    private init() {
        // 初始化默认配置
        self.splashConfig = SplashConfig.default

        // 加载本地缓存配置
        loadSavedConfig()

        // 异步加载远程配置
        loadRemoteConfig()

        Log.info("🚀 启动数据服务初始化完成")
    }

    // MARK: - Public Methods

    /// 加载远程配置（只加载一次，除非调用 refreshConfig）
    ///
    /// 异步从服务器获取最新的启动页配置，支持重试机制。
    /// 加载成功后会自动保存到本地缓存。
    func loadRemoteConfig() {
        guard !hasLoadedRemoteConfig else {
            Log.debug("🔄 远程配置已加载，跳过重复请求")
            return
        }

        hasLoadedRemoteConfig = true
        isLoading = true
        error = nil

        Log.info("🌐 开始加载远程启动配置")

        Task {
            do {
                let config = try await fetchRemoteConfigWithRetry()
                await MainActor.run {
                    self.splashConfig = config
                    self.saveConfigToLocal(config)
                    self.isLoading = false

                    // 发送配置变化通知
                    NotificationCenter.default.post(name: .splashConfigDidChange, object: nil)

                    Log.success("✅ 远程配置加载成功")
                }
            } catch {
                await MainActor.run {
                    self.error = .networkError(error)
                    self.isLoading = false
                    Log.error("❌ 远程配置加载失败: \(error)")
                }
            }
        }
    }

    /// 手动刷新配置
    ///
    /// 重置加载标志并重新从服务器获取配置。
    /// 通常在用户手动刷新或检测到配置过期时调用。
    func refreshConfig() {
        Log.info("🔄 手动刷新启动配置")
        hasLoadedRemoteConfig = false
        loadRemoteConfig()
    }

    // MARK: - Private Methods

    /// 加载本地缓存的配置
    ///
    /// 从本地文件系统读取之前保存的配置，如果读取失败则使用默认配置。
    private func loadSavedConfig() {
        configQueue.async {
            do {
                let data = try self.fileManager.readData(fileName: FileConstants.splashConfigFileName)
                let config = try JSONDecoder().decode(SplashConfig.self, from: data)

                DispatchQueue.main.async {
                    self.splashConfig = config
                    Log.info("📁 本地配置加载成功")
                }
            } catch {
                DispatchQueue.main.async {
                    self.splashConfig = SplashConfig.default
                    Log.warning("⚠️ 本地配置加载失败，使用默认配置: \(error)")
                }
            }
        }
    }

    /// 保存配置到本地文件
    ///
    /// - Parameter config: 要保存的配置对象
    private func saveConfigToLocal(_ config: SplashConfig) {
        configQueue.async {
            do {
                let encoder = JSONEncoder()
                encoder.outputFormatting = .prettyPrinted
                let data = try encoder.encode(config)
                try self.fileManager.writeData(data, fileName: FileConstants.splashConfigFileName)
                Log.debug("💾 配置已保存到本地")
            } catch {
                Log.error("❌ 配置保存失败: \(error)")
            }
        }
    }

    /// 带重试机制的远程配置获取
    ///
    /// 使用指数退避策略进行重试，最大重试次数由配置决定。
    ///
    /// - Returns: 从服务器获取的配置对象
    /// - Throws: LaunchDataError 相关错误
    private func fetchRemoteConfigWithRetry() async throws -> SplashConfig {
        var lastError: Error?

        for attempt in 1...maxRetries {
            do {
                Log.debug("🔄 尝试获取远程配置 (第\(attempt)次)")
                let config: SplashConfig = try await networkService.request(.get(APIPaths.splashConfig))

                // 验证配置有效性
                guard isValidConfig(config) else {
                    throw LaunchDataError.invalidConfig
                }

                return config

            } catch {
                lastError = error
                Log.warning("⚠️ 第\(attempt)次尝试失败: \(error)")

                // 如果不是最后一次尝试，则等待后重试
                if attempt < maxRetries {
                    let delay = pow(2.0, Double(attempt)) * AppConfig.Network.retryInterval
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }

        // 所有重试都失败，抛出最后一个错误
        throw LaunchDataError.networkError(lastError ?? NSError(domain: "UnknownError", code: -1))
    }

    /// 验证配置的有效性
    ///
    /// - Parameter config: 要验证的配置对象
    /// - Returns: 配置是否有效
    private func isValidConfig(_ config: SplashConfig) -> Bool {
        // 验证持续时间是否合理
        guard config.duration > 0 && config.duration <= 10 else {
            Log.warning("⚠️ 配置验证失败: 持续时间不合理 (\(config.duration))")
            return false
        }

        // 验证图片URL格式（如果有）
        if let imageUrl = config.imageUrl, !imageUrl.isEmpty {
            guard URL(string: imageUrl) != nil else {
                Log.warning("⚠️ 配置验证失败: 图片URL格式无效")
                return false
            }
        }

        return true
    }
}

// MARK: - Notification Names

extension Notification.Name {
    /// 启动页配置变化通知
    static let splashConfigDidChange = Notification.Name("splashConfigDidChange")
}
