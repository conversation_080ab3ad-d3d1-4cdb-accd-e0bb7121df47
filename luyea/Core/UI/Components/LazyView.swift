import SwiftUI

/// 用于延迟加载子视图的通用容器，无状态组件
///
/// 延迟视图的创建直到真正需要渲染时，提升列表和网格的性能。
/// 特别适用于复杂的视图层级和大量数据的场景。
struct LazyView<Content: View>: View {
    let build: () -> Content

    /// 初始化（支持 ViewBuilder）
    /// - Parameter build: 视图构建闭包
    init(@ViewBuilder _ build: @escaping () -> Content) {
        self.build = build
    }

    // MARK: - Body
    var body: some View {
        build()
    }
}

// MARK: - Performance Extensions

extension View {
    /// 将视图包装为懒加载视图
    /// - Returns: 懒加载包装后的视图
    func lazy() -> LazyView<Self> {
        LazyView { self }
    }

    /// 启用 Equatable 优化
    ///
    /// 当视图遵循 Equatable 协议时，SwiftUI 可以避免不必要的重绘。
    /// 这对于列表和网格中的重复视图特别有效。
    ///
    /// - Returns: 启用 Equatable 优化的视图
    func equatable() -> EquatableView<Self> where Self: Equatable {
        EquatableView(content: self)
    }

    /// 启用视图组合
    ///
    /// 将复杂的视图层级组合为单个图层，提升渲染性能。
    /// 特别适用于包含多个子视图的复杂布局。
    ///
    /// - Returns: 启用组合的视图
    func composited() -> some View {
        self.compositingGroup()
    }
}