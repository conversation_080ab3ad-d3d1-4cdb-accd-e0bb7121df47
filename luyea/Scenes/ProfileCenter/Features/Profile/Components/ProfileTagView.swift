import SwiftUI

/// 个人中心标签组件，无状态组件
struct ProfileTagView: View {
    let text: String
    let isSelected: Bool
    let action: () -> Void
    
    // MARK: - 显示文本
    private var displayText: String {
        // 如果是"全部"标签，不显示#
        if text == "全部" {
            return text
        }
        return "#\(text)"
    }
    
    // MARK: - Body
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                action()
            }
        }) {
            Text(displayText)
                .font(.system(size: 13, weight: isSelected ? .medium : .regular))
                .foregroundColor(isSelected ? .white : .secondary)
                .frame(maxWidth: .infinity)
                .frame(height: 32)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? Color.accentColor : Color(.systemGray6))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            isSelected ? Color.accentColor.opacity(0.3) : Color.clear,
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: isSelected ? Color.accentColor.opacity(0.2) : .clear,
                    radius: 4,
                    x: 0,
                    y: 2
                )
        }
        .buttonStyle(ScaleButtonStyle())
    }
}

#Preview("标签样式") {
    VStack(spacing: 16) {
        // 第一行：选中状态
        HStack(spacing: 12) {
            ProfileTagView(
                text: "城市探索",
                isSelected: true,
                action: {}
            )
            ProfileTagView(
                text: "户外运动",
                isSelected: true,
                action: {}
            )
            ProfileTagView(
                text: "美食探店",
                isSelected: true,
                action: {}
            )
        }
        
        // 第二行：未选中状态
        HStack(spacing: 12) {
            ProfileTagView(
                text: "文化艺术",
                isSelected: false,
                action: {}
            )
            ProfileTagView(
                text: "摄影",
                isSelected: false,
                action: {}
            )
            ProfileTagView(
                text: "旅行",
                isSelected: false,
                action: {}
            )
        }
        
        // 第三行：混合状态
        HStack(spacing: 12) {
            ProfileTagView(
                text: "生活",
                isSelected: true,
                action: {}
            )
            ProfileTagView(
                text: "设计",
                isSelected: false,
                action: {}
            )
            ProfileTagView(
                text: "咖啡",
                isSelected: false,
                action: {}
            )
        }
    }
    .padding()
    .background(Color(.systemBackground))
}

#Preview("单个标签") {
    ProfileTagView(
        text: "城市探索",
        isSelected: true,
        action: {}
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("全部标签") {
    ProfileTagView(
        text: "全部",
        isSelected: true,
        action: {}
    )
    .padding()
    .background(Color(.systemBackground))
} 
