import SwiftUI

/// ItineraryErrorView
/// 行程加载错误提示视图，带重试按钮。
///
/// 功能特性：
/// - 根据错误类型显示不同的图标和消息
/// - 提供重试按钮和恢复建议
/// - 支持无障碍访问
/// - 响应式布局设计
struct ItineraryErrorView: View {

    // MARK: - Properties

    /// 错误信息
    let error: Error

    /// 重试操作回调
    let retryAction: () -> Void

    // MARK: - Body

    var body: some View {
        VStack(spacing: 20) {
            errorIconView
            errorMessageView
            retryButtonView
        }
        .padding(.horizontal, 24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 80)
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityDescription)
    }

    // MARK: - Private Views

    /// 错误图标视图
    private var errorIconView: some View {
        Image(systemName: "exclamationmark.triangle")
            .font(.system(size: 48, weight: .light))
            .foregroundColor(.red)
            .accessibilityHidden(true)
    }

    /// 错误消息视图
    private var errorMessageView: some View {
        VStack(spacing: 8) {
            Text(ItineraryPlanConstants.Text.loadingError)
                .font(.title2.bold())
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)

            Text(error.localizedDescription)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
    }

    /// 重试按钮视图
    private var retryButtonView: some View {
        Button(action: retryAction) {
            HStack(spacing: 8) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 14, weight: .medium))
                Text(ItineraryPlanConstants.Text.retryAction)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .clipShape(Capsule())
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("重试加载数据")
    }

    // MARK: - Computed Properties

    /// 无障碍描述
    private var accessibilityDescription: String {
        "\(ItineraryPlanConstants.Text.loadingError)。\(error.localizedDescription)。点击重试按钮重新加载。"
    }
}