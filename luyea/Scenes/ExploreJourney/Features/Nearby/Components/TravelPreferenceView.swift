import SwiftUI

struct TravelPreferenceView: View {
    @StateObject private var viewModel = TravelPreferenceViewModel()
    
    private let columns = [
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible())
    ]
    
    var body: some View {
        TabBarAwareScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 旅行偏好选择
                VStack(alignment: .leading, spacing: 10) {
                    Text("旅行偏好")
                        .font(.headline)
                        .padding(.horizontal)

                    LazyVGrid(columns: columns, spacing: 15) {
                        ForEach(viewModel.preferences) { preference in
                            PreferenceButtonView(
                                preference: preference,
                                isSelected: viewModel.selectedPreference == preference
                            ) {
                                viewModel.selectPreference(preference)
                            }
                        }
                    }
                    .padding(.horizontal)
                }

                // 附近推荐内容
                VStack(alignment: .leading, spacing: 10) {
                    Text("附近推荐")
                        .font(.headline)
                        .padding(.horizontal)

                    // 附近推荐内容占位视图
                    NearbyRecommendationPlaceholder()
                }
            }
            .padding(.top)
        }
    }
}

/// 附近推荐占位视图
private struct NearbyRecommendationPlaceholder: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "location.circle")
                .font(.system(size: 48))
                .foregroundColor(.gray.opacity(0.6))

            Text("附近推荐功能即将上线")
                .font(.headline)
                .foregroundColor(.gray)

            Text("我们正在为您准备基于位置的个性化推荐")
                .font(.caption)
                .foregroundColor(.gray.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }
}

#Preview {
    TravelPreferenceView()
}
