import Foundation

/// 文件相关常量定义
///
/// 统一管理应用中使用的文件名、路径等常量。
/// 按文件类型和用途分组，便于维护和查找。
enum FileConstants {
    // MARK: - 配置文件

    /// 启动页配置文件名
    static let splashConfigFileName = "splashConfig.json"
    /// 用户偏好设置文件名
    static let userPreferencesFileName = "userPreferences.json"
    /// 应用配置文件名
    static let appConfigFileName = "appConfig.json"

    // MARK: - 缓存文件

    /// 图片缓存目录名
    static let imageCacheDirectoryName = "ImageCache"
    /// 数据缓存目录名
    static let dataCacheDirectoryName = "DataCache"
    /// 临时文件目录名
    static let tempDirectoryName = "Temp"



    // MARK: - 文件扩展名

    /// JSON 文件扩展名
    static let jsonExtension = "json"
    /// 图片文件扩展名
    static let imageExtensions = ["jpg", "jpeg", "png", "gif", "webp"]
    /// 视频文件扩展名
    static let videoExtensions = ["mp4", "mov", "avi"]
}