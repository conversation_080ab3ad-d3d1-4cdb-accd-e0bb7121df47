#!/bin/bash

# =============================================================================
# 强制更新依赖脚本 - Force Update Dependencies Script
# =============================================================================
# 
# 用途：清理所有缓存并强制重新下载最新版本的 Swift Package 依赖
# Usage: Clean all caches and force download latest Swift Package dependencies
#
# 使用方法 / Usage:
#   ./scripts/force-update-dependencies.sh
#   或者 / or: bash scripts/force-update-dependencies.sh
#
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在项目根目录
check_project_root() {
    if [ ! -f "luyea.xcodeproj/project.pbxproj" ]; then
        log_error "请在项目根目录运行此脚本"
        log_error "Please run this script from the project root directory"
        exit 1
    fi
}

# 清理 Xcode DerivedData
clean_derived_data() {
    log_info "清理 Xcode DerivedData..."
    if [ -d ~/Library/Developer/Xcode/DerivedData ]; then
        rm -rf ~/Library/Developer/Xcode/DerivedData
        log_success "DerivedData 已清理"
    else
        log_warning "DerivedData 目录不存在"
    fi
}

# 清理 Swift Package Manager 缓存
clean_spm_cache() {
    log_info "清理 Swift Package Manager 缓存..."
    
    # 清理系统级 SPM 缓存
    if [ -d ~/Library/Caches/org.swift.swiftpm ]; then
        rm -rf ~/Library/Caches/org.swift.swiftpm
        log_success "SPM 系统缓存已清理"
    fi
    
    if [ -d ~/Library/org.swift.swiftpm ]; then
        rm -rf ~/Library/org.swift.swiftpm
        log_success "SPM 用户缓存已清理"
    fi
}

# 清理项目本地缓存
clean_project_cache() {
    log_info "清理项目本地缓存..."
    
    # 清理 .build 目录
    if [ -d .build ]; then
        rm -rf .build
        log_success "项目 .build 目录已清理"
    fi
    
    # 清理 Package.resolved
    if [ -f "luyea.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved" ]; then
        rm -f "luyea.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved"
        log_success "Package.resolved 已删除"
    fi
    
    # 清理 SourcePackages 目录
    if [ -d "luyea.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/SourcePackages" ]; then
        rm -rf "luyea.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/SourcePackages"
        log_success "SourcePackages 目录已清理"
    fi
}

# 强制重新解析依赖
force_resolve_dependencies() {
    log_info "强制重新解析包依赖..."

    # 重试机制
    local MAX_RETRIES=3
    local RETRY_COUNT=0

    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if xcodebuild -project luyea.xcodeproj -resolvePackageDependencies; then
            log_success "包依赖解析成功"
            return 0
        else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                log_warning "解析失败，等待 5 秒后重试 ($RETRY_COUNT/$MAX_RETRIES)..."
                sleep 5
            else
                log_error "包依赖解析失败，请检查网络连接"
                log_error "提示：可以在 Xcode 中手动更新依赖"
                exit 1
            fi
        fi
    done
}

# 验证构建
verify_build() {
    log_info "验证项目构建..."
    
    # 清理构建
    if xcodebuild -project luyea.xcodeproj -scheme luyea -destination 'platform=iOS Simulator,name=iPhone 16' clean; then
        log_success "项目清理成功"
    else
        log_warning "项目清理失败，但继续执行"
    fi
    
    # 尝试构建
    if xcodebuild -project luyea.xcodeproj -scheme luyea -destination 'platform=iOS Simulator,name=iPhone 16' build; then
        log_success "项目构建成功"
    else
        log_error "项目构建失败"
        exit 1
    fi
}

# 显示依赖信息
show_dependency_info() {
    log_info "当前依赖信息："
    
    if [ -f "luyea.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved" ]; then
        echo ""
        cat "luyea.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved"
        echo ""
    else
        log_warning "Package.resolved 文件不存在"
    fi
}

# 主函数
main() {
    echo "=============================================="
    echo "🚀 强制更新依赖脚本 Force Update Dependencies"
    echo "=============================================="
    echo ""
    
    # 检查项目根目录
    check_project_root
    
    # 执行清理步骤
    clean_derived_data
    clean_spm_cache
    clean_project_cache
    
    echo ""
    log_info "开始重新解析依赖..."
    echo ""
    
    # 强制重新解析依赖
    force_resolve_dependencies
    
    echo ""
    log_info "验证构建..."
    echo ""
    
    # 验证构建
    verify_build
    
    echo ""
    log_info "显示最新依赖信息..."
    echo ""
    
    # 显示依赖信息
    show_dependency_info
    
    echo ""
    echo "=============================================="
    log_success "✅ 依赖更新完成！"
    echo "=============================================="
}

# 运行主函数
main "$@"
