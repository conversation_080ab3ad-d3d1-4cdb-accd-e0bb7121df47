import SwiftUI

/// ContentView 视图模型
///
/// 负责管理ContentView的状态，专注于启动画面等非TabBar相关的状态管理。
/// TabBar相关状态已迁移到TabBarStateManager进行统一管理。
///
/// 主要功能：
/// - 管理启动画面的显示隐藏
/// - 处理应用启动流程
/// - 协调全局UI状态
///
/// 注意：TabBar相关状态请使用TabBarStateManager.shared
@MainActor
class ContentViewViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 是否显示启动画面
    ///
    /// 控制应用启动时的启动画面显示状态。
    /// 启动完成后会自动隐藏启动画面。
    @Published var isShowingSplash: Bool = true

    // MARK: - Public Methods

    /// 隐藏启动画面
    ///
    /// 以动画方式隐藏启动画面，标志着应用启动完成。
    /// 通常在应用初始化完成后调用。
    func hideSplash() {
        withAnimation(.easeInOut) {
            isShowingSplash = false
        }
    }

    /// 重置到启动状态
    ///
    /// 重新显示启动画面，适用于应用重启或重置场景。
    func resetToSplash() {
        withAnimation(.easeInOut) {
            isShowingSplash = true
        }
    }
}
