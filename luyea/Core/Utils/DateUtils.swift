import Foundation

/// 日期工具类
///
/// 提供常用的日期处理、格式化和计算功能，减少重复代码。
/// 包含日期验证、格式化、组件更新等实用方法。
final class DateUtils {
    
    // MARK: - Date Formatting
    
    /// 格式化日期为指定样式
    ///
    /// - Parameters:
    ///   - date: 要格式化的日期
    ///   - style: 日期样式
    ///   - locale: 地区设置，默认为中文
    /// - Returns: 格式化后的日期字符串
    static func formatDate(_ date: Date, style: DateFormatter.Style, locale: Locale = Locale(identifier: "zh_CN")) -> String {
        let formatter = DateFormatter()
        formatter.locale = locale
        formatter.dateStyle = style
        return formatter.string(from: date)
    }
    
    /// 格式化日期为自定义格式
    ///
    /// - Parameters:
    ///   - date: 要格式化的日期
    ///   - format: 自定义格式字符串
    ///   - locale: 地区设置，默认为中文
    /// - Returns: 格式化后的日期字符串
    static func formatDate(_ date: Date, format: String, locale: Locale = Locale(identifier: "zh_CN")) -> String {
        let formatter = DateFormatter()
        formatter.locale = locale
        formatter.dateFormat = format
        return formatter.string(from: date)
    }
    
    /// 格式化选择的日期为API标准格式
    ///
    /// - Parameter date: 要格式化的日期
    /// - Returns: 格式化后的日期字符串，如 "2024-06-23"
    static func formatSelectedDate(_ date: Date) -> String {
        return formatDate(date, format: "yyyy-MM-dd")
    }

    /// 格式化日期为显示格式
    ///
    /// - Parameter date: 要格式化的日期
    /// - Returns: 格式化后的日期字符串，如 "2024年6月23日"
    static func formatDisplayDate(_ date: Date) -> String {
        return formatDate(date, format: "yyyy年M月d日")
    }
    
    /// 格式化日期为简短显示格式
    ///
    /// - Parameter date: 要格式化的日期
    /// - Returns: 格式化后的日期字符串，如 "6月23日"
    static func formatShortDate(_ date: Date) -> String {
        return formatDate(date, format: "M月d日")
    }
    
    // MARK: - Date Validation
    
    /// 检查日期是否在同一天
    ///
    /// - Parameters:
    ///   - date1: 第一个日期
    ///   - date2: 第二个日期
    /// - Returns: 是否在同一天
    static func isSameDay(_ date1: Date, _ date2: Date) -> Bool {
        return Calendar.current.isDate(date1, inSameDayAs: date2)
    }
    
    /// 检查日期是否为今天
    ///
    /// - Parameter date: 要检查的日期
    /// - Returns: 是否为今天
    static func isToday(_ date: Date) -> Bool {
        return Calendar.current.isDateInToday(date)
    }
    
    /// 检查日期是否为明天
    ///
    /// - Parameter date: 要检查的日期
    /// - Returns: 是否为明天
    static func isTomorrow(_ date: Date) -> Bool {
        return Calendar.current.isDateInTomorrow(date)
    }
    
    /// 检查日期是否在未来
    ///
    /// - Parameter date: 要检查的日期
    /// - Returns: 是否在未来
    static func isFuture(_ date: Date) -> Bool {
        return date > Date()
    }
    
    /// 检查日期是否有效（不早于今天）
    ///
    /// - Parameter date: 要检查的日期
    /// - Returns: 是否有效
    static func isValidFutureDate(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let targetDay = calendar.startOfDay(for: date)
        return targetDay >= today
    }
    
    // MARK: - Date Components
    
    /// 更新日期的指定组件
    ///
    /// - Parameters:
    ///   - date: 原始日期
    ///   - component: 要更新的日期组件
    ///   - value: 新值
    /// - Returns: 更新后的日期，如果无效则返回原日期
    static func updateDateComponent(_ date: Date, component: Calendar.Component, value: Int) -> Date {
        let calendar = Calendar.current
        var components = calendar.dateComponents([.year, .month, .day], from: date)
        
        switch component {
        case .year:
            components.year = value
        case .month:
            components.month = value
        case .day:
            components.day = value
        default:
            return date
        }
        
        // 确保日期有效且不早于今天
        if let newDate = calendar.date(from: components), isValidFutureDate(newDate) {
            return newDate
        } else {
            // 如果日期无效或早于今天，返回今天
            return Date()
        }
    }
    
    /// 获取指定年月的天数范围
    ///
    /// - Parameters:
    ///   - year: 年份
    ///   - month: 月份
    /// - Returns: 可选择的天数数组
    static func getAvailableDays(for year: Int, month: Int) -> [Int] {
        let calendar = Calendar.current
        
        // 创建该月第一天的日期
        var components = DateComponents()
        components.year = year
        components.month = month
        components.day = 1
        
        guard let firstDayOfMonth = calendar.date(from: components) else {
            return Array(1...31)
        }
        
        // 获取该月的天数
        let range = calendar.range(of: .day, in: .month, for: firstDayOfMonth)
        let numberOfDays = range?.count ?? 31
        
        // 如果是当前月，则从今天开始
        let today = Date()
        let currentYear = calendar.component(.year, from: today)
        let currentMonth = calendar.component(.month, from: today)
        let currentDay = calendar.component(.day, from: today)
        
        if year == currentYear && month == currentMonth {
            return Array(currentDay...numberOfDays)
        } else {
            return Array(1...numberOfDays)
        }
    }
    
    /// 获取可选择的年份范围
    ///
    /// - Parameter yearsAhead: 未来年份数量，默认为2年
    /// - Returns: 可选择的年份数组
    static func getAvailableYears(yearsAhead: Int = 2) -> [Int] {
        let currentYear = Calendar.current.component(.year, from: Date())
        return Array(currentYear...(currentYear + yearsAhead))
    }
    
    // MARK: - Date Calculations
    
    /// 获取明天的日期
    ///
    /// - Parameter from: 起始日期，默认为今天
    /// - Returns: 明天的日期
    static func getTomorrow(from date: Date = Date()) -> Date? {
        return Calendar.current.date(byAdding: .day, value: 1, to: date)
    }
    
    /// 获取指定天数后的日期
    ///
    /// - Parameters:
    ///   - days: 天数
    ///   - from: 起始日期，默认为今天
    /// - Returns: 指定天数后的日期
    static func getDateAfter(days: Int, from date: Date = Date()) -> Date? {
        return Calendar.current.date(byAdding: .day, value: days, to: date)
    }
    
    /// 计算两个日期之间的天数差
    ///
    /// - Parameters:
    ///   - from: 起始日期
    ///   - to: 结束日期
    /// - Returns: 天数差
    static func daysBetween(from: Date, to: Date) -> Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: from, to: to)
        return components.day ?? 0
    }
    
    // MARK: - Quick Date Options
    
    /// 快速日期选项
    struct QuickDateOption {
        let title: String
        let subtitle: String
        let date: Date
        
        init(title: String, subtitle: String, date: Date) {
            self.title = title
            self.subtitle = subtitle
            self.date = date
        }
        
        init(title: String, date: Date) {
            self.title = title
            self.subtitle = DateUtils.formatShortDate(date)
            self.date = date
        }
    }
    
    /// 生成快速日期选择选项
    ///
    /// - Parameter count: 选项数量，默认为6个
    /// - Returns: 快速日期选项数组
    static func generateQuickDateOptions(count: Int = 6) -> [QuickDateOption] {
        let today = Date()
        var options: [QuickDateOption] = []
        
        // 今天
        options.append(QuickDateOption(
            title: "今天",
            subtitle: formatShortDate(today),
            date: today
        ))
        
        // 明天
        if let tomorrow = getTomorrow() {
            options.append(QuickDateOption(
                title: "明天",
                subtitle: formatShortDate(tomorrow),
                date: tomorrow
            ))
        }
        
        // 最近的周末
        if let nextWeekend = HolidayDataService.shared.getNextWeekend() {
            options.append(QuickDateOption(
                title: "最近周末",
                subtitle: formatShortDate(nextWeekend),
                date: nextWeekend
            ))
        }
        
        // 获取最近的节假日
        let nextHolidays = HolidayDataService.shared.getNextHolidays(count: count - 3)
        for holiday in nextHolidays {
            let calendar = Calendar.current
            let holidayEve = calendar.date(byAdding: .day, value: -1, to: holiday.date) ?? holiday.date
            options.append(QuickDateOption(
                title: holiday.name,
                subtitle: "\(formatShortDate(holidayEve)) (前一天)",
                date: holidayEve
            ))
        }
        
        // 只返回指定数量的选项
        return Array(options.prefix(count))
    }
}
