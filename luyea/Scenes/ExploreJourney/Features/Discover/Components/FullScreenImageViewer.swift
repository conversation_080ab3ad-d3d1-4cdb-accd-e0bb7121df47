import SwiftUI

/// 全屏图片预览器
/// 支持放大缩小、拖动查看、图片切换等功能
struct FullScreenImageViewerView: View {
    let imageUrls: [String]
    @Binding var currentPage: Int
    @Binding var isPresented: Bool

    // 自动滚动控制回调
    let onZoomChanged: (Bool) -> Void
    
    // 为每个图片维护独立的缩放状态
    @State private var imageStates: [Int: ImageState] = [:]

    // 图片状态结构
    struct ImageState {
        var scale: CGFloat = 1.0
        var offset: CGSize = .zero
        var lastOffset: CGSize = .zero
    }

    // 当前图片的缩放状态
    private var currentImageState: ImageState {
        imageStates[currentPage] ?? ImageState()
    }

    private var isCurrentImageZoomed: Bool {
        currentImageState.scale > 1.0
    }
    
    // 缩放和拖动的限制
    private let minScale: CGFloat = 1.0
    private let maxScale: CGFloat = 4.0
    private let resetButtonAnimationDuration: Double = 0.3
    
    var body: some View {
        Color.black
            .ignoresSafeArea(.all, edges: .all)
            .overlay {
                ZStack {
                    // 背景层 - 添加渐变效果
                    Color.black
                        .ignoresSafeArea(.all, edges: .all)
                        .opacity(0.95)
                        .onTapGesture {
                            withAnimation(.spring(response: 0.45, dampingFraction: 0.85, blendDuration: 0.05)) {
                                isPresented = false
                            }
                        }

                    mainContentView()
                        .scaleEffect(1.0) // 确保内容以正常大小显示
                }
            }
            .animation(.spring(response: 0.35, dampingFraction: 0.85, blendDuration: 0.05), value: isCurrentImageZoomed)
            .onAppear {
                for i in 0..<imageUrls.count {
                    if imageStates[i] == nil {
                        imageStates[i] = ImageState()
                    }
                }
            }
            .onChange(of: currentPage) { _, newPage in
                let currentState = imageStates[newPage] ?? ImageState()
                let isZoomed = currentState.scale > 1.0
                onZoomChanged(isZoomed)
            }
    }

    // MARK: - 主要内容视图
    @ViewBuilder
    private func mainContentView() -> some View {
        ZStack {
            imageCarouselView()

            if isCurrentImageZoomed {
                dragOverlayView()
            }

            controlButtonsView()
        }
    }

    // MARK: - 图片轮播视图
    @ViewBuilder
    private func imageCarouselView() -> some View {
        TabView(selection: $currentPage) {
            ForEach(Array(imageUrls.enumerated()), id: \.offset) { index, imageUrl in
                singleImageView(for: index, imageUrl: imageUrl)
                    .tag(index)
            }
        }
        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        .disabled(isCurrentImageZoomed)
    }

    // MARK: - 单个图片视图
    @ViewBuilder
    private func singleImageView(for index: Int, imageUrl: String) -> some View {
        ZStack {
            Color.clear

            CachedAsyncImage(
                url: URL(string: imageUrl)
            ) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .scaleEffect(currentImageState.scale)
                    .offset(currentImageState.offset)
                    .transition(.scale(scale: 0.95).combined(with: .opacity))
            } placeholder: {
                VStack(spacing: 12) {
                    ProgressView()
                        .tint(.white)
                        .scaleEffect(1.2)

                    Text("加载中...")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
                .transition(.opacity)
            } errorView: {
                VStack(spacing: 16) {
                    Image(systemName: "photo")
                        .font(.system(size: 48))
                        .foregroundColor(.white.opacity(0.6))

                    Text("图片加载失败")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                .transition(.opacity)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .clipped()
        .onTapGesture(count: 2) {
            // 双击缩放逻辑
            handleDoubleTap(for: index)
        }
        .gesture(
            // 缩放手势
            MagnificationGesture()
                .onChanged { value in
                    handleMagnificationChanged(for: index, value: value)
                }
                .onEnded { _ in
                    handleMagnificationEnded(for: index)
                }
        )
    }

    // MARK: - 拖动覆盖层
    @ViewBuilder
    private func dragOverlayView() -> some View {
        Color.clear
            .contentShape(Rectangle())
            .onTapGesture(count: 2) {
                handleDoubleTap(for: currentPage)
            }
            .gesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { value in
                        handleDragChanged(value: value)
                    }
                    .onEnded { _ in
                        handleDragEnded()
                    }
            )
    }

    // MARK: - 控制按钮层
    @ViewBuilder
    private func controlButtonsView() -> some View {
        ZStack {
            // 顶部控制栏
            VStack {
                HStack {
                    // 关闭按钮
                    Button(action: {
                        withAnimation(.spring(response: 0.45, dampingFraction: 0.85, blendDuration: 0.05)) {
                            isPresented = false
                        }
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 44, height: 44)
                            .background(Color.black.opacity(0.5))
                            .clipShape(Circle())
                    }

                    Spacer()

                    // 重置按钮（放大时显示）
                    if isCurrentImageZoomed {
                        Button(action: resetImageTransform) {
                            Image(systemName: "arrow.down.right.and.arrow.up.left")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 44, height: 44)
                                .background(Color.black.opacity(0.5))
                                .clipShape(Circle())
                        }
                        .transition(.scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .trailing)))
                    } else {
                        // 占位空间保持布局一致
                        Color.clear
                            .frame(width: 44, height: 44)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)

                Spacer()
            }

            // 右下角页面指示器
            if imageUrls.count > 1 {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        HStack(spacing: 6) {
                            ForEach(0..<imageUrls.count, id: \.self) { index in
                                Circle()
                                    .fill(index == currentPage ? Color.white : Color.white.opacity(0.4))
                                    .frame(width: 8, height: 8)
                                    .scaleEffect(index == currentPage ? 1.2 : 1.0)
                                    .animation(.spring(response: 0.25, dampingFraction: 0.8, blendDuration: 0.05), value: currentPage)
                            }
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.black.opacity(0.5))
                        .cornerRadius(16)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                }
            }
        }
    }

    // MARK: - 手势处理方法

    /// 处理双击手势
    private func handleDoubleTap(for index: Int) {
        let currentState = imageStates[index] ?? ImageState()

        withAnimation(.spring(response: 0.35, dampingFraction: 0.8, blendDuration: 0.05)) {
            if currentState.scale > minScale {
                updateImageState(for: index) { state in
                    state.scale = minScale
                    state.offset = .zero
                    state.lastOffset = .zero
                }
            } else {
                updateImageState(for: index) { state in
                    state.scale = 2.0
                }
            }
        }
    }

    /// 处理缩放手势变化
    private func handleMagnificationChanged(for index: Int, value: CGFloat) {
        let newScale = max(minScale, min(maxScale, value))
        updateImageState(for: index) { state in
            state.scale = newScale
        }
    }

    /// 处理缩放手势结束
    private func handleMagnificationEnded(for index: Int) {
        withAnimation(.spring(response: 0.35, dampingFraction: 0.85, blendDuration: 0.05)) {
            updateImageState(for: index) { state in
                if state.scale < minScale {
                    state.scale = minScale
                    state.offset = .zero
                    state.lastOffset = .zero
                } else if state.scale > maxScale {
                    state.scale = maxScale
                }
            }
        }
    }

    /// 处理拖动手势变化
    private func handleDragChanged(value: DragGesture.Value) {
        let currentState = imageStates[currentPage] ?? ImageState()

        if currentState.scale > minScale {
            let newOffset = CGSize(
                width: currentState.lastOffset.width + value.translation.width,
                height: currentState.lastOffset.height + value.translation.height
            )

            // 计算拖动边界
            let screenWidth = UIScreen.main.bounds.width
            let screenHeight = UIScreen.main.bounds.height
            let scaledWidth = screenWidth * currentState.scale
            let scaledHeight = screenHeight * currentState.scale

            let maxOffsetX = max(0, (scaledWidth - screenWidth) / 2)
            let maxOffsetY = max(0, (scaledHeight - screenHeight) / 2)

            updateImageState(for: currentPage) { state in
                state.offset = CGSize(
                    width: max(-maxOffsetX, min(maxOffsetX, newOffset.width)),
                    height: max(-maxOffsetY, min(maxOffsetY, newOffset.height))
                )
            }
        }
    }

    /// 处理拖动手势结束
    private func handleDragEnded() {
        updateImageState(for: currentPage) { state in
            state.lastOffset = state.offset
        }
    }

    // MARK: - 辅助方法

    /// 重置图片变换
    private func resetImageTransform() {
        withAnimation(.spring(response: 0.35, dampingFraction: 0.8, blendDuration: 0.05)) {
            updateImageState(for: currentPage) { state in
                state.scale = minScale
                state.offset = .zero
                state.lastOffset = .zero
            }
        }
    }

    /// 更新指定图片的状态
    private func updateImageState(for index: Int, update: (inout ImageState) -> Void) {
        var state = imageStates[index] ?? ImageState()
        let oldScale = state.scale
        update(&state)
        imageStates[index] = state

        // 如果是当前图片且缩放状态发生变化，通知父视图
        if index == currentPage && oldScale != state.scale {
            let isZoomed = state.scale > 1.0
            onZoomChanged(isZoomed)
        }
    }
}

// MARK: - 预览
#Preview {
    FullScreenImageViewerView(
        imageUrls: [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
            "https://images.unsplash.com/photo-1469474968028-56623f02e42e",
            "https://images.unsplash.com/photo-1441974231531-c6227db76b6e"
        ],
        currentPage: .constant(0),
        isPresented: .constant(true),
        onZoomChanged: { _ in }
    )
}
