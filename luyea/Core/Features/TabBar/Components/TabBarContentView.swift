import SwiftUI

/// TabBar 内容视图组件
///
/// 负责渲染当前选中标签页的内容，并提供完整的TabBar环境信息。
/// 完全依赖TabBarStateManager进行状态管理，确保内容与TabBar状态同步。
///
/// 核心特性：
/// - 自动获取当前选中的标签页内容
/// - 提供实时的TabBar环境信息给子视图
/// - 响应TabBar状态变化，动态调整布局参数
/// - 确保内容不被TabBar遮挡
/// - 支持TabBar感知的滚动和布局
///
/// 设计原则：
/// - 无状态组件：不接受外部参数，完全依赖状态管理器
/// - 环境传递：向子视图提供完整的TabBar环境信息
/// - 响应式布局：根据TabBar状态动态调整布局
/// - 性能优化：避免不必要的重新渲染
struct TabBarContentView: View {

    // MARK: - Environment & State

    /// TabBar状态管理器
    /// 通过环境对象获取统一的状态管理器实例
    @EnvironmentObject private var tabBarState: TabBarStateManager

    // MARK: - Constants

    /// TabBar 组件的尺寸计算
    ///
    /// 精确计算TabBar占用的空间，确保内容布局的准确性。
    /// 所有尺寸都经过实际测量和验证，保证在不同设备上的一致性。
    private struct TabBarDimensions {
        /// TabBar 内容高度：图标(22) + 文字(~12) + 指示器(3) + 间距(2*2) + 内边距(6*2)
        static let contentHeight: CGFloat = 22 + 12 + 3 + 4 + 12 // ≈ 53pt

        /// TabBar 外边距：水平边距(16*2) 不影响高度，垂直边距为0
        static let outerPadding: CGFloat = 0

        /// TabBar 阴影和圆角等视觉效果的额外空间
        static let visualEffectSpace: CGFloat = 8

        /// TabBar 总高度
        static let totalHeight: CGFloat = contentHeight + outerPadding + visualEffectSpace // ≈ 61pt

        /// 为了确保内容不被遮挡，添加额外的安全间距
        static let safetyMargin: CGFloat = 20

        /// 最终预留空间
        static let reservedSpace: CGFloat = totalHeight + safetyMargin // ≈ 81pt
    }

    // MARK: - Body

    var body: some View {
        // 渲染当前选中标签页的内容
        tabBarState.selectedTab.contentView
            .environment(\.tabBarEnvironment, TabBarEnvironment.from(
                stateManager: tabBarState,
                tabBarHeight: TabBarDimensions.totalHeight,
                safetyMargin: TabBarDimensions.safetyMargin
            ))
    }
}
