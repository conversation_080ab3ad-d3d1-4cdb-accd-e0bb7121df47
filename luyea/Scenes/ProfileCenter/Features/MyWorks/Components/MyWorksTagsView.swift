import SwiftUI

// MARK: - MyWorksTagsView

/// 我的作品页面的标签选择视图
///
/// 提供话题标签的展示和选择功能，支持展开/收起操作
/// 参考UserProfileTagsView的美观设计，但针对作品管理进行了优化
struct MyWorksTagsView: View {

    // MARK: - Properties

    /// 内容标签列表
    let contentTags: [String]

    /// 当前选中的标签
    @Binding var selectedTag: String?

    /// 标签是否展开
    @Binding var isTagsExpanded: Bool

    /// 总作品数量
    let totalWorksCount: Int

    /// 话题统计数据
    let topicStats: [MyWorksModels.TopicStats]

    // MARK: - Configuration

    /// 每行标签数量
    private let tagsPerRow = 3

    /// 默认显示行数
    private let defaultVisibleRows = 1

    // MARK: - Computed Properties

    /// 默认可见标签数量（减1是因为包含"全部"标签）
    private var defaultVisibleTagCount: Int {
        tagsPerRow * defaultVisibleRows - 1
    }

    /// 是否应该显示展开按钮
    private var shouldShowExpandButton: Bool {
        contentTags.count > defaultVisibleTagCount
    }

    private var visibleTags: [String] {
        var tags = ["全部"]

        if isTagsExpanded {
            // 展开状态：显示所有标签 + 收起按钮
            tags.append(contentsOf: contentTags)
            if shouldShowExpandButton {
                tags.append("收起标签")
            }
        } else {
            // 折叠状态：显示部分标签 + 展开按钮
            // 如果有选中的标签，确保它在折叠状态下也可见
            if let selectedTag = selectedTag,
               let selectedIndex = contentTags.firstIndex(of: selectedTag) {
                let availableSlots = shouldShowExpandButton ? defaultVisibleTagCount - 1 : defaultVisibleTagCount
                let startIndex = max(0, selectedIndex - (availableSlots - 1))
                let endIndex = min(contentTags.count, startIndex + availableSlots)
                let visibleContentTags = Array(contentTags[startIndex..<endIndex])
                tags.append(contentsOf: visibleContentTags)
            } else {
                let availableSlots = shouldShowExpandButton ? defaultVisibleTagCount - 1 : defaultVisibleTagCount
                tags.append(contentsOf: Array(contentTags.prefix(availableSlots)))
            }

            // 如果需要展开按钮，添加特殊的"更多"标签
            if shouldShowExpandButton {
                tags.append("展开更多")
            }
        }

        return tags
    }
    
    // MARK: - Body
    var body: some View {
        ZStack(alignment: .topTrailing) {
            // 标签流式布局 - 自适应宽度设计
            FlowLayout(spacing: 10) {
                ForEach(visibleTags, id: \.self) { tag in
                    if tag == "展开更多" || tag == "收起标签" {
                        // 特殊的展开/收起按钮标签
                        MyWorksExpandTagView(
                            isExpanded: isTagsExpanded,
                            action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    isTagsExpanded.toggle()
                                }
                            }
                        )
                        .transition(.scale.combined(with: .opacity))
                    } else {
                        // 普通标签
                        MyWorksProfileTagView(
                            text: tag,
                            isSelected: tag == "全部" ? selectedTag == nil : selectedTag == tag,
                            count: tag == "全部" ? totalWorksCount : getTagCount(for: tag),
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.9)) {
                                    if tag == "全部" {
                                        selectedTag = nil
                                    } else {
                                        selectedTag = tag
                                    }
                                }
                            }
                        )
                        .transition(.scale.combined(with: .opacity))
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 4)
        }
        .padding(.vertical, 12)
        .background(
            // 添加微妙的背景效果
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.02), radius: 1, x: 0, y: 1)
        )
    }
    
    // MARK: - 辅助方法
    private func getTagCount(for tag: String) -> Int {
        // 从实际的话题统计数据中获取数量
        return topicStats.first { $0.name == tag }?.worksCount ?? 0
    }
}

/// 展开/收起标签按钮 - 精致设计版本
/// 与主标签保持一致的设计语言
struct MyWorksExpandTagView: View {
    let isExpanded: Bool
    let action: () -> Void

    @State private var isPressed = false

    // MARK: - 设计配置
    private let cornerRadius: CGFloat = 16
    private let tagHeight: CGFloat = 32

    // MARK: - 颜色配置
    private var accentColor: Color {
        isExpanded ? .blue : .secondary
    }

    private var backgroundGradient: LinearGradient {
        if isExpanded {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.15),
                    Color.blue.opacity(0.08)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(.systemGray6),
                    Color(.systemGray5).opacity(0.3)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }

    var body: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()

            withAnimation(.spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0.2)) {
                action()
            }
        }) {
            HStack(spacing: 6) {
                Text(isExpanded ? "收起" : "更多")
                    .font(.system(size: 13, weight: .medium, design: .rounded))
                    .foregroundColor(accentColor)

                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(accentColor)
                    .rotationEffect(.degrees(isExpanded ? 0 : 0))
                    .animation(.easeInOut(duration: 0.3), value: isExpanded)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .frame(maxWidth: .infinity)
            .frame(height: tagHeight)
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(backgroundGradient)
                    .overlay(
                        // 内部高光效果
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.1),
                                        Color.clear
                                    ]),
                                    startPoint: .top,
                                    endPoint: .bottom
                                ),
                                lineWidth: 1
                            )
                            .blendMode(.overlay)
                    )
            )
            .overlay(
                // 外部边框
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        isExpanded ?
                        Color.accentColor.opacity(0.3) :
                        Color.secondary.opacity(0.2),
                        lineWidth: isExpanded ? 1.5 : 0.5
                    )
            )
            .shadow(
                color: isExpanded ?
                Color.accentColor.opacity(0.15) :
                Color.primary.opacity(0.08),
                radius: isExpanded ? 6 : 3,
                x: 0,
                y: isExpanded ? 3 : 2
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(ScaleButtonStyle(scale: 0.95))
    }
}

/// 我的作品标签按钮 - 精致设计版本
/// 采用现代化设计语言，包含渐变背景、精致动画和优雅的视觉层次
struct MyWorksProfileTagView: View {
    let text: String
    let isSelected: Bool
    let count: Int
    let action: () -> Void

    @State private var isPressed = false

    // MARK: - 设计配置
    private let cornerRadius: CGFloat = 16
    private let tagHeight: CGFloat = 32

    // MARK: - 显示文本
    private var displayText: String {
        // 所有标签都不显示#符号，保持简洁
        return text
    }

    // MARK: - 颜色配置
    private var backgroundGradient: LinearGradient {
        if isSelected {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color.accentColor,
                    Color.accentColor.opacity(0.8)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(.systemGray6),
                    Color(.systemGray5).opacity(0.3)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }

    private var textColor: Color {
        isSelected ? .white : .primary
    }

    private var countColor: Color {
        isSelected ? .white.opacity(0.85) : .secondary
    }

    // MARK: - Body
    var body: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()

            withAnimation(.spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0.2)) {
                action()
            }
        }) {
            HStack(spacing: 6) {
                // 主标签文本
                Text(displayText)
                    .font(.system(size: 13, weight: isSelected ? .medium : .regular, design: .rounded))
                    .foregroundColor(textColor)
                    .lineLimit(1)

                // 作品数量显示 - 精致的徽章样式（统一风格）
                Text("\(count)")
                    .font(.system(size: 10, weight: .medium, design: .rounded))
                    .foregroundColor(countColor)
                    .padding(.horizontal, 5)
                    .padding(.vertical, 1)
                    .background(
                        Capsule()
                            .fill(isSelected ?
                                  Color.white.opacity(0.2) :
                                  Color.secondary.opacity(0.15))
                    )
                    .transition(.scale.combined(with: .opacity))
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .frame(height: tagHeight)
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(backgroundGradient)
                    .overlay(
                        // 内部高光效果
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(isSelected ? 0.3 : 0.1),
                                        Color.clear
                                    ]),
                                    startPoint: .top,
                                    endPoint: .bottom
                                ),
                                lineWidth: 1
                            )
                            .blendMode(.overlay)
                    )
            )
            .overlay(
                // 外部边框
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        isSelected ?
                        Color.accentColor.opacity(0.4) :
                        Color.secondary.opacity(0.2),
                        lineWidth: isSelected ? 1.5 : 0.5
                    )
            )
            .shadow(
                color: isSelected ?
                Color.accentColor.opacity(0.25) :
                Color.primary.opacity(0.08),
                radius: isSelected ? 8 : 3,
                x: 0,
                y: isSelected ? 4 : 2
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(ScaleButtonStyle(scale: 0.95))
    }
}

// MARK: - 预览
#Preview("标签视图 - 美观设计") {
    MyWorksTagsView(
        contentTags: [
            "自然风光", "美食探店", "历史文化",
            "城市漫步", "艺术展览", "特色民宿",
            "主题乐园", "购物指南", "交通攻略"
        ],
        selectedTag: .constant(nil),
        isTagsExpanded: .constant(false),
        totalWorksCount: 45,
        topicStats: [
            MyWorksModels.TopicStats(id: "1", name: "自然风光", worksCount: 12),
            MyWorksModels.TopicStats(id: "2", name: "美食探店", worksCount: 8),
            MyWorksModels.TopicStats(id: "3", name: "历史文化", worksCount: 6)
        ]
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("标签视图 - 选中状态") {
    MyWorksTagsView(
        contentTags: [
            "自然风光", "美食探店", "历史文化",
            "城市漫步", "艺术展览", "特色民宿"
        ],
        selectedTag: .constant("美食探店"),
        isTagsExpanded: .constant(false),
        totalWorksCount: 45,
        topicStats: [
            MyWorksModels.TopicStats(id: "1", name: "自然风光", worksCount: 12),
            MyWorksModels.TopicStats(id: "2", name: "美食探店", worksCount: 8),
            MyWorksModels.TopicStats(id: "3", name: "历史文化", worksCount: 6)
        ]
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("标签视图 - 选中后面的标签") {
    MyWorksTagsView(
        contentTags: [
            "自然风光", "美食探店", "历史文化",
            "城市漫步", "艺术展览", "特色民宿",
            "主题乐园", "购物指南", "交通攻略",
            "咖啡探店", "摄影", "旅行"
        ],
        selectedTag: .constant("咖啡探店"),
        isTagsExpanded: .constant(false),
        totalWorksCount: 45,
        topicStats: [
            MyWorksModels.TopicStats(id: "1", name: "自然风光", worksCount: 12),
            MyWorksModels.TopicStats(id: "2", name: "美食探店", worksCount: 8),
            MyWorksModels.TopicStats(id: "3", name: "历史文化", worksCount: 6),
            MyWorksModels.TopicStats(id: "4", name: "咖啡探店", worksCount: 4)
        ]
    )
    .padding()
    .background(Color(.systemBackground))
}

/// 搜索按钮组件
/// 只负责显示搜索/关闭按钮，搜索框在主视图中单独处理
struct MyWorksSearchButton: View {
    @Binding var isExpanded: Bool

    // 点击按钮时的回调
    let onToggle: (() -> Void)?

    var body: some View {
        Button(action: {
            onToggle?()
        }) {
            Image(systemName: isExpanded ? "xmark" : "magnifyingglass")
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 24, height: 24)
                .background(Color(.systemGray6))
                .clipShape(Circle())
                .rotationEffect(.degrees(isExpanded ? 90 : 0))
                .scaleEffect(isExpanded ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isExpanded)
    }
}

/// 搜索输入框组件
/// 在标题和标签之间展开的搜索框
struct MyWorksSearchField: View {
    @Binding var searchText: String
    @FocusState private var isTextFieldFocused: Bool

    // 取消搜索时的回调
    let onCancel: (() -> Void)?

    // 提交搜索时的回调
    let onSearch: ((String) -> Void)?

    var body: some View {
        HStack(spacing: 12) {
            // 搜索输入框
            HStack(spacing: 8) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)

                TextField("搜索作品", text: $searchText)
                    .font(.system(size: 15))
                    .focused($isTextFieldFocused)
                    .submitLabel(.search)
                    .onSubmit {
                        performSearch()
                    }

                // 清空按钮
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(20)

            // 搜索按钮
            Button(action: {
                performSearch()
            }) {
                Text("搜索")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.accentColor)
                    .cornerRadius(20)
            }
            .disabled(searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            .opacity(searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.6 : 1.0)
        }
        .padding(.horizontal, DesignSystemConstants.Spacing.standard)
        .padding(.top, 8)
        .onAppear {
            // 展开时自动聚焦
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                isTextFieldFocused = true
            }
        }
    }

    private func performSearch() {
        let keyword = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        if !keyword.isEmpty {
            onSearch?(keyword)
            isTextFieldFocused = false
        }
    }
}

#Preview("标签按钮 - 美观设计") {
    HStack(spacing: 12) {
        MyWorksProfileTagView(
            text: "全部",
            isSelected: true,
            count: 45,
            action: {}
        )

        MyWorksProfileTagView(
            text: "自然风光",
            isSelected: false,
            count: 12,
            action: {}
        )

        MyWorksProfileTagView(
            text: "美食探店",
            isSelected: false,
            count: 8,
            action: {}
        )
    }
    .padding()
    .background(Color(.systemBackground))
}

#Preview("按钮统一设计") {
    VStack(spacing: 30) {
        // 按钮对比 - 统一风格
        VStack(alignment: .leading, spacing: 8) {
            Text("按钮风格统一")
                .font(.caption)
                .foregroundColor(.secondary)

            HStack(spacing: 12) {
                Text("作品列表")
                    .font(.system(size: 16, weight: .medium))

                // 排序按钮
                MyWorksSortButton(
                    selectedOption: .constant(.createTime),
                    showMenu: .constant(false)
                )

                Spacer()

                // 搜索按钮 - 折叠状态
                MyWorksSearchButton(
                    isExpanded: .constant(false),
                    onToggle: nil
                )

                // 搜索按钮 - 展开状态
                MyWorksSearchButton(
                    isExpanded: .constant(true),
                    onToggle: nil
                )
            }
        }

        // 搜索输入框
        VStack(alignment: .leading, spacing: 8) {
            Text("搜索输入框")
                .font(.caption)
                .foregroundColor(.secondary)

            MyWorksSearchField(
                searchText: .constant("自然风光"),
                onCancel: nil,
                onSearch: { keyword in
                    print("搜索: \(keyword)")
                }
            )
        }
    }
    .padding()
    .background(Color(.systemBackground))
}

/// 排序选择按钮组件
struct MyWorksSortButton: View {
    @Binding var selectedOption: MyWorksSortOption
    @Binding var showMenu: Bool

    var body: some View {
        Menu {
            ForEach(MyWorksSortOption.allCases, id: \.self) { option in
                Button(action: {
                    selectedOption = option
                }) {
                    HStack {
                        Image(systemName: option.iconName)
                        Text(option.displayName)
                        Spacer()
                        if selectedOption == option {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accentColor)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 4) {
                Text(selectedOption.displayName)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.secondary)
                Image(systemName: "chevron.down")
                    .font(.system(size: 10))
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(.systemGray6))
            .cornerRadius(6)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview("排序按钮 - 筛选样式") {
    VStack(spacing: 20) {
        MyWorksSortButton(
            selectedOption: .constant(.createTime),
            showMenu: .constant(false)
        )

        MyWorksSortButton(
            selectedOption: .constant(.views),
            showMenu: .constant(false)
        )

        MyWorksSortButton(
            selectedOption: .constant(.likes),
            showMenu: .constant(false)
        )
    }
    .padding()
    .background(Color(.systemBackground))
}

// MARK: - FlowLayout

/// 简单的流式布局组件，支持自适应宽度的标签排列
struct FlowLayout: Layout {
    var spacing: CGFloat = 10

    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let rows = arrangeSubviews(proposal: proposal, subviews: subviews)
        let totalHeight = rows.reduce(0) { result, row in
            result + row.maxHeight + spacing
        } - spacing

        return CGSize(width: proposal.width ?? 0, height: max(0, totalHeight))
    }

    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let rows = arrangeSubviews(proposal: proposal, subviews: subviews)
        var y = bounds.minY

        for row in rows {
            var x = bounds.minX
            for subview in row.subviews {
                let size = subview.sizeThatFits(.unspecified)
                subview.place(at: CGPoint(x: x, y: y), proposal: ProposedViewSize(size))
                x += size.width + spacing
            }
            y += row.maxHeight + spacing
        }
    }

    private func arrangeSubviews(proposal: ProposedViewSize, subviews: Subviews) -> [Row] {
        var rows: [Row] = []
        var currentRow = Row()
        let maxWidth = proposal.width ?? .infinity

        for subview in subviews {
            let size = subview.sizeThatFits(.unspecified)

            if currentRow.width + size.width + spacing > maxWidth && !currentRow.subviews.isEmpty {
                rows.append(currentRow)
                currentRow = Row()
            }

            currentRow.add(subview: subview, size: size, spacing: spacing)
        }

        if !currentRow.subviews.isEmpty {
            rows.append(currentRow)
        }

        return rows
    }

    private struct Row {
        var subviews: [LayoutSubview] = []
        var width: CGFloat = 0
        var maxHeight: CGFloat = 0

        mutating func add(subview: LayoutSubview, size: CGSize, spacing: CGFloat) {
            if !subviews.isEmpty {
                width += spacing
            }
            subviews.append(subview)
            width += size.width
            maxHeight = max(maxHeight, size.height)
        }
    }
}
