import SwiftUI

/// 行程卡片右下角 info 按钮及气泡说明浮层
struct ItineraryInfoTipsOverlay: View {
    @Binding var showTips: Bool
    var body: some View {
        ZStack {
            // info按钮
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    Button(action: { withAnimation { showTips.toggle() } }) {
                        Image(systemName: "info.circle")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.gray)
                    }
                    .padding(.trailing, 18)
                    .padding(.bottom, 14)
                    .accessibilityLabel("查看说明")
                    .zIndex(2)
                }
            }
            // tips气泡
            ZStack {
                Color.clear
                Color.black.opacity(0.01)
                    .ignoresSafeArea()
                    .opacity(showTips ? 1 : 0)
                    .onTapGesture { withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) { showTips = false } }
                VStack(alignment: .leading, spacing: 12) {
                    HStack(spacing: 8) {
                        Image(systemName: "info.circle.fill")
                            .font(.system(size: 16))
                            .foregroundColor(.gray)
                        Text("流量预测和预计花费说明")
                            .font(.system(size: 13, weight: .semibold))
                    }
                    Text("• 流量预测：根据历史数据和节假日等因素，预测目的地在出行期间的游客流量，仅供参考。")
                        .font(.system(size: 13))
                    Text("• 预计花费：根据行程天数、目的地消费水平等估算，实际花费可能因个人选择有所不同。")
                        .font(.system(size: 13))
                }
                .foregroundColor(.secondary)
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 12, style: .continuous)
                        .fill(Color(.systemBackground))
                        .shadow(color: Color.black.opacity(0.10), radius: 8, x: 0, y: 4)
                )
                .frame(width: 240)
                .scaleEffect(showTips ? 1 : 0.95)
                .opacity(showTips ? 1 : 0)
                .offset(y: showTips ? 0 : 20)
                .animation(.spring(response: 0.4, dampingFraction: 0.7), value: showTips)
                .zIndex(10)
            }
        }
        .allowsHitTesting(true)
    }
} 