import Foundation
import UIKit

/// 探索旅程模块常量定义
enum ExploreJourneyConstants {
    
    // MARK: - UI 常量
    
    /// 界面布局常量
    ///
    /// 注意：基础的布局常量已迁移到 DesignSystemConstants
    /// 这里只保留模块专用的布局参数
    enum Layout {
        // MARK: - 模块专用布局常量

        /// 卡片高度
        static let cardHeight: CGFloat = 200
        /// 小卡片高度
        static let smallCardHeight: CGFloat = 120
        /// 大卡片高度
        static let largeCardHeight: CGFloat = 280
    }
    
    // 注意：基础颜色常量已移除，请直接使用系统语义颜色：
    // - 主色调: Color.accentColor 或 UIColor.systemBlue
    // - 文本色: Color.primary 或 UIColor.label
    // - 背景色: Color(.systemBackground) 或 UIColor.systemBackground
    // - 状态色: Color.green 或 UIColor.systemGreen
    //
    // 如需业务特定颜色，请在此处添加
    

    
    // MARK: - 业务常量
    
    /// 搜索相关常量
    enum Search {
        /// 最小搜索字符数
        static let minimumQueryLength = 2
        /// 最大搜索字符数
        static let maximumQueryLength = 100

        // 注意：以下配置已迁移到全局配置：
        // - 搜索建议最大数量: AppConfig.Pagination.maxSuggestions
        // - 搜索历史最大数量: AppConfig.Pagination.maxSearchHistory
        // - 搜索防抖延迟: DesignSystemConstants.Interaction.debounceDelay
    }

    // 注意：分页相关常量已迁移到 AppConfig.Pagination
    // - 默认页面大小: AppConfig.Pagination.defaultPageSize
    // - 最大页面大小: AppConfig.Pagination.maxPageSize
    // - 预加载阈值: AppConfig.Pagination.preloadThreshold
    
    /// 位置相关常量
    enum Location {
        /// 默认搜索半径（公里）
        static let defaultSearchRadius: Double = 10.0
        /// 最大搜索半径（公里）
        static let maxSearchRadius: Double = 100.0
        /// 最小搜索半径（公里）
        static let minSearchRadius: Double = 1.0
        /// 位置精度阈值（米）
        static let accuracyThreshold: Double = 100.0
    }
    
    /// 缓存相关常量
    ///
    /// 注意：基础的缓存配置已迁移到 AppConfig.Cache
    /// 这里保留模块专用的缓存参数（如果有的话）
    enum Cache {
        // 当前没有模块专用的缓存参数
        // 所有缓存常量都已迁移到 AppConfig.Cache
    }
    
    /// 网络相关常量
    ///
    /// 注意：基础的网络配置已迁移到 AppConfig.Network
    /// 这里保留模块专用的网络参数（如果有的话）
    enum Network {
        // 当前没有模块专用的网络参数
        // 所有网络常量都已迁移到 AppConfig.Network
    }
    
    // MARK: - 动画常量
    
    /// 动画相关常量
    ///
    /// 注意：基础的动画常量已迁移到 DesignSystemConstants.Animation
    /// 这里保留模块专用的动画参数（如果有的话）
    enum Animation {
        // 当前没有模块专用的动画参数
        // 所有动画常量都已迁移到 DesignSystemConstants.Animation
    }

    /// 阴影相关常量
    enum Shadow {
        /// 阴影半径
        static let shadowRadius: CGFloat = 2
        /// 阴影偏移
        static let shadowOffset: CGFloat = 2
        /// 阴影透明度
        static let shadowOpacity: Double = 0.05
    }
    
    // MARK: - 图标常量
    
    /// SF Symbols 图标名称
    enum Icons {
        /// 搜索
        static let search = "magnifyingglass"
        /// 位置
        static let location = "location"
        /// 地图
        static let map = "map"
        /// 筛选
        static let filter = "line.3.horizontal.decrease.circle"
        /// 排序
        static let sort = "arrow.up.arrow.down"
        /// 收藏
        static let favorite = "heart"
        /// 已收藏
        static let favorited = "heart.fill"
        /// 分享
        static let share = "square.and.arrow.up"
        /// 更多
        static let more = "ellipsis"
        /// 返回
        static let back = "chevron.left"
        /// 关闭
        static let close = "xmark"
        /// 刷新
        static let refresh = "arrow.clockwise"
        /// 设置
        static let settings = "gearshape"
        /// 用户
        static let user = "person.circle"
        /// 星级评分
        static let star = "star"
        /// 已评分
        static let starFilled = "star.fill"
        /// 时间
        static let time = "clock"
        /// 距离
        static let distance = "location.circle"
        /// 价格
        static let price = "dollarsign.circle"
        /// 分类
        static let category = "square.grid.2x2"
        /// 趋势
        static let trending = "chart.line.uptrend.xyaxis"
        /// 推荐
        static let featured = "crown"
        /// 附近
        static let nearby = "location.magnifyingglass"
    }
    
    // MARK: - 文本常量
    
    /// 界面文本
    enum Text {
        /// 标题
        static let exploreTitle = "探索旅程"
        static let discoverTitle = "发现"
        static let nearbyTitle = "附近"
        static let searchTitle = "搜索"
        static let filtersTitle = "筛选"
        static let preferencesTitle = "偏好设置"
        
        /// 占位符文本
        static let searchPlaceholder = "搜索目的地、景点..."
        static let noResultsPlaceholder = "暂无搜索结果"
        static let loadingPlaceholder = "加载中..."
        static let errorPlaceholder = "加载失败，请重试"
        
        /// 按钮文本
        static let searchButton = "搜索"
        static let filterButton = "筛选"
        static let clearButton = "清除"
        static let applyButton = "应用"
        static let cancelButton = "取消"
        static let retryButton = "重试"
        static let refreshButton = "刷新"
        
        /// 状态文本
        static let loadingText = "正在加载..."
        static let noDataText = "暂无数据"
        static let networkErrorText = "网络连接失败"
        static let locationErrorText = "无法获取位置信息"
        
        /// 单位文本
        static let kilometerUnit = "公里"
        static let meterUnit = "米"
        static let minuteUnit = "分钟"
        static let hourUnit = "小时"
    }
}

// MARK: - 扩展方法

extension ExploreJourneyConstants {
    
    /// 格式化距离显示
    static func formatDistance(_ distance: Double) -> String {
        if distance < 1.0 {
            return String(format: "%.0f%@", distance * 1000, Text.meterUnit)
        } else {
            return String(format: "%.1f%@", distance, Text.kilometerUnit)
        }
    }
    
    /// 格式化时间显示
    static func formatDuration(_ minutes: Int) -> String {
        if minutes < 60 {
            return "\(minutes)\(Text.minuteUnit)"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours)\(Text.hourUnit)"
            } else {
                return "\(hours)\(Text.hourUnit)\(remainingMinutes)\(Text.minuteUnit)"
            }
        }
    }
    
    /// 格式化评分显示
    static func formatRating(_ rating: Double) -> String {
        return String(format: "%.1f", rating)
    }
}
