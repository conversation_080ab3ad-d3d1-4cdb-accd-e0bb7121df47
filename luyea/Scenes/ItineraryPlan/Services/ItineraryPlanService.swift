import Foundation
import Combine

/// 行程计划服务
///
/// 负责处理行程计划模块的所有业务逻辑，包括数据获取、缓存管理、业务规则处理等。
/// 遵循Service层设计原则，为ViewModel提供高级业务接口。
protocol ItineraryPlanServiceProtocol {
    /// 获取行程列表
    func fetchItineraries() async throws -> [ItineraryModel]

    /// 获取足迹列表
    func fetchFootprints() async throws -> [FootprintModel]

    /// 搜索行程
    func searchItineraries(query: String) async throws -> [ItineraryModel]

    /// 刷新所有数据
    func refreshAllData() async throws -> (itineraries: [ItineraryModel], footprints: [FootprintModel])

    /// 预加载行程封面图片
    func preloadItineraryCoverImages(_ itineraries: [ItineraryModel]) async

    /// 根据状态筛选行程
    func filterItineraries(_ itineraries: [ItineraryModel], by filter: ItineraryFilter) -> [ItineraryModel]

    /// 根据搜索关键词筛选行程
    func filterItineraries(_ itineraries: [ItineraryModel], by searchText: String) -> [ItineraryModel]

    /// 对行程进行排序
    func sortItineraries(_ itineraries: [ItineraryModel]) -> [ItineraryModel]
}

/// 行程计划服务实现
final class ItineraryPlanService: ItineraryPlanServiceProtocol {
    
    // MARK: - Dependencies
    
    /// 网络服务
    private let networkService: NetworkServiceProtocol
    
    /// 图片预加载服务
    private let imagePreloadService: ImagePreloadService
    
    // MARK: - Cache Properties
    
    /// 行程数据缓存
    private var itinerariesCache: [ItineraryModel] = []
    
    /// 足迹数据缓存
    private var footprintsCache: [FootprintModel] = []
    
    /// 缓存时间戳
    private var cacheTimestamp: Date?
    
    /// 缓存有效期（秒）
    private let cacheValidityDuration: TimeInterval = 300 // 5分钟
    
    // MARK: - Initialization
    
    init(
        networkService: NetworkServiceProtocol = NetworkService.shared,
        imagePreloadService: ImagePreloadService = ImagePreloadService.shared
    ) {
        self.networkService = networkService
        self.imagePreloadService = imagePreloadService
    }
    
    // MARK: - Public Methods
    
    /// 获取行程列表
    func fetchItineraries() async throws -> [ItineraryModel] {
        // 检查缓存是否有效
        if let cachedData = getCachedItineraries() {
            return cachedData
        }
        
        // 从网络获取数据
        let itineraries: [ItineraryModel] = try await networkService.request(
            .get(APIPaths.itineraryList)
        )
        
        // 更新缓存
        updateItinerariesCache(itineraries)
        
        // 异步预加载图片
        Task {
            await preloadItineraryCoverImages(itineraries)
        }
        
        return itineraries
    }
    
    /// 获取足迹列表
    func fetchFootprints() async throws -> [FootprintModel] {
        // 检查缓存是否有效
        if let cachedData = getCachedFootprints() {
            return cachedData
        }
        
        // 从网络获取数据
        let footprints: [FootprintModel] = try await networkService.request(
            .get(APIPaths.footprintList)
        )
        
        // 更新缓存
        updateFootprintsCache(footprints)
        
        return footprints
    }
    
    /// 搜索行程
    func searchItineraries(query: String) async throws -> [ItineraryModel] {
        let request = APIRequest
            .get(APIPaths.itineraryList)
            .query(["q": query])

        let results: [ItineraryModel] = try await networkService.request(request)
        return results
    }
    
    /// 刷新所有数据
    func refreshAllData() async throws -> (itineraries: [ItineraryModel], footprints: [FootprintModel]) {
        // 清除缓存，强制从网络获取
        clearCache()
        
        // 并行获取数据
        async let itinerariesTask = fetchItineraries()
        async let footprintsTask = fetchFootprints()
        
        let itineraries = try await itinerariesTask
        let footprints = try await footprintsTask
        
        return (itineraries: itineraries, footprints: footprints)
    }
    
    /// 预加载行程封面图片
    func preloadItineraryCoverImages(_ itineraries: [ItineraryModel]) async {
        // 只预加载前3张图片，避免过度预加载
        let imagesToPreload = Array(itineraries.prefix(3))
            .compactMap { $0.coverImage }
        
        if !imagesToPreload.isEmpty {
            await imagePreloadService.preloadImages(urls: imagesToPreload, priority: .background)
        }
    }
    
    // MARK: - Private Methods - Cache Management
    
    /// 获取缓存的行程数据
    private func getCachedItineraries() -> [ItineraryModel]? {
        guard isCacheValid() else { return nil }
        return itinerariesCache.isEmpty ? nil : itinerariesCache
    }
    
    /// 获取缓存的足迹数据
    private func getCachedFootprints() -> [FootprintModel]? {
        guard isCacheValid() else { return nil }
        return footprintsCache.isEmpty ? nil : footprintsCache
    }
    
    /// 检查缓存是否有效
    private func isCacheValid() -> Bool {
        guard let timestamp = cacheTimestamp else { return false }
        return Date().timeIntervalSince(timestamp) < cacheValidityDuration
    }
    
    /// 更新行程缓存
    private func updateItinerariesCache(_ itineraries: [ItineraryModel]) {
        itinerariesCache = itineraries
        cacheTimestamp = Date()
    }
    
    /// 更新足迹缓存
    private func updateFootprintsCache(_ footprints: [FootprintModel]) {
        footprintsCache = footprints
        if cacheTimestamp == nil {
            cacheTimestamp = Date()
        }
    }
    
    /// 清除缓存
    private func clearCache() {
        itinerariesCache.removeAll()
        footprintsCache.removeAll()
        cacheTimestamp = nil
    }
}

// MARK: - Business Logic Extensions

extension ItineraryPlanService {
    
    /// 根据状态筛选行程
    func filterItineraries(_ itineraries: [ItineraryModel], by filter: ItineraryFilter) -> [ItineraryModel] {
        switch filter {
        case .all:
            return itineraries
        case .inProgress:
            return itineraries.filter { $0.status == "在途中" }
        case .upcoming:
            return itineraries.filter { $0.status == "待出行" }
        case .completed:
            return itineraries.filter { $0.status == "已结束" }
        }
    }
    
    /// 根据搜索关键词筛选行程
    func filterItineraries(_ itineraries: [ItineraryModel], by searchText: String) -> [ItineraryModel] {
        guard !searchText.isEmpty else { return itineraries }
        
        return itineraries.filter { itinerary in
            itinerary.title.localizedCaseInsensitiveContains(searchText) ||
            itinerary.destination.localizedCaseInsensitiveContains(searchText)
        }
    }
    
    /// 对行程进行排序
    func sortItineraries(_ itineraries: [ItineraryModel]) -> [ItineraryModel] {
        return itineraries.sorted { lhs, rhs in
            let orderL = statusOrder(for: lhs.status)
            let orderR = statusOrder(for: rhs.status)
            if orderL != orderR {
                return orderL < orderR
            } else {
                return lhs.dateRange < rhs.dateRange
            }
        }
    }
    
    /// 定义行程状态的排序优先级
    private func statusOrder(for status: String) -> Int {
        switch status {
        case "在途中": return 0
        case "待出行": return 1
        case "已结束": return 2
        default: return 3
        }
    }
}
