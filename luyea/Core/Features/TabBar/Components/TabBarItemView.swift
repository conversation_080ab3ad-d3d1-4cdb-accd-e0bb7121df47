import SwiftUI

/// TabBar单项视图，无状态组件
struct TabBarItemView: View {
    let tab: AppTab
    let isSelected: Bool
    var indicatorNamespace: Namespace.ID
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 2) {
            ZStack {
                Image(systemName: tab.iconName)
                    .font(.system(size: 22, weight: .semibold))
                    .scaleEffect(isSelected ? 1.18 : 1.0)
                    .foregroundColor(isSelected ? .accentColor : .gray)
                    .animation(.spring(response: 0.35, dampingFraction: 0.7), value: isSelected)
            }
            Text(tab.title)
                .font(.caption)
                .fontWeight(isSelected ? .semibold : .regular)
                .foregroundColor(isSelected ? .accentColor : .gray)
            ZStack {
                if isSelected {
                    Capsule()
                        .fill(LinearGradient(gradient: Gradient(colors: [Color.accentColor, Color.accentColor.opacity(0.7)]), startPoint: .leading, endPoint: .trailing))
                        .frame(height: 3)
                        .matchedGeometryEffect(id: "indicator", in: indicatorNamespace)
                        .padding(.horizontal, 12)
                        .transition(.scale)
                } else {
                    Color.clear.frame(height: 3).padding(.horizontal, 12)
                }
            }
        }
        .padding(.vertical, 0)
        .frame(maxWidth: .infinity)
        .contentShape(Rectangle())
    }
} 