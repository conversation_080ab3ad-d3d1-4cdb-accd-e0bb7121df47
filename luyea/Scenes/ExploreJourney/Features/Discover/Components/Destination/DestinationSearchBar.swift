import SwiftUI

struct DestinationSearchBar: View {
    @Binding var text: String

    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(Color(.systemGray3))
            TextField("搜索目的地城市、省份或国家", text: $text)
                .font(.system(size: 15))
                .foregroundColor(.primary)
        }
        .padding(10)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
} 