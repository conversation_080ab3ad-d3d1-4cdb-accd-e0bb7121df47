import Foundation
import SwiftUI

/// Authentication功能专用常量
enum AuthenticationConstants {
    

    
    // MARK: - 验证规则
    
    enum Validation {
        static let phoneNumberPattern = "^1[3-9]\\d{9}$"
        static let verificationCodeLength = 6
        static let codeExpirationTime: TimeInterval = 300 // 5分钟
        static let codeCooldownTime: TimeInterval = 60 // 1分钟
    }
    
    // MARK: - 文本内容
    
    enum Text {
        // 标题和提示
        static let loginTitle = "欢迎使用路亦"
        static let loginSubtitle = "计划每一步，享受每一程"
        
        // 输入框提示
        static let phoneNumberPlaceholder = "请输入手机号"
        static let verificationCodePlaceholder = "请输入验证码"
        
        // 按钮文本
        static let sendCodeButtonTitle = "获取验证码"
        static let resendCodeButtonTitle = "重新发送"
        static let loginButtonTitle = "登录"
        static let wechatLoginTitle = "微信登录"
        static let appleLoginTitle = "通过 Apple 登录"
        
        // 状态提示
        static let codeSentSuccess = "验证码已发送"
        static let loginSuccess = "登录成功"
        static let loginFailedError = "登录失败，请重试"
        
        // 协议相关
        static let agreementPrefix = "登录即表示同意"
        static let userAgreement = "《用户协议》"
        static let agreementConnector = "和"
        static let privacyPolicy = "《隐私政策》"
        
        // 第三方登录
        static let thirdPartyLoginHint = "其他登录方式"
        
        // 错误提示
        static let phoneNumberError = "请输入正确的手机号码"
        static let verificationCodeError = "请输入6位验证码"
        static let agreementError = "请先同意用户协议和隐私政策"
    }
    
    // MARK: - 布局尺寸
    
    ///
    /// 注意：基础的布局常量已迁移到 DesignSystemConstants
    /// 这里只保留模块专用的布局参数
    enum Layout {
        // MARK: - 模块专用布局常量

        static let logoSize: CGFloat = 80
        static let thirdPartyButtonSize: CGFloat = 44
        static let borderWidth: CGFloat = 1
    }
    
    // MARK: - 颜色主题

    enum Colors {
        // 业务特定颜色（保留）
        static let wechatGreen = Color(red: 0.09, green: 0.70, blue: 0.27)
        static let appleBlack = Color.primary // 使用系统主色调，自动适配深色模式

        // 注意：基础颜色常量已移除，请直接使用系统语义颜色：
        // - 主色调: Color.accentColor
        // - 主要文本: Color.primary
        // - 次要文本: Color.secondary
        // - 占位符文本: Color(.placeholderText)
        // - 输入框背景: Color(.systemGray6)
        // - 输入框边框: Color(.systemGray4)
        // - 按钮背景: Color.accentColor
        // - 按钮文本: Color.white
        // - 卡片背景: Color(.systemBackground)
        // - 错误色: Color.red
        // - 成功色: Color.green
    }
    
    // MARK: - 字体样式

    enum Fonts {
        // 业务特定字体样式（保留）
        static let title = Font.largeTitle.weight(.bold)

        // 注意：基础字体常量已移除，请直接使用系统字体：
        // - 副标题: Font.body
        // - 按钮文本: Font.body.weight(.medium)
        // - 输入框文本: Font.body
        // - 说明文字: Font.caption
        // - 小说明文字: Font.caption2
    }
    
    // MARK: - 动画参数
    ///
    /// 注意：基础的动画常量已迁移到 DesignSystemConstants.Animation
    /// 这里只保留模块专用的动画参数
    enum Animation {
        // MARK: - 模块专用动画参数

        static let buttonScale: CGFloat = 0.95
    }
    
    // MARK: - 图标名称
    
    enum Icons {
        static let phone = "phone"
        static let lock = "lock"
        static let eye = "eye"
        static let eyeSlash = "eye.slash"
        static let checkmark = "checkmark"
        static let xmark = "xmark"
        static let wechat = "message.circle.fill"
        static let apple = "apple.logo"
        static let loading = "arrow.2.circlepath"
    }
}
