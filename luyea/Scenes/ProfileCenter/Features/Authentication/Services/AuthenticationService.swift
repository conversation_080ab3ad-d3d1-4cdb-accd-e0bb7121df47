import Foundation
import Combine
import UIKit

/// Authentication功能专用服务
/// 
/// 职责：
/// - 处理用户认证相关的业务逻辑
/// - 管理验证码发送和验证
/// - 处理第三方登录集成
/// - 与全局NetworkService和KeychainService协作
@MainActor
class AuthenticationService: ObservableObject {

    // MARK: - Singleton

    static let shared = AuthenticationService()

    // MARK: - Dependencies

    private let networkService = NetworkService.shared
    private let keychainService = KeychainService.shared

    // MARK: - Published Properties

    @Published var currentUser: AuthenticationModels.User?
    @Published var authState: AuthenticationModels.AuthState = .unauthenticated

    // MARK: - Private Properties

    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    private init() {
        loadStoredUser()
    }
    
    // MARK: - Public Methods
    
    /// 发送验证码
    /// - Parameter phoneNumber: 手机号码
    /// - Returns: 验证码发送结果
    func sendVerificationCode(to phoneNumber: String) async throws -> AuthenticationModels.VerificationCodeResponse {
        guard phoneNumber.isValidPhoneNumber else {
            throw AuthenticationModels.AuthError.invalidPhoneNumber
        }
        
        let request = AuthenticationModels.VerificationCodeRequest(
            phoneNumber: phoneNumber,
            type: .login
        )
        
        do {
            let api = APIRequest(
                path: APIPaths.authSendCode,
                method: "POST",
                body: try JSONEncoder().encode(request)
            )
            
            let response: AuthenticationModels.VerificationCodeResponse = try await networkService.request(api)
            
            Log.info("✅ [AuthenticationService] 验证码发送成功: \(phoneNumber.maskedPhoneNumber)")
            return response
            
        } catch {
            Log.error("❌ [AuthenticationService] 验证码发送失败: \(error)")
            throw AuthenticationModels.AuthError.codeSendFailed
        }
    }
    
    /// 手机号登录
    /// - Parameters:
    ///   - phoneNumber: 手机号码
    ///   - verificationCode: 验证码
    /// - Returns: 登录响应
    func loginWithPhoneNumber(_ phoneNumber: String, verificationCode: String) async throws -> AuthenticationModels.LoginResponse {
        guard phoneNumber.isValidPhoneNumber else {
            throw AuthenticationModels.AuthError.invalidPhoneNumber
        }
        
        guard verificationCode.isValidVerificationCode else {
            throw AuthenticationModels.AuthError.invalidVerificationCode
        }
        
        authState = .authenticating
        
        do {
            let request = AuthenticationModels.LoginRequest(
                phoneNumber: phoneNumber,
                verificationCode: verificationCode,
                deviceId: UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
            )
            
            let api = APIRequest(
                path: APIPaths.authLogin,
                method: "POST",
                body: try JSONEncoder().encode(request)
            )
            
            let response: AuthenticationModels.LoginResponse = try await networkService.request(api)
            
            // 保存用户会话
            await saveUserSession(response)
            
            // 更新状态
            authState = .authenticated(response.user)
            currentUser = response.user
            
            Log.info("✅ [AuthenticationService] 登录成功: \(phoneNumber.maskedPhoneNumber)")
            return response
            
        } catch {
            Log.error("❌ [AuthenticationService] 登录失败: \(error)")
            authState = .error(.loginFailed(error.localizedDescription))
            throw mapNetworkErrorToAuthError(error)
        }
    }
    
    /// 微信登录
    /// 通过微信SDK进行登录认证
    /// - Returns: 登录响应
    func loginWithWeChat() async throws -> AuthenticationModels.LoginResponse {
        authState = .authenticating

        // TODO: 集成微信SDK登录
        // 1. 调用微信SDK进行授权登录
        // 2. 获取微信用户信息
        // 3. 将微信用户信息转换为应用用户模型
        // 4. 保存用户会话信息

        Log.warning("⚠️ [AuthenticationService] 微信登录功能待实现")
        authState = .error(.loginFailed("微信登录功能开发中"))
        throw AuthenticationModels.AuthError.loginFailed("微信登录功能开发中")
    }
    
    /// Apple登录
    /// 通过Apple Sign In SDK进行登录认证
    /// - Returns: 登录响应
    func loginWithApple() async throws -> AuthenticationModels.LoginResponse {
        authState = .authenticating

        // TODO: 集成Apple Sign In SDK登录
        // 1. 导入 AuthenticationServices 框架
        // 2. 创建 ASAuthorizationAppleIDProvider
        // 3. 配置授权请求 (requestedScopes: [.fullName, .email])
        // 4. 处理授权回调，获取用户信息
        // 5. 将Apple用户信息转换为应用用户模型
        // 6. 保存用户会话信息

        Log.warning("⚠️ [AuthenticationService] Apple登录功能待实现")
        authState = .error(.loginFailed("Apple登录功能开发中"))
        throw AuthenticationModels.AuthError.loginFailed("Apple登录功能开发中")
    }
    
    /// 登出
    func logout() async {
        Log.info("🔐 [AuthenticationService] Starting logout process")

        // 清除本地存储
        let tokenDeleted = keychainService.deleteAccessToken()
        let refreshTokenDeleted = keychainService.deleteRefreshToken()
        let userDataDeleted = keychainService.deleteUserData()

        Log.info("🔐 [AuthenticationService] Keychain cleanup - token: \(tokenDeleted), refresh: \(refreshTokenDeleted), user: \(userDataDeleted)")

        authState = .unauthenticated
        currentUser = nil

        Log.info("✅ [AuthenticationService] 用户已登出，状态已更新为: \(authState)")
    }
    
    /// 检查登录状态
    var isAuthenticated: Bool {
        if case .authenticated = authState {
            return true
        }
        return false
    }
    
    // MARK: - Private Methods
    
    /// 保存用户会话信息
    private func saveUserSession(_ response: AuthenticationModels.LoginResponse) async {
        // 保存令牌
        _ = keychainService.saveAccessToken(response.accessToken)
        _ = keychainService.saveRefreshToken(response.refreshToken)

        // 保存用户信息
        _ = keychainService.saveUserData(response.user)
    }
    
    /// 加载存储的用户信息
    private func loadStoredUser() {
        guard let user = keychainService.getUserData(type: AuthenticationModels.User.self),
              keychainService.getAccessToken() != nil else {
            authState = .unauthenticated
            return
        }
        
        authState = .authenticated(user)
        currentUser = user
        Log.info("✅ [AuthenticationService] 已加载存储的用户信息")
    }
    
    /// 将网络错误映射为认证错误
    private func mapNetworkErrorToAuthError(_ error: Error) -> AuthenticationModels.AuthError {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .noInternetConnection:
                return .networkError
            case .decodingError:
                return .unknownError
            case .serverError(let statusCode):
                switch statusCode {
                case 400:
                    return .invalidVerificationCode
                case 401:
                    return .loginFailed("验证码错误或已过期")
                case 404:
                    return .loginFailed("用户不存在")
                default:
                    return .loginFailed("服务器错误")
                }
            default:
                return .unknownError
            }
        }
        return .unknownError
    }
}
