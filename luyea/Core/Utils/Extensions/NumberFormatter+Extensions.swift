import Foundation

// MARK: - Int 扩展（数字格式化）
extension Int {
    /// 格式化数字显示
    /// - Returns: 格式化后的字符串
    /// - 例如：1234 -> "1.2k", 12345 -> "1.2万"
    func formatCount() -> String {
        if self >= 10000 {
            let value = Double(self) / 10000.0
            return String(format: "%.1f万", value)
        } else if self >= 1000 {
            let value = Double(self) / 1000.0
            return String(format: "%.1fk", value)
        } else {
            return "\(self)"
        }
    }
}

// MARK: - Double 扩展（数字格式化）
extension Double {
    /// 格式化数字显示
    /// - Returns: 格式化后的字符串
    /// - 例如：1234.5 -> "1.2k", 12345.6 -> "1.2万"
    func formatCount() -> String {
        if self >= 10000 {
            let value = self / 10000.0
            return String(format: "%.1f万", value)
        } else if self >= 1000 {
            let value = self / 1000.0
            return String(format: "%.1fk", value)
        } else {
            return String(format: "%.0f", self)
        }
    }
} 